import * as React from 'react';
import * as PropTypes from 'prop-types';
import Popover from '@roo/roo/core/Popover';
// http://dev.sankuai.com/code/repo-detail/~LIUWEI83/react-color/file/list?codeArea=bj
import { SketchPicker } from '@roo/react-color';
import { isFunction, isArray } from '@utiljs/is';
import { GlobalConfigContext } from '@roo/roo/ConfigProvider';
import { $lineColor, $linkColor } from '../_utils/ThemeColor';
import { ColorPickerProps, ColorState, RGBColor, SketchPickerProps } from './interface';

const defaultStyle = {
    width: '36px',
    height: '36px',
    border: `1px solid ${$lineColor}`,
    borderRadius: '2px',
};

// 根据size定义不同尺寸
const sizeStyles = {
    small: {
        width: '32px',
        height: '32px',
    },
    normal: {
        width: '36px',
        height: '36px',
    },
    large: {
        width: '40px',
        height: '40px',
    },
};

type SS = {
    color: ColorState | string;
};

const defaultColor = $linkColor;

const checkColorType = (color: ColorState | string | undefined, defaultValue?: ColorState | string) =>
    !color ? defaultValue || defaultColor : color;

const checkPresetColors = (presetColors: any[]) => {
    if (isArray(presetColors)) {
        return presetColors.filter(color => color !== null && color !== undefined);
    } else {
        return [];
    }
};

const DefaultColorPicker = (props: SketchPickerProps) => (
    <SketchPicker
        {...props}
        color={checkColorType(props?.color as string | ColorState)}
        presetColors={checkPresetColors(props?.presetColors as [])}
    />
);

class ColorPicker extends React.Component<ColorPickerProps, SS> {
    static contextType?: React.Context<any> | undefined = GlobalConfigContext;

    static ColorPanel = DefaultColorPicker;

    static propTypes = {
        onChange: PropTypes.func,
        onChangeComplete: PropTypes.func,
        presetColors: PropTypes.array,
        value: PropTypes.string,
        defaultValue: PropTypes.string,
        className: PropTypes.string,
        trigger: PropTypes.string,
        visible: PropTypes.bool,
        defaultVisible: PropTypes.bool,
        disableAlpha: PropTypes.bool,
        disabled: PropTypes.bool,
        disablePortal: PropTypes.bool,
        placement: PropTypes.string,
        onVisibleChange: PropTypes.func,
        popupContainer: PropTypes.oneOfType([PropTypes.instanceOf(Element), PropTypes.func]),
        destroyOnHidden: PropTypes.bool,
        size: PropTypes.oneOf(['small', 'normal', 'large']),
    };

    static defaultProps = {
        onChange: () => {},
        onChangeComplete: () => {},
        presetColors: [],
        defaultVisible: false,
        disableAlpha: false,
        disablePortal: false,
        visible: undefined,
        value: undefined,
        className: '',
        trigger: 'click',
        disabled: false,
        placement: 'bottomLeft',
        defaultValue: defaultColor,
        popupContainer: undefined,
        onVisibleChange: undefined,
        destroyOnHidden: true,
        size: 'normal',
    };

    static getDerivedStateFromProps(nextProps: any, preState: any) {
        const { value } = nextProps;

        if (value !== undefined && value !== preState.color) {
            return {
                color: checkColorType(nextProps.value, nextProps.defaultValue),
            };
        }
        return null;
    }

    // eslint-disable-next-line
    state = {
        color: checkColorType(this.props.defaultValue, this.props.defaultValue),
    };

    rgba = (color: RGBColor) => (color ? `rgba(${color.r}, ${color.g}, ${color.b}, ${color.a ?? 1})` : defaultColor);

    handleChange = (e: ColorState) => {
        this.handleColor(e);
        this.handlePC(this.rgba(e.rgb), e);
    };

    handlePC = (a: string, b: ColorState) => {
        if (isFunction(this.props.onChange)) {
            this.props.onChange(a, b);
        }
    };

    handleColor = (e: ColorState) => {
        this.setState({
            color: e,
        });
    };

    render() {
        const {
            presetColors,
            defaultVisible,
            visible,
            children,
            className,
            popupClassName,
            trigger,
            placement,
            disabled,
            disablePortal,
            popupContainer,
            onVisibleChange,
            disableAlpha,
            onChangeComplete,
            destroyOnHidden,
            size,
        } = this.props;
        const color =
            typeof this.state.color === 'string'
                ? this.state.color
                : (this.state.color as ColorState).rgb || defaultColor;
        const ss = {
            ...defaultStyle,
            ...sizeStyles[size || 'normal'],
            background: typeof color === 'string' ? color : this.rgba(color),
        };
        return (
            <Popover
                defaultVisible={defaultVisible}
                visible={visible}
                placement={placement}
                trigger={trigger}
                useClone
                lazy={!destroyOnHidden}
                flip="flip"
                onVisibleChange={onVisibleChange}
                container={popupContainer}
                disabled={disabled}
                disablePortal={disablePortal}
                content={
                    <div className={`${this.context.prefixCls}-color-picker-wrap`}>
                        <SketchPicker
                            className={popupClassName}
                            color={color}
                            disableAlpha={disableAlpha}
                            onChange={this.handleChange}
                            presetColors={checkPresetColors(presetColors as [])}
                            onChangeComplete={onChangeComplete}
                        />
                    </div>
                }
            >
                {isFunction(children)
                    ? children(this.state.color)
                    : children || (
                        <div
                            className={`${this.context.prefixCls}-color-picker-display ${className}`}
                            style={ss}
                          // eslint-disable-next-line react/jsx-closing-bracket-location
                          />
                    )}
            </Popover>
        );
    }
}

export default ColorPicker;
