/* eslint-disable no-nested-ternary */
import React, { PureComponent, RefObject } from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import { polyfill } from 'react-lifecycles-compat';
import memoize from 'memoize-one';
import Empty from '@roo/roo/core/Empty';
import locale from '@roo/roo/locale';
import Input from '@roo/roo/Input';
import Icon from '@roo/roo/Icon';
import Checkbox from '@roo/roo/CheckBox';
import { Manager, Reference, PopperPortal } from '@roo/roo/core/Popper';
import { createRef } from '@roo/create-react-ref';
import { isFunction, isArray, isBoolean, isNull } from '@utiljs/is';
import uniqBy from 'lodash/uniqBy';
import { GlobalConfigContext } from '@roo/roo/ConfigProvider';
import ReactResizeDetector from '../_utils/ReactResizeDetector';
import { placementEnum, placementTypes } from '../_utils/types';
import '../_utils/polyfill';
import VirtualList from '../_utils/VirtualList';
import withDisabled from '../_utils/hoc/withDisabled';

import {
    PrimitiveValue,
    SelectorOption,
    SelectorProps,
    SelectorState,
    FilterFunc,
    SortFunc,
    FieldNameMap,
    NormalOption,
    SelectRawOption,
} from './interface';

const reactVersion = React.version;

const defaultFieldNames = Object.freeze({
    title: 'title',
    children: 'children',
    value: 'value',
    label: 'label',
    disabled: 'disabled',
});

const sizeMap = {
    large: 'lg',
    normal: 'md',
    small: 'sm',
    mini: 'xs',
    compact: 'compact',
};

function filterOptions(
    options: SelectorOption[],
    filterable: boolean,
    filterMethod: FilterFunc | undefined | null | boolean,
    query: string,
    fieldNames: FieldNameMap,
    tagOptions: SelectorOption[],
    tags: boolean,
    filterSort: SortFunc | undefined | null,
    optionFilterProp: string | undefined,
): SelectRawOption[] {
    const ret = [];
    let _options = [];
    // tags模式下，options需要拼接输入的tag数据
    if (tags) {
        _options = options.concat(tagOptions);
        _options = uniqBy(_options, `${[fieldNames.value || defaultFieldNames.value]}`);
    } else {
        _options = options;
    }
    // 排序
    if (isFunction(filterSort)) {
        _options.sort(filterSort);
    }
    if (_options) {
        for (let i = 0; i < _options.length; ++i) {
            const raw = _options[i];
            const opt = {
                value: raw[fieldNames.value || 'value'],
                label: raw[fieldNames.label || 'label'],
                disabled: !!raw[fieldNames.disabled || 'disabled'],
                title: raw[fieldNames.title || 'title'],
                className: raw[fieldNames.className || 'className'],
            };
            if (filterable && query) {
                if (isFunction(filterMethod) && filterMethod(query, opt, raw)) {
                    // filterMethod函数如果返回true，则返回此项
                    ret.push({
                        raw,
                        opt,
                    });
                } else if (isBoolean(filterMethod) && filterMethod) {
                    // filterMethod是true，没有提供自定义的过滤方法，则默认按照label过滤出此值
                    if (optionFilterProp) {
                        if (String(raw[optionFilterProp]).toLowerCase().includes(query?.toLowerCase())) {
                            ret.push({
                                raw,
                                opt,
                            });
                        }
                    } else  if (String(opt?.label).toLowerCase().includes(query?.toLowerCase()) || String(opt?.value).toLowerCase().includes(query?.toLowerCase())) {
                        ret.push({
                            raw,
                            opt,
                        });
                    }
                } else if (!filterMethod) {
                    // filterable 是true，但是没有写对应的搜索方法，则默认返回
                    ret.push({
                        raw,
                        opt,
                    });
                }
            } else {
                ret.push({
                    raw,
                    opt,
                });
            }
        }
    }
    return ret;
}

function filterOptionGroups(
    groups: SelectorOption[],
    filterable: boolean,
    filterMethod: FilterFunc | undefined | null | boolean,
    query: string,
    fieldNames: FieldNameMap,
    filterSort: SortFunc | undefined | null,
    optionFilterProp: string | undefined,
): { title: string; children: SelectRawOption[] }[] {
    const ret = [];

    for (let i = 0; i < groups.length; ++i) {
        const group = groups[i];
        const title = group[fieldNames.title || 'title'] || '';
        const children = group[fieldNames.children || 'children'] || [];
        // 排序
        if (isFunction(filterSort)) {
            children.sort(filterSort);
        }
        const subList: SelectRawOption[] = [];

        for (let j = 0; j < children.length; ++j) {
            const raw = children[j];
            const opt = {
                value: raw[fieldNames.value || 'value'],
                label: raw[fieldNames.label || 'label'],
                disabled: !!raw[fieldNames.disabled || 'disabled'],
                title: raw[fieldNames.title || 'title'],
                className: raw[fieldNames.className || 'className'],
            };
            if (filterable && query) {
                if (isFunction(filterMethod) && filterMethod(query, opt, raw)) {
                    subList.push({
                        raw,
                        opt,
                    });
                } else if (isBoolean(filterMethod) && filterMethod) {
                    if (optionFilterProp) {
                        if (String(raw[optionFilterProp]).toLowerCase().includes(query?.toLowerCase())) {
                            subList.push({
                                raw,
                                opt,
                            });
                        }
                    } else if (String(opt?.label).toLowerCase().includes(query?.toLowerCase()) || String(opt?.value).toLowerCase().includes(query?.toLowerCase())) {
                        subList.push({
                            raw,
                            opt,
                        });
                    }
                } else if (!filterMethod) {
                    subList.push({
                        raw,
                        opt,
                    });
                }
            } else {
                subList.push({
                    raw,
                    opt,
                });
            }
        }

        if (subList.length > 0) {
            ret.push({
                title,
                children: subList,
            });
        }
    }

    return ret;
}

function isDef(value: any): boolean {
    return value !== undefined && value !== null;
}

const CursorHoverClass = 'hover';

export class Selector extends PureComponent<SelectorProps, SelectorState> {
    static contextType = GlobalConfigContext;
    context!: React.ContextType<typeof GlobalConfigContext>;

    static propTypes = {
        options: PropTypes.arrayOf(PropTypes.object),
        // value: PropTypes.oneOfType([
        //     PropTypes.string,
        //     PropTypes.number,
        //     PropTypes.object,
        //     PropTypes.bool,
        //     PropTypes.arrayOf(PropTypes.string),
        //     PropTypes.arrayOf(PropTypes.number),
        //     PropTypes.arrayOf(PropTypes.object),
        //     PropTypes.arrayOf(PropTypes.bool),
        // ]),
        disabled: PropTypes.bool,
        readOnly: PropTypes.bool,
        // loading: PropTypes.bool,
        placeholder: PropTypes.string,
        inputType: PropTypes.oneOf(['', 'line', 'plaintext']),
        // size: PropTypes.oneOf(['large', 'normal', 'small', 'mini', 'compact']),
        status: PropTypes.oneOf(['normal', 'success', 'error']),
        filterable: PropTypes.bool,
        filterMethod: PropTypes.oneOfType([PropTypes.bool, PropTypes.func]),
        filterSort: PropTypes.func,
        multiple: PropTypes.bool,
        maxTag: PropTypes.number,
        rawOption: PropTypes.bool,
        showArrow: PropTypes.bool,
        clearable: PropTypes.bool,
        grouped: PropTypes.bool,
        fieldNames: PropTypes.shape({
            title: PropTypes.string,
            children: PropTypes.string,
            value: PropTypes.string,
            label: PropTypes.string,
            disabled: PropTypes.string,
        }),
        defaultPopupVisible: PropTypes.bool,
        popupVisible: PropTypes.bool,
        disablePortal: PropTypes.bool,
        zIndex: PropTypes.number,

        optionRenderer: PropTypes.func,
        contentRenderer: PropTypes.func,
        multipleRenderer: PropTypes.func,

        suffixIcon: PropTypes.string,
        prefixIcon: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),

        onChange: PropTypes.func,
        onCompositionStart: PropTypes.func,
        onCompositionEnd: PropTypes.func,
        onClear: PropTypes.func,
        onSearch: PropTypes.func,
        onBlur: PropTypes.func,
        onFocus: PropTypes.func,
        onPopupVisibleChange: PropTypes.func,
        onSelect: PropTypes.func,
        onDeSelect: PropTypes.func,
        onPopupScroll: PropTypes.func,
        notFoundContent: PropTypes.node,
        placement: PropTypes.oneOf([
            'left',
            'right',
            'top',
            'bottom',
            'bottomLeft',
            'bottomRight',
            'topLeft',
            'topRight',
        ]),
        flip: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
        clearIcon: PropTypes.node,
        defaultValue: PropTypes.oneOfType([
            PropTypes.string,
            PropTypes.number,
            PropTypes.object,
            PropTypes.arrayOf(PropTypes.string),
            PropTypes.arrayOf(PropTypes.number),
            PropTypes.arrayOf(PropTypes.object),
        ]),
        removeIcon: PropTypes.node,
        onMouseEnter: PropTypes.func,
        onMouseLeave: PropTypes.func,
        dropdownRender: PropTypes.func,
        showSelectIcon: PropTypes.bool,
        isEnterSelect: PropTypes.bool,
        autoFocus: PropTypes.bool,
        bordered: PropTypes.bool,
        tags: PropTypes.bool,
        checkable: PropTypes.bool,
        popupWidth: PropTypes.number,
        listHeight: PropTypes.number,
        tagRender: PropTypes.func,
        popupContainer: PropTypes.oneOfType([PropTypes.instanceOf(Element), PropTypes.func]),
        isCloseMotion: PropTypes.bool,
        maxLength: PropTypes.number,
        maxTagPlaceholder: PropTypes.oneOfType([PropTypes.func, PropTypes.node]),
        autoClearSearchValue: PropTypes.bool,
        loading: PropTypes.bool,
        maxCount: PropTypes.number,
        maxTagTextLength: PropTypes.number,
        menuItemSelectedIcon: PropTypes.node,
        searchValue: PropTypes.string,
        tokenSeparators: PropTypes.array,
        onInputKeyDown: PropTypes.func,
        virtual: PropTypes.bool,
        virtualListProps: PropTypes.shape({
            height: PropTypes.number,
            itemHeight: PropTypes.number,
            threshold: PropTypes.number
        }),
        scrollToTopOnSearch: PropTypes.bool,
        defaultActiveFirstOption: PropTypes.bool,
        optionFilterProp: PropTypes.string,
        optionLabelProp: PropTypes.string,
    };

    static defaultProps = {
        options: [],
        // value: undefined,
        disablePortal: false,
        disabled: false,
        readOnly: false,
        placeholder: undefined,
        inputType: '',
        // size: 'normal',
        status: undefined,
        defaultPopupVisible: false,
        popupVisible: undefined,

        filterable: false,
        filterMethod: undefined,
        filterSort: undefined,
        multiple: false,
        maxTag: undefined,
        rawOption: false,
        clearable: false,
        grouped: false,
        showArrow: true,
        fieldNames: defaultFieldNames,

        optionRenderer: undefined,
        contentRenderer: undefined,
        multipleRenderer: undefined,
        zIndex: undefined,

        suffixIcon: 'chevron-down-new',
        prefixIcon: '',

        onChange: undefined,
        onCompositionStart: undefined,
        onCompositionEnd: undefined,
        onClear: undefined,
        onSearch: undefined,
        onBlur: undefined,
        onFocus: undefined,
        onPopupVisibleChange: undefined,
        onSelect: undefined,
        onDeSelect: undefined,
        onPopupScroll: undefined,
        notFoundContent: null,
        placement: undefined,
        flip: true,
        clearIcon: null,
        defaultValue: undefined,
        removeIcon: null,
        onMouseEnter: undefined,
        onMouseLeave: undefined,
        dropdownRender: undefined,
        showSelectIcon: true,
        isEnterSelect: true,
        autoFocus: false,
        bordered: true,
        tags: false,
        checkable: false,
        popupContainer: undefined,
        popupWidth: undefined,
        listHeight: undefined,
        tagRender: undefined,
        isCloseMotion: false,
        maxLength: undefined,
        maxTagPlaceholder: undefined,
        autoClearSearchValue: true,
        loading: false,
        maxCount: undefined,
        maxTagTextLength: undefined,
        menuItemSelectedIcon: undefined,
        searchValue: undefined,
        tokenSeparators: undefined,
        onInputKeyDown: undefined,
        virtual: false,
        virtualListProps: undefined,
        scrollToTopOnSearch: false,
        defaultActiveFirstOption: false,
        optionFilterProp: undefined,
        optionLabelProp: undefined,
    };

    // select 框体
    private selectRef: HTMLElement | null = null;

    // select 中的 input 框
    private inputRef: RefObject<HTMLInputElement> = createRef();

    // 下拉列表
    private listRef: RefObject<HTMLUListElement> = createRef();

    // 虚拟滚动下拉列表
    private vlistRef: RefObject<HTMLUListElement> = createRef();

    private filterOptions = memoize(filterOptions);

    private filterOptionGroups = memoize(filterOptionGroups);

    constructor(props: SelectorProps) {
        super(props);
        this.state = {
            focused: false,
            keyword: '', // 输入框值
            query: '', // 过滤选项
            tagOptions: [], // tags模式下的新增列表项
            valueChanged: false, // 监控输入框是否在变动
            showClear: false,
            showPopover: false,
            width: undefined,
            cursorValue: undefined,
            value: this.props.value !== undefined ? this.props.value : this.props.defaultValue,
            isForceUpdate: false,
        };

        this.showClear = this.showClear.bind(this);
        this.hideClear = this.hideClear.bind(this);

        this.showPopover = this.showPopover.bind(this);
        this.hidePopover = this.hidePopover.bind(this);

        this.handleClearAll = this.handleClearAll.bind(this);

        this.handleInputBlur = this.handleInputBlur.bind(this);
        this.handleInputFocus = this.handleInputFocus.bind(this);
        this.handleInputClick = this.handleInputClick.bind(this);
        this.handleInputChange = this.handleInputChange.bind(this);
        this.handleClickOutside = this.handleClickOutside.bind(this);
        this.handleKeyDown = this.handleKeyDown.bind(this);
        this.handleItemMouseLeave = this.handleItemMouseLeave.bind(this);
        this.handleScrollChange = this.handleScrollChange.bind(this);
    }

    static getDerivedStateFromProps(props: SelectorProps, state: SelectorState) {
        // 受控
        let newState = {};
        if (props.searchValue !== undefined) {
            if ((props.tags || props.multiple) && props.searchValue !== state.keyword) {
                newState = {
                    ...newState,
                    keyword: props.searchValue,
                    query: props.searchValue,
                };
            }
            if (!props.tags && !props.multiple) {
                newState = {
                    ...newState,
                    value: props.searchValue,
                    keyword: props.searchValue,
                    query: props.searchValue,
                };
            }
        }
        if (props.popupVisible !== undefined && props.popupVisible !== state.showPopover) {
            newState = {
                ...newState,
                showPopover: props.popupVisible,
                cursorValue: props.popupVisible ? state.cursorValue : undefined,
            };
        }
        // 受控时，内部与外部value不一样，处理外部将 value 设置为 undefined 的情况
        if ('value' in props && props.value !== state.value) {
            newState = {
                ...newState,
                value: props.value === undefined ? (props.value || props.defaultValue) : props.value,
            };
        }
        if (props.value !== undefined && isArray(props.value) && props.maxCount && props.maxCount <= props.value.length) {
            newState = {
                ...newState,
                value: props.value.slice(0, props.maxCount),
            };
        }
        // 关闭下拉框时清空 cursor
        if (!state.showPopover) {
            newState = {
                ...newState,
                cursorValue: undefined,
            };
        }
        return Object.keys(newState).length > 0 ? newState : null;
    }

    componentDidMount() {
        document.addEventListener('keydown', this.handleKeyDown);
        const {
            defaultPopupVisible,
            popupVisible,
        } = this.props;

        const isVisible = popupVisible || defaultPopupVisible;

        if (isVisible) {
            if (this.selectRef) {
                const { width } = this.selectRef.getBoundingClientRect();
                this.setState({
                    width,
                    showPopover: true,
                });
            }
        }
    }

    componentDidUpdate(prevProps: SelectorProps, prevState: SelectorState) {
        // 调整滚动位置使悬停项在可视区域内
        const { value, showPopover } = this.state;
        const { grouped, multiple, tags, virtual } = this.props;
        // 在弹窗打开时调整滚动位置
        if (prevState.showPopover !== showPopover && virtual && !grouped && this.vlistRef?.current) {
            const filteredOptions = this.getFilteredOptions();
            let _value = value;
            if (multiple || tags) {
                _value = value ? value?.[0] : undefined;
            }
            const index = filteredOptions.map(item => item.opt)?.findIndex(n => n.value === _value) || 0;
            // 设置滚动容器的滚动位置
            // eslint-disable-next-line no-unused-expressions
            this.vlistRef?.current?.scrollTo({ index });
        }
        if (showPopover && this.listRef.current && !virtual) {
            const list = this.listRef.current;
            const hover = list.querySelector(`.${CursorHoverClass}`) as HTMLLIElement;

            if (hover) {
                const windowTop = list.scrollTop;
                const windowBottom = list.scrollTop + list.clientHeight;

                const itemTop = hover.offsetTop;
                const itemBottom = hover.offsetTop + hover.clientHeight;

                if (itemTop < windowTop) {
                    if (list.scrollTo && typeof list.scrollTo === 'function') {
                        list.scrollTo(0, itemTop);
                    }
                }

                if (windowBottom < itemBottom) {
                    if (list.scrollTo && typeof list.scrollTo === 'function') {
                        list.scrollTo(0, itemBottom - list.clientHeight);
                    }
                }
            }
        }
    }

    componentWillUnmount() {
        document.removeEventListener('keydown', this.handleKeyDown);
    }

    getPlacement = () => {
        const { placement } = this.props;
        if (placement) {
            return placementEnum[placement as placementTypes];
        } else {
            return this.context.direction === 'RTL' ? placementEnum.bottomRight : placementEnum.bottomLeft;
        }
    };

    showClear() {
        const { clearable } = this.props;
        if (clearable) {
            this.setState({ showClear: true });
        }
    }

    hideClear() {
        this.setState({ showClear: false });
    }

    showPopover() {
        const { disabled } = this.props;
        if (disabled) return;
        this.setState({ showPopover: true }, this.handlePopupVisibleChange.bind(this, true));
    }

    hidePopover() {
        this.setState({ showPopover: false }, this.handlePopupVisibleChange.bind(this, false));
    }

    handlePopupVisibleChange(status: boolean) {
        const {
            onPopupVisibleChange,
            defaultActiveFirstOption,
        } = this.props;
        if (isFunction(onPopupVisibleChange)) {
            onPopupVisibleChange(status);
        }
        if (status) {
            this.handleInputFocus();
            // 搜索时默认高亮第一个选项
            if (defaultActiveFirstOption) {
                this.handleActiveFirstOption();
            }
        } else {
            this.handleInputBlur();
        }
    }

    handleInputBlur() {
        this.setState({
            focused: false,
            // keyword: '',
        }, () => {
            const { onBlur } = this.props;

            if (isFunction(onBlur)) {
                onBlur();
            }
        });
    }

    handleInputFocus() {
        this.setState({ focused: true }, () => {
            const { onFocus } = this.props;

            if (isFunction(onFocus)) {
                onFocus();
            }
        });
    }

    handleInputClick() {
        const { disabled, defaultActiveFirstOption } = this.props;
        if (disabled) {
            return;
        }

        if (this.selectRef) {
            const { width } = this.selectRef.getBoundingClientRect();

            this.setState({
                width,
                showPopover: true,
            }, this.handlePopupVisibleChange.bind(this, true));
        } else {
            this.setState({ showPopover: true }, this.handlePopupVisibleChange.bind(this, true));
        }
    }

    handleActiveFirstOption = () => {
        const optionList = this.getFilterdOptionList();
        if (optionList.length > 0) {
            const firstOption = optionList[0];
            if (firstOption && !firstOption.opt.disabled) {
                this.setState({
                    cursorValue: firstOption.opt.value,
                });
            }
        }
    }

    handleInputChange(event: React.ChangeEvent<HTMLInputElement>) {
        const { showPopover, value } = this.state;
        const { maxCount, tags, tokenSeparators, scrollToTopOnSearch, defaultActiveFirstOption, onSearch } = this.props;

        // tags模式下，当已选的tag数达到最大数时，不更新输入框的值
        if (tags && isArray(value) && maxCount && value.length >= maxCount) return;

        const keyword = event.target.value;
        if (!showPopover && this.selectRef) {
            const { width } = this.selectRef.getBoundingClientRect();

            this.setState({
                width,
                showPopover: true,
            });
        }

        // tags模式下，若有tokenSeparators，判断keyword是否能分割出token
        // 若能直接添加tag，若不能，则继续输入
        if (tags && !!tokenSeparators) {
            const token = this.handleTokenSeparators(keyword);
            if (token) {
                this.handleAddTagItem(token);
                return;
            }
        }

        // 输入时同步 keyword & query
        this.setState({
            keyword,
            query: keyword,
            valueChanged: true,
        }, () => {
            if (onSearch) {
                onSearch(keyword);
            }

            // 搜索完成后滚动到顶部
            if (scrollToTopOnSearch) {
                if (this.listRef.current) {
                    this.listRef.current.scrollTop = 0;
                }
                if (this.vlistRef.current) {
                    // @ts-ignore
                    this.vlistRef.current.scrollTo(0);
                }
            }

            // 默认高亮第一个选项
            if (defaultActiveFirstOption) {
                this.handleActiveFirstOption();
            }
        });
    }

    handleScrollChange(event: React.UIEvent<HTMLElement>) {
        const e = event.target as HTMLElement;
        const { onPopupScroll } = this.props;
        if (isFunction(onPopupScroll)) {
            onPopupScroll(e);
        }
    }

    // 自定义tag透传close事件
    handleTagDelClick = (item: SelectorOption) => {
        this.handleItemClick(item, undefined, 'delete');
        if (!this.state.showPopover && this.selectRef) {
            const { width } = this.selectRef.getBoundingClientRect();
            this.setState({
                width,
                showPopover: true,
            });
        }
    }

    handleItemClick(raw: SelectorOption, event?: React.MouseEvent, type?: string) {
        if (event) {
            event.preventDefault();
        }

        // { value, label, disabled }
        const option = this.getOptionFromRaw(raw);

        const {
            multiple, rawOption, onChange, disabled, readOnly, fieldNames = defaultFieldNames, onSelect, onDeSelect, onMouseLeave, tags, autoClearSearchValue
        } = this.props;
        const { value, tagOptions = [], keyword } = this.state;

        // 判断是否清除搜索框内容，单选模式下清除搜索框，非单选模式根据autoClearSearchValue判断
        const isClear = (!tags && !multiple) || autoClearSearchValue;

        const { active, optionDisabled } = this.getOptionStatus(option);

        let _tagOptions = tagOptions;
        if (optionDisabled || disabled || readOnly) {
            return;
        }

        if (isFunction(onDeSelect) && type === 'delete') {
            onDeSelect(raw);
        }

        if (isFunction(onSelect) && type === 'select') {
            onSelect(raw, option.value, option);
        }

        if (multiple || tags) {
            let newValue: (PrimitiveValue | SelectorOption)[] = [];
            let selectedOptions: SelectorOption[] = [];

            if (active) { // 取消选中
                if (isArray(value)) {
                    newValue = rawOption
                        ? (value as SelectorOption[]).filter(item => item[fieldNames.value || defaultFieldNames.value] !== option.value)
                        : (value as PrimitiveValue[]).filter(v => v !== option.value);

                    // 获取剩余选中项对应的options
                    selectedOptions = this.getOptionsList().filter(opt =>
                        newValue.includes(rawOption ? opt : opt[fieldNames.value || defaultFieldNames.value]),
                    );
                }
                if (tags) {
                    // 点击删除需要同时删除动态新增的列表项数据
                    _tagOptions = tagOptions?.filter(
                        item =>
                            item[fieldNames.value || defaultFieldNames.value] !==
                            raw[fieldNames.value || defaultFieldNames.value],
                    );
                }
            } else { // 新选中
                if (isArray(value)) {
                    newValue = rawOption
                        ? [...(value as SelectorOption[]), raw]
                        : [...(value as PrimitiveValue[]), option.value];
                } else {
                    newValue = [rawOption ? raw : option.value];
                }

                // 获取所有选中项对应的options
                selectedOptions = this.getOptionsList().filter(opt =>
                    newValue.includes(rawOption ? opt : opt[fieldNames.value || defaultFieldNames.value]),
                );
            }

            if (isFunction(onChange)) {
                onChange(newValue, raw, selectedOptions);
            }
            this.setState({
                value: newValue,
                tagOptions: _tagOptions,
            });
        } else {
            if (isFunction(onChange)) {
                onChange(rawOption ? raw : option.value, raw, [raw]);
            }
            this.setState({
                value: rawOption ? raw : option.value,
            });
            if (isFunction(onMouseLeave) && type === 'select' && !('popupVisible' in this.props)) {
                onMouseLeave();
            }
        }

        this.setState({
            // 是否清空输入框
            keyword: isClear ? '' : keyword,
            // 多选时下拉框保持展开
            showPopover: !!multiple || !!tags,
            // 多选时保留过滤结果
            // query: (multiple || tags) ? query : '',
            query: isClear ? '' : keyword,
        }, () => {
            if ((multiple || tags) && this.inputRef.current) {
                this.inputRef.current.focus();
            }

            // 单选的时候，点击才会直接关闭弹窗，多选的时候不会
            if (!multiple && !tags) {
                this.handlePopupVisibleChange(false);
            }
        });
    }

    handleItemMouseEnter(value: string) {
        this.setState({
            cursorValue: value,
        });
    }

    handleItemMouseLeave() {
        this.setState({
            cursorValue: undefined,
        });
    }

    handleClearAll(event: React.MouseEvent) {
        event.stopPropagation();

        this.setState({ keyword: '', query: '' });

        const { multiple, onChange, onClear, tags } = this.props;

        if (isFunction(onChange)) {
            onChange((multiple || tags) ? [] : undefined, {}, []); // 清空时selectedOptions传空数组
        }
        this.setState({
            value: (multiple || tags) ? [] : undefined,
            tagOptions: [],
        });
        if (onClear) {
            onClear();
        }
    }

    handleClickOutside(event: MouseEvent) {
        if (this.selectRef && this.selectRef.contains(event.target as HTMLElement)) {
            return;
        }

        this.setState({
            keyword: '',
            query: '',
            showPopover: false,
        }, this.handlePopupVisibleChange.bind(this, false));
    }

    handleArrowUp() {
        const { cursorValue } = this.state;
        const optionList = this.getFilterdOptionList();

        // 没有可激活选项
        if (optionList.every(({ opt }) => {
            const option = this.getOptionFromRaw(opt);
            const { optionDisabled } = this.getOptionStatus(option);
            return !!optionDisabled;
        })) {
            return;
        }

        const cursorIndex= optionList.findIndex(({ opt }) => opt.value === cursorValue);

        if (cursorValue !== null && cursorValue !== undefined
            && cursorIndex >= 0 && cursorIndex < optionList.length) {
            for (let i = (optionList.length + cursorIndex - 1) % optionList.length; ;) {
                if (!optionList[i].opt.disabled) {
                    this.setState({
                        cursorValue: optionList[i].opt.value,
                    });
                    return;
                }
                i = (optionList.length + i - 1) % optionList.length;
            }
        } else {
            for (let i = optionList.length - 1; i >= 0; --i) {
                if (!optionList[i].opt.disabled) {
                    this.setState({
                        cursorValue: optionList[i].opt.value,
                    });
                    return;
                }
            }
        }
    }

    handleArrowDown() {
        const { cursorValue } = this.state;
        const optionList = this.getFilterdOptionList();

        // 没有可激活选项
        if (optionList.every(({ opt }) => {
            const option = this.getOptionFromRaw(opt);
            const { optionDisabled } = this.getOptionStatus(option);
            return !!optionDisabled;
        })) {
            return;
        }
        const cursorIndex= optionList.findIndex(({ opt }) => opt.value === cursorValue);
        if (cursorValue !== null && cursorValue !== undefined
            && cursorIndex >= 0 && cursorIndex < optionList.length) {
            for (let i = (cursorIndex + 1) % optionList.length; ; i = (i + 1) % optionList.length) {
                if (!optionList[i].opt.disabled) {
                    this.setState({
                        cursorValue: optionList[i].opt.value,
                    });
                    return;
                }
            }
        } else {
            for (let i = 0; i < optionList.length; ++i) {
                if (!optionList[i].opt.disabled) {
                    this.setState({
                        cursorValue: optionList[i].opt.value,
                    });
                    return;
                }
            }
        }
    }

    handleEnter(): void {
        const { cursorValue } = this.state;
        const { isEnterSelect, tags } = this.props;
        if (isEnterSelect && !tags) {
            const optionList = this.getFilterdOptionList();
            const cursorIndex= optionList.findIndex(({ opt }) => opt.value === cursorValue);
            if (cursorValue !== undefined && cursorValue !== null && optionList[cursorIndex]) {
                const { raw } = optionList[cursorIndex];
                this.handleItemClick(raw);
            }
        }
    }

    handleBackspace(): void { // 删除前高亮确认，目前没有样式
        // empty
    }

    handleKeyDown(event: KeyboardEvent) {
        // console.log(event.key, event.keyCode, event.charCode);

        const { showPopover } = this.state;
        const { onInputKeyDown } = this.props;

        if (onInputKeyDown && isFunction(onInputKeyDown)) {
            onInputKeyDown(event);
        }

        if (!showPopover) {
            return;
        }

        switch (event.key) {
            case 'ArrowUp':
                event.preventDefault(); // 防止整个页面的滚动
                this.handleArrowUp();
                break;
            case 'ArrowDown':
                event.preventDefault(); // 防止整个页面的滚动
                this.handleArrowDown();
                break;
            case 'Enter':
                this.handleEnter();
                break;
            case 'Backspace':
                this.handleBackspace();
                break;
            default:
                break;
        }
    }

    handleCompositionStart = (event: React.CompositionEvent<HTMLInputElement> & React.ChangeEvent<HTMLInputElement>) => {
        const keyword = event.target.value;
        const { onCompositionStart } = this.props;
        if (isFunction(onCompositionStart)) {
            onCompositionStart(keyword);
        }
    }

    handleCompositionEnd = (event: React.CompositionEvent<HTMLInputElement> & React.ChangeEvent<HTMLInputElement>) => {
        const keyword = event.target.value;
        const { onCompositionEnd } = this.props;
        if (isFunction(onCompositionEnd)) {
            onCompositionEnd(keyword);
        }
    }

    getFilteredOptions() {
        const {
            query, // 过滤选项时使用 query
            tagOptions = [],
        } = this.state;
        const {
            options, filterable, filterMethod, fieldNames = defaultFieldNames, tags, filterSort, optionFilterProp
        } = this.props;

        return this.filterOptions(options as SelectorOption[], !!filterable, filterMethod, query, fieldNames, tagOptions, !!tags, filterSort, optionFilterProp);
    }

    getFilteredOptionGroups() {
        const {
            query, // 过滤选项时使用 query
        } = this.state;
        const {
            options, filterable, filterMethod, fieldNames = defaultFieldNames, filterSort, optionFilterProp
        } = this.props;

        return this.filterOptionGroups(options as SelectorOption[], !!filterable, filterMethod, query, fieldNames, filterSort, optionFilterProp);
    }

    // 拍平后的 options
    getFilterdOptionList() {
        const { grouped } = this.props;

        if (grouped) {
            const groups = this.getFilteredOptionGroups();
            return groups.reduce((acc, cur) => acc.concat(cur.children), [] as { raw: SelectorOption; opt: SelectorOption }[]);
        }

        return this.getFilteredOptions();
    }

    getInputClassName(): string {
        const {
            disabled,
            size = this.context.size || 'normal',
            inputType,
            multiple,
            bordered,
            tags,
        } = this.props;

        const list = [`${this.context.prefixCls}-input`, bordered ? '' : `${this.context.prefixCls}-input-noborder`];

        if (multiple || tags) {
            list.push(`${this.context.prefixCls}-input-tag`);
        }

        if (disabled) {
            list.push('disabled');
        }

        if (size) {
            list.push(`${this.context.prefixCls}-input-${sizeMap[size]}`);
        }

        if (inputType) {
            list.push(`${this.context.prefixCls}-input-${inputType}`);
        }

        return classNames(list);
    }

    // 选中 value 集合，用于判断 option 的选中状态
    getSelectedValueSet(): Set<PrimitiveValue> {
        const selectedValueSet = new Set<PrimitiveValue>();

        const { rawOption, fieldNames = defaultFieldNames } = this.props;
        const { value } = this.state;

        if (value === undefined || value === null) {
            return selectedValueSet;
        }

        if (rawOption) {
            if (isArray(value)) {
                (value as SelectorOption[]).forEach(item => {
                    selectedValueSet.add(item[fieldNames.value || defaultFieldNames.value]);
                });
            } else {
                selectedValueSet.add(((value || {}) as SelectorOption)[fieldNames.value || defaultFieldNames.value]);
            }
        } else {
            if (isArray(value)) {
                (value as (PrimitiveValue)[]).forEach(v => selectedValueSet.add(v));
            } else {
                selectedValueSet.add(value as PrimitiveValue);
            }
        }

        return selectedValueSet;
    }

    // 将分组中 options 拍平
    getOptionsList(): SelectorOption[] {
        const {
            options = [], grouped, fieldNames = defaultFieldNames, tags
        } = this.props;
        const { tagOptions = [] } = this.state;
        let _options = options;
        // tags模式下，需要动态更新options
        if (tags) {
            _options = options.concat(tagOptions);
            _options = uniqBy(_options, `${[fieldNames.value || defaultFieldNames.value]}`);
        }
        if (grouped) {
            let ret = [] as SelectorOption[];

            for (let i = 0; i < _options.length; ++i) {
                const children = (_options[i][fieldNames.children || defaultFieldNames.children] || []) as SelectorOption[];

                ret = ret.concat(children);
            }

            return ret;
        }

        return (_options as SelectorOption[]);
    }

    // getOptionValueSet() {
    //     const { fieldNames = defaultFieldNames } = this.props;
    //     return new Set(this.getOptionsList().map((opt) => opt[fieldNames.value || defaultFieldNames.value]));
    // }

    // !仅单选用
    // 单选时使用，选中项的展示内容
    getDisplayValue(): string | undefined {
        const {
            options,
            fieldNames = defaultFieldNames,
            rawOption,
            contentRenderer,
            optionLabelProp,
        } = this.props;
        const { value } = this.state;

        if (isDef(value) && !isArray(value)) {
            if (rawOption) {
                if (value === '') {
                    return '';
                }
                return isFunction(contentRenderer)
                    ? contentRenderer(value as SelectorOption)
                    : (value as SelectorOption)[optionLabelProp || fieldNames.label || defaultFieldNames.label];
            }

            if (options) {
                // 全部选项列表，分组选项拍平
                const list = this.getOptionsList();

                for (let i = 0; i < list.length; ++i) {
                    const opt = list[i];
                    const optValue = opt[fieldNames.value || defaultFieldNames.value];
                    if (optValue === value) {
                        return isFunction(contentRenderer)
                            ? contentRenderer(opt)
                            : opt[optionLabelProp || fieldNames.label || defaultFieldNames.label];
                    }
                }
            }

            return value as string;
        }

        return '';
    }

    getOptionFromRaw(raw: { [prop: string]: any }): NormalOption {
        const { fieldNames = defaultFieldNames } = this.props;

        return {
            value: raw[fieldNames.value || defaultFieldNames.value],
            label: raw[fieldNames.label || defaultFieldNames.label],
            disabled: !!raw[fieldNames.disabled || defaultFieldNames.disabled],
            title: raw[fieldNames.title || defaultFieldNames.title],
        };
    }

    // 输入框是否只读
    getInputReadOnly = () => {
        const { readOnly, filterable, tags } = this.props;
        if (readOnly) {
            return true;
        } else if (tags) {
            return false;
        } else if (filterable) {
            return false;
        } else {
            return false;
        }
    }

    // 判断选项是否禁用
    getOptionStatus = (opt: NormalOption) => {
        const { maxCount } = this.props;
        const { value } = this.state;

        const selectedValueSet = this.getSelectedValueSet();
        const active = selectedValueSet.has(opt.value);

        // 当选项未被选中且已选中的个数大于maxCount
        const isMaxCount = !active && !!maxCount && isArray(value) && value.length >= maxCount;

        return {
            optionDisabled: opt.disabled || isMaxCount,
            active,
        };
    }

    handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
        const {
            onEnter
        } = this.props;

        if (e.key === 'Enter' && onEnter) {
            onEnter(e, this.state.keyword);
        }
    }

    handleKeyUp = (e: React.KeyboardEvent<HTMLInputElement>): void => {
        const {
            value,
            keyword,
            focused,
            valueChanged,
        } = this.state;
        const { tags } = this.props;

        if (tags && (e.key === 'Enter' || e.keyCode === 13) && keyword && focused) {
            this.handleAddTagItem(keyword);
        }

        if (tags && (e.key === 'Backspace' || e.keyCode === 8) && keyword === '' && focused && isArray(value) && value.length) {
            // 此处理，为解决，删除inputValue最后一个字符时，同时删除最后一个标签问题
            if (valueChanged) {
                this.setState({
                    valueChanged: false,
                });
                return;
            }
            this.handleRemoveTagItem();
        }
    }

    // 回车新增标签项
    handleAddTagItem = (tagKeywords: string | string[]) => {
        const {
            options = [], rawOption, fieldNames = defaultFieldNames, onChange, onSelect, maxCount
        } = this.props;
        const { tagOptions = [], value = [] } = this.state;

        let _tagOptions = isArray(value) ? tagOptions : [];
        let newValue: (PrimitiveValue | SelectorOption)[] = isArray(value) ? value : [value];

        const keywords = isArray(tagKeywords) ? tagKeywords : [tagKeywords];

        // eslint-disable-next-line no-restricted-syntax
        for (const keyword of keywords) {
            // 不能超过最大数量
            if (maxCount && newValue.length >= maxCount) break;

            const raw = {
                [fieldNames.label || defaultFieldNames.label]: keyword,
                [fieldNames.value || defaultFieldNames.value]: keyword,
            };
            const option = this.getOptionFromRaw(raw);
            const selected = this.getSelectedValueSet().has(raw[fieldNames.value || defaultFieldNames.value]);
            // 新选中，更新
            if (!selected) {
                newValue = rawOption
                    ? [...newValue, raw]
                    : [...newValue, raw[fieldNames.value || defaultFieldNames.value]];
                _tagOptions = [..._tagOptions, raw];
                // 若输入的是options里传入的值，则需要去重，选中默认option项
                const allOptions = [...options, ..._tagOptions];
                const uniqOptions = uniqBy(allOptions, `${[fieldNames.value || defaultFieldNames.value]}`);
                _tagOptions = uniqOptions;
            }
            const selectedOptions = _tagOptions.filter(opt =>
                newValue.includes(rawOption ? opt : opt[fieldNames.value || defaultFieldNames.value]),
            );
            if (isFunction(onChange)) {
                onChange(newValue, raw, selectedOptions);
            }
            if (isFunction(onSelect)) {
                onSelect(raw, option.value, option);
            }
        }

        this.setState({
            value: newValue,
            tagOptions: _tagOptions,
            keyword: '',
            query: '',
        }, () => {
            const { onSearch } = this.props;

            if (onSearch) {
                onSearch('');
            }
        });
    }

    // 删除标签项
    handleRemoveTagItem = () => {
        const {
            rawOption, onChange, fieldNames = defaultFieldNames, onDeSelect
        } = this.props;
        const { tagOptions = [], value = [] } = this.state;
        const _valueFieldNames = fieldNames.value || defaultFieldNames.value;
        if (isArray(value) && value?.length) {
            // 删除的是最后一项
            const removeValue = value![value!.length - 1];
            const removeRaw: SelectorOption = tagOptions.find(n => n[_valueFieldNames] === removeValue) || {};

            let newValue: (PrimitiveValue | SelectorOption)[] = [];

            // 删除列表项
            const _tagOptions = tagOptions?.filter(item => item[_valueFieldNames] !== removeRaw[_valueFieldNames]);
            if (isArray(value)) {
                newValue = rawOption
                    ? (value as SelectorOption[]).filter(item => item[_valueFieldNames] !== removeValue[_valueFieldNames])
                    : (value as PrimitiveValue[]).filter(v => v !== removeValue);
            }
            if (isFunction(onChange)) {
                onChange(newValue, removeRaw, []);
            }
            if (isFunction(onDeSelect)) {
                onDeSelect(removeRaw);
            }

            this.setState({
                value: newValue,
                tagOptions: _tagOptions
            });
        }
    }

    // 处理placeholder
    handlePlaceHolderContent = () => {
        const { placeholder } = this.props;
        const placeholderLocale = locale.lng('Select.placeholder') as string;
        let placeholderContent = placeholder || placeholderLocale;
        if (placeholder === '') {
            placeholderContent = placeholder;
        }
        return placeholderContent;
    }

    handleTokenSeparators = (keyword: string) => {
        const { tokenSeparators } = this.props;

        // 分割token
        const result = tokenSeparators?.reduce((pre, cur) => {
            if (keyword.indexOf(cur) < 0) return pre;
            return pre.map(item => item.split(cur)).flat().filter(item => !!item);
        }, [keyword]);

        // 若结果数组的唯一值与keyword相同，则无法分割
        if (!result || result?.[0] === keyword) return false;

        return result;
    }

    renderPrefixIcon(): React.ReactNode {
        const { prefixIcon } = this.props;
        if (React.isValidElement(prefixIcon)) {
            return <span className={classNames(['roo-icon', 'prefix-icon', `${this.context.prefixCls}-selector-custom-icon`])}>{prefixIcon}</span>;
        }
        if (prefixIcon) {
            return (
                <i
                    className={classNames(['roo-icon', 'prefix-icon', `roo-icon-${prefixIcon}`])}
                />
            );
        }

        return null;
    }

    renderSuffixIcon(): React.ReactNode {
        const {
            disabled,
            readOnly,
            clearable,
            suffixIcon,
            showArrow,
            removeIcon,
            loading,
        } = this.props;

        const {
            keyword,
            showClear,
            value,
        } = this.state;

        if (clearable && showClear && !(disabled || readOnly) && (keyword.length > 0 || (isArray(value) ? value.length > 0 : (value?.toString() === 'false' || value === 0 || !!value)))) {
            return removeIcon ? (
                <i
                    className={classNames('roo-icon', 'addon-icon', 'has-click')}
                    onClick={this.handleClearAll}
                    onMouseEnter={this.showClear}
                    onMouseLeave={this.hideClear}
                >
                    {removeIcon}
                </i>
            ) : (
                <i
                    className={classNames('roo-icon', 'addon-icon', 'roo-icon-times-circle-new', 'has-click')}
                    onClick={this.handleClearAll}
                    onMouseEnter={this.showClear}
                    onMouseLeave={this.hideClear}
                />
            );
        }
        if (loading) {
            return (
                <i className={classNames('roo-icon', 'addon-icon')}>
                    <Icon name="loading" />
                </i>
            );
        }
        if (showArrow) {
            if (React.isValidElement(suffixIcon)) {
                return <span className={classNames(['roo-icon', 'addon-icon', `${this.context.prefixCls}-selector-custom-icon`])}>{suffixIcon}</span>;
            }
            return (
                <i
                    onClick={this.showPopover}
                    className={classNames('roo-icon', 'addon-icon', { [`roo-icon-${suffixIcon}`]: !!suffixIcon })}
                />
            );
        }
        return null;
    }

    renderTag = (opt: SelectorOption) => {
        const { maxTagTextLength, fieldNames = defaultFieldNames, optionLabelProp } = this.props;
        const content = opt[optionLabelProp || fieldNames.label || defaultFieldNames.label];
        if (maxTagTextLength && maxTagTextLength < (content?.length || 0)) {
            return `${content.slice(0, maxTagTextLength)}...`;
        }
        return content;
    }

    renderSingleContainer(): React.ReactNode {
        const {
            disabled,
            readOnly,
            filterable,
            autoFocus,
            prefixIcon,
            maxLength
        } = this.props;

        const { focused, keyword } = this.state;

        const displayValue = this.getDisplayValue();

        const cStyle = {
            flexBasis: 0,
            minWidth: '20px',
            border: 0,
        };

        return (
            <div
                style={{
                    overflow: 'hidden',
                    padding: '0',
                }}
                className={this.getInputClassName()}
            >
                {this.renderPrefixIcon()}
                <Input
                    inputRef={this.inputRef}
                    type="text"
                    autoComplete="off"
                    style={cStyle}
                    className={this.getInputClassName()}
                    disabled={disabled}
                    autoFocus={autoFocus}
                    readOnly={readOnly || !filterable}
                    value={(filterable && focused) ? keyword : displayValue}
                    placeholder={(filterable && focused) ? (displayValue || this.handlePlaceHolderContent()) : this.handlePlaceHolderContent()}
                    onChange={this.handleInputChange}
                    onMouseEnter={this.showClear}
                    onMouseLeave={this.hideClear}
                    onKeyDown={this.handleKeyPress}
                    onCompositionStart={this.handleCompositionStart}
                    onCompositionEnd={this.handleCompositionEnd}
                    customPreIcon={prefixIcon as NonNullable<string | boolean | null | undefined> | null | undefined}
                    maxLength={maxLength}
                />
            </div>
        );
    }

    renderMultipleContainer(): React.ReactNode {
        const {
            rawOption,
            fieldNames = defaultFieldNames,
            disabled,
            maxTag,
            clearIcon,
            autoFocus,
            inputType,
            prefixIcon,
            contentRenderer,
            multipleRenderer,
            tagRender,
            maxTagPlaceholder,
            maxLength,
            size = this.context.size || 'normal',
        } = this.props;

        const { keyword, value } = this.state;

        const tags: SelectorOption[] = [];

        if (isArray(value)) {
            if (rawOption) {
                tags.push(...(value as SelectorOption[])); //非包装
            } else {
                const optionsList = this.getOptionsList();

                for (let i = 0; i < value.length; ++i) {
                    const val = value[i] as (number | string);

                    const opt = optionsList.find(item => item[fieldNames.value || defaultFieldNames.value] === val);

                    // value 不在 options 不展示
                    if (opt) {
                        tags.push(opt);
                    }
                }
            }
        }

        const cStyle = {
            flexBasis: 0,
            minWidth: '20px',
            padding: prefixIcon ? '8px' : '0px',
        };

        const renderTagContent = isFunction(contentRenderer)
            ? contentRenderer
            : this.renderTag;

        return (
            <div
                className={this.getInputClassName()}
                onMouseEnter={this.showClear}
                onMouseLeave={this.hideClear}
            >
                {this.renderPrefixIcon()}
                <div
                    className={classNames(
                        'tags-box',
                        {
                            [`${this.context.prefixCls}-selector-tags-box-prefix-icon`]: (prefixIcon && tags.length),
                        }
                    )}
                >
                    {
                        isFunction(multipleRenderer) ? multipleRenderer(tags) : tags.map((item, index) => {
                            if (!maxTag || maxTag > index) {
                                if (isFunction(tagRender)) {
                                    return (
                                        <div key={item[fieldNames.value || defaultFieldNames.value]}>
                                            {tagRender({ item, onDeSelect: this.handleTagDelClick })}
                                        </div>
                                    );
                                } else {
                                    return (
                                        <span
                                            className={classNames(`${this.context.prefixCls}-tag ${this.context.prefixCls}-tag-gray`, {
                                                [`${this.context.prefixCls}-tag-${sizeMap[size]}`]: size !== 'normal',
                                            })}
                                            key={item[fieldNames.value || defaultFieldNames.value]}
                                        >
                                            {renderTagContent(item)}
                                            {item[fieldNames.disabled || defaultFieldNames.disabled] ? null : (
                                                clearIcon
                                                    ? (
                                                        <span
                                                            className={`${this.context.prefixCls}-tag-icon-close`}
                                                            onClick={this.handleItemClick.bind(this, item, undefined, 'delete')}
                                                        >
                                                            {clearIcon}
                                                        </span>
                                                    )
                                                    : (
                                                        <i
                                                            className="roo-icon roo-icon-close-new"
                                                            onClick={this.handleItemClick.bind(this, item, undefined, 'delete')}
                                                        />
                                                    )
                                            )}
                                        </span>
                                    );
                                }
                            } else if (maxTag === index) {
                                return (
                                    <span
                                        key="etc-tag"
                                        className={`${this.context.prefixCls}-selector-etc-tag`}
                                    >
                                        {
                                            isFunction(maxTagPlaceholder)
                                                ? maxTagPlaceholder(tags)
                                                : (maxTagPlaceholder || `...${locale.lng('Select.etc')}${tags.length}${locale.lng('Select.indivual')}`)
                                        }
                                    </span>
                                );
                            } else {
                                return null;
                            }
                        })
                    }
                    <Input
                        inputRef={this.inputRef}
                        type="text"
                        style={cStyle}
                        className={classNames({
                            [`${this.context.prefixCls}-selector-multiple-input-prefix-icon`]: prefixIcon && !tags.length,
                            [`${this.context.prefixCls}-selector-input-disabled`]: disabled,
                            [`${this.context.prefixCls}-selector-input-plaintext`]: inputType === 'plaintext',
                        })}
                        value={keyword}
                        placeholder={((Array.isArray(value) && !value.length) || !Array.isArray(value)) ? this.handlePlaceHolderContent() : ''}
                        disabled={disabled}
                        autoFocus={autoFocus}
                        readOnly={this.getInputReadOnly()}
                        onChange={this.handleInputChange}
                        onKeyDown={this.handleKeyPress}
                        onKeyUp={this.handleKeyUp}
                        onCompositionStart={this.handleCompositionStart}
                        onCompositionEnd={this.handleCompositionEnd}
                        customPreIcon={prefixIcon as NonNullable<string | boolean | null | undefined> | null | undefined}
                        maxLength={maxLength}
                    />
                </div>
            </div>
        );
    }

    renderItemLi = (item: SelectRawOption, index: number) => {
        const {
            disabled,
            readOnly,
            multiple,
            optionRenderer,
            showSelectIcon,
            checkable,
            menuItemSelectedIcon,
        } = this.props;

        const { opt, raw } = item;
        const { cursorValue } = this.state;
        const { active, optionDisabled } = this.getOptionStatus(opt);
        if (checkable && multiple) {
            return (
                <li
                    key={opt.value}
                    className={classNames(
                        `${this.context.prefixCls}-selector-option-default`,
                        `${this.context.prefixCls}-selector-option-checkbox`,
                        {
                            'item-no-select-icon': !showSelectIcon,
                            [`${CursorHoverClass}`]: opt.value === cursorValue,
                            [`${opt.className}`]: !!opt.className,
                        },
                    )}
                    onMouseEnter={optionDisabled ? undefined : this.handleItemMouseEnter.bind(this, opt.value)}
                    onMouseLeave={this.handleItemMouseLeave}
                >
                    <Checkbox
                        disabled={optionDisabled}
                        checked={active}
                        onChange={e => {
                            const checked = e.target.checked;
                            if (checked) {
                                this.handleItemClick(raw, undefined, 'delete');
                            } else {
                                this.handleItemClick(raw, undefined, 'select');
                            }
                        }}
                    >
                        <span title={opt.title || opt.label || ''}>
                            { isFunction(optionRenderer) ? optionRenderer(opt, raw) : this.renderSearchHighlight(opt)}
                        </span>
                    </Checkbox>
                </li>
            );
        } else {
            return (
                <li
                    key={opt.value}
                    className={classNames(
                        `${this.context.prefixCls}-selector-option-default`,
                        {
                            'item-no-select-icon': !showSelectIcon,
                            [`${CursorHoverClass}`]: opt.value === cursorValue
                        }
                    )}
                    onMouseEnter={optionDisabled ? undefined : this.handleItemMouseEnter.bind(this, opt.value)}
                    onMouseLeave={this.handleItemMouseLeave}
                >
                    <a
                        className={classNames(
                            `${this.context.prefixCls}-selector-option-item`,
                            {
                                'active-highlight': showSelectIcon && active && !menuItemSelectedIcon,
                                'active-highlight-customiz': active && menuItemSelectedIcon,
                                'active-highlight-no-select-icon': !showSelectIcon && active,
                                disabled: optionDisabled || readOnly || disabled,
                                [`${opt.className}`]: !!opt.className,
                            },
                        )}
                        data-value={opt.value}
                        onClick={this.handleItemClick.bind(this, raw, undefined, 'select')}
                        title={opt.title || opt.label || ''}
                    >
                        {this.renderOptionContent(opt, raw)}
                    </a>
                </li>
            );
        }
    };

    renderOptionContent = (opt: NormalOption, raw: SelectorOption) => {
        const { menuItemSelectedIcon, optionRenderer } = this.props;
        const { active } = this.getOptionStatus(opt);
        if (active && menuItemSelectedIcon) {
            return (
                <>
                    <div className="active-highlight-customiz-option">
                        {isFunction(optionRenderer) ? optionRenderer(opt, raw) : this.renderSearchHighlight(opt)}
                    </div>
                    <div className="active-highlight-customiz-icon">{menuItemSelectedIcon}</div>
                </>
            );
        }
        return isFunction(optionRenderer) ? optionRenderer(opt, raw) : this.renderSearchHighlight(opt);
    }

    renderOptions = (): React.ReactNode => {
        const {
            popupClass,
            popupStyle,
            listHeight,
            notFoundContent,
            dropdownRender,
            virtualListProps,
            fieldNames,
            virtual
        } = this.props;

        // 覆盖 roo-theme 样式
        const style: React.CSSProperties = {
            position: 'relative',
            width: '100%',
            height: '100%',
            top: 'auto',
            left: 'auto',
            ...popupStyle,
        };

        // 弹窗滚动高度
        const heightStyle = listHeight ? {
            height: listHeight,
            maxHeight: listHeight
        } : {};

        const filteredOptions = this.getFilteredOptions();
        const EmptyContent = () => (
            <li>
                {
                    notFoundContent || (
                        <Empty title={locale.lng('Select.noData') as string} />
                    )
                }
            </li>
        );
        const { height = 180, itemHeight = 36, threshold = 100 } = virtualListProps || {};
        let content = null;
        if (filteredOptions.length > 0) {
            content = virtual
                ? (
                    <VirtualList
                        renderItem={(item, index) => this.renderItemLi(item, index)}
                        height={height}
                        itemHeight={itemHeight}
                        itemKey={item => item?.opt?.value || 'value'}
                        data={filteredOptions}
                        listRef={this.vlistRef}
                        virtual={isNull(threshold) ? false : filteredOptions.length >= threshold}
                        onScroll={this.handleScrollChange}
                    />
                )
                : filteredOptions.map((item, index) => this.renderItemLi(item, index));
        } else {
            content = EmptyContent();
        }
        return (
            <ul
                ref={this.listRef}
                className={classNames(`${this.context.prefixCls}-dropdown-menu`, popupClass)}
                style={{ ...style, ...heightStyle }}
                onScroll={this.handleScrollChange}
            >
                {isFunction(dropdownRender) ? dropdownRender(content) : content}
            </ul>
        );
    }

    // 渲染高亮
    renderSearchHighlight = (item: NormalOption) => {
        const { tags, filterable } = this.props;
        const { label = '', disabled } = item;
        const { keyword } = this.state;
        const findIndex: number = `${label}`.indexOf(keyword);
        if (keyword && findIndex > -1 && !disabled && ((!tags && filterable) || (tags && filterable))) {
            const start = label.slice(0, findIndex);
            const end = label.slice(findIndex + keyword.length);
            return (
                <div className={`${this.context.prefixCls}-selector-option-item-label`}>
                    {start}
                    <span className={`${this.context.prefixCls}-selector-font-weight`}>
                        {keyword}
                    </span>
                    {end}
                </div>
            );
        } else {
            return (
                <div className={`${this.context.prefixCls}-selector-option-item-label`}>
                    {label}
                </div>
            );
        }
    }

    renderGroupItemLi = (item: SelectRawOption, index: number) => {
        const {
            disabled,
            readOnly,
            multiple,
            checkable,
            optionRenderer,
            showSelectIcon,
            menuItemSelectedIcon,
        } = this.props;
        const { opt, raw } = item;

        const { cursorValue } = this.state;

        const { active, optionDisabled } = this.getOptionStatus(opt);
        if (checkable && multiple) {
            return (
                <li
                    key={opt.value}
                    className={classNames(
                        `${this.context.prefixCls}-selector-option-default`,
                        `${this.context.prefixCls}-selector-option-checkbox`,
                        {
                            'item-no-select-icon': !showSelectIcon,
                            [`${CursorHoverClass}`]: opt.value === cursorValue
                        }
                    )}
                    onMouseEnter={optionDisabled ? undefined : this.handleItemMouseEnter.bind(this, opt.value)}
                    onMouseLeave={this.handleItemMouseLeave}
                >
                    <Checkbox
                        disabled={optionDisabled}
                        checked={active}
                        onChange={e => {
                            const checked = e.target.checked;
                            if (checked) {
                                this.handleItemClick(raw, undefined, 'delete');
                            } else {
                                this.handleItemClick(raw, undefined, 'select');
                            }
                        }}
                    >
                        <span title={opt.title || opt.label || ''}>
                            {isFunction(optionRenderer) ? optionRenderer(opt, raw) : this.renderSearchHighlight(opt)}
                        </span>
                    </Checkbox>
                </li>
            );
        } else {
            return (
                <li
                    key={opt.value}
                    className={classNames(
                        `${this.context.prefixCls}-selector-option-default`,
                        {
                            'item-no-select-icon': !showSelectIcon,
                            [`${CursorHoverClass}`]: opt.value === cursorValue
                        }
                    )}
                    onMouseEnter={optionDisabled ? undefined : this.handleItemMouseEnter.bind(this, opt.value)}
                    onMouseLeave={this.handleItemMouseLeave}
                >
                    <a
                        className={classNames(
                            `${this.context.prefixCls}-selector-option-item`,
                            {
                                'active-highlight': showSelectIcon && active && !menuItemSelectedIcon,
                                'active-highlight-customiz': active && menuItemSelectedIcon,
                                'active-highlight-no-select-icon': !showSelectIcon && active,
                                disabled: optionDisabled || readOnly || disabled,
                                [`${opt.className}`]: !!opt.className,
                            }
                        )}
                        data-value={opt.value}
                        onClick={this.handleItemClick.bind(this, raw, undefined, 'select')}
                        title={opt.title || opt.label || ''}
                    >
                        {this.renderOptionContent(opt, raw)}
                    </a>
                </li>
            );
        }
    }

    renderContent = (subList: SelectRawOption[]) => {
        const { fieldNames, virtual, virtualListProps } = this.props;
        const { height = 80, itemHeight = 36, threshold = 100 } = virtualListProps || {};

        if (virtual) {
            return (
                <VirtualList
                    renderItem={(item, index) => this.renderGroupItemLi(item, index)}
                    height={height}
                    itemHeight={itemHeight}
                    itemKey={item => item?.opt?.value || 'value'}
                    data={subList}
                    virtual={isNull(threshold) ? false : subList.length >= threshold}
                    onScroll={this.handleScrollChange}
                />
            );
        } else {
            return subList.map((item, index) => this.renderGroupItemLi(item, index));
        }
    };

    renderGroupedOptions() {
        const {
            popupClass,
            popupStyle,
            notFoundContent,
            dropdownRender,
        } = this.props;

        // 覆盖 roo-theme 样式
        const style: React.CSSProperties = {
            position: 'relative',
            width: '100%',
            height: '100%',
            top: 'auto',
            left: 'auto',
            ...popupStyle,
        };

        const showOptionGroups = this.getFilteredOptionGroups();

        const list = showOptionGroups.reduce<React.ReactNode[]>((acc, group, i) => {
            const { title, children } = group;
            if (children.length > 0) {
                acc.push(
                    <li key={`group_${title}_${i}`}>
                        <div className="dropdown-classify">
                            <h6>{title}</h6>
                            <ul>
                                {this.renderContent(children)}
                            </ul>
                        </div>
                    </li>
                );
            }
            return acc;
        }, []);

        const content = list.length > 0 ? list : (
            <li>
                {
                    notFoundContent || (
                        <Empty title={locale.lng('Select.noData') as string} />
                    )
                }
            </li>
        );

        return (
            <ul
                ref={this.listRef}
                className={classNames(`${this.context.prefixCls}-dropdown-menu`, popupClass)}
                style={style}
                onScroll={this.handleScrollChange}
            >
                {isFunction(dropdownRender) ? dropdownRender(content) : content}
            </ul>
        );
    }

    handleMouseEnter = () => {
        const { onMouseEnter } = this.props;
        if (isFunction(onMouseEnter)) {
            onMouseEnter();
        }
    }

    handleMouseLeave = () => {
        const { onMouseLeave } = this.props;
        if (isFunction(onMouseLeave)) {
            onMouseLeave();
        }
    }

    render(): React.ReactNode {
        const {
            className,
            style,
            grouped,
            multiple,
            zIndex,
            disablePortal,
            flip,
            popupContainer,
            popupWidth,
            status,
            tags,
            isCloseMotion,
        } = this.props;

        const {
            showPopover,
            width,
            isForceUpdate,
        } = this.state;
        return (
            <div
                className={classNames('dropdown', className)}
                style={style}
                onMouseEnter={this.handleMouseEnter}
                onMouseLeave={this.handleMouseLeave}
            >
                <Manager>
                    <ReactResizeDetector
                        handleHeight
                        handleWidth
                        onResize={() => {
                            // 当输入框宽高变化时，下拉窗重新渲染
                            this.setState({
                                isForceUpdate: !isForceUpdate
                            });
                            if (this.selectRef) {
                                const { width: selectWidth } = this.selectRef.getBoundingClientRect();
                                this.setState({
                                    width: selectWidth,
                                });
                            }
                        }}
                    >
                        <Reference
                            innerRef={node => { this.selectRef = node; }}
                        >
                            <div
                                className={classNames(
                                    `${this.context.prefixCls}-input-group`,
                                    'has-icon',
                                    { 'has-error': status === 'error' },
                                    { 'is-open': showPopover },
                                    { 'has-success-color': status === 'success' }
                                )}
                                onClick={this.handleInputClick}
                            >
                                {(multiple || tags) ? this.renderMultipleContainer() : this.renderSingleContainer()}
                                {this.renderSuffixIcon()}
                            </div>
                        </Reference>
                    </ReactResizeDetector>
                    <PopperPortal
                        zIndex={zIndex}
                        disablePortal={disablePortal}
                        visible={showPopover}
                        isForceUpdate={isForceUpdate}
                        modifiers={[{
                            name: 'flip',
                            enabled: flip,
                            options: {
                                flipVariations: flip === 'flip',
                            }
                        }]}
                        placement={this.getPlacement()}
                        container={popupContainer}
                        onClickOutside={this.handleClickOutside}
                        motionName={isCloseMotion ? `${this.context.prefixCls}-close-motion` : `${this.context.prefixCls}-popup-fast-motion`}
                        isCloseMotion={isCloseMotion}
                    >
                        <div
                            style={{ width: popupWidth || width }}
                            className={`${this.context.prefixCls}-selector ${this.context.prefixCls}-selector-content`}
                        >
                            {grouped ? this.renderGroupedOptions() : this.renderOptions()}
                        </div>
                    </PopperPortal>
                </Manager>
            </div>
        );
    }
}

polyfill(Selector);

export default withDisabled<typeof Selector>(Selector);
