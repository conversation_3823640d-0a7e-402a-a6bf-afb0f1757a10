import * as React from 'react';
import * as PropTypes from 'prop-types';
import classNames from 'classnames';
import {
    disableBodyScroll, enableBodyScroll, canUseDocElement, offset, contains
} from '@roo/roo/_utils/Dom';
import Button from '@roo/roo/Button';
import Icon from '@roo/roo/Icon';
import locale from '@roo/roo/locale';
import omit from 'lodash/omit';
import { isFunction } from '@utiljs/is';
import AdvancedPortal from '@roo/roo/core/AdvancedPortal';
import CSSMotion from 'rc-motion';
import warning from 'warning';

import { GlobalConfigContext } from '@roo/roo/ConfigProvider';
import {
    ModalProps, ModalConfirmProps, ConfirmCallback, WithConfirmProps, OldModalProps
} from './interface';
import KeyCode from '../_utils/KeyCode';

const sizeMap: { [key: string]: string } = {
    large: '-lg',
    normal: '',
    default: '-default', // 兼容历史
    small: '-sm',
    compact: '-compact',
};

// 兼容历史
const SizeRename: any = {
    lg: 'large',
    sm: 'small',
};

let mousePosition: { x: number; y: number } | null;
const getClickPosition = (e: MouseEvent) => {
    mousePosition = {
        x: e.pageX,
        y: e.pageY,
    };

    // 100ms 内发生过点击事件，则从点击位置动画展示
    // 否则直接 zoom 展示
    // 这样可以兼容非点击方式展开
    setTimeout(() => {
        mousePosition = null;
    }, 100);
};

// 只有点击事件支持从鼠标位置动画展开
if (canUseDocElement()) {
    document.documentElement.addEventListener('click', getClickPosition, true);
}

const ModalStatus: any = {
    info: 'info',
    success: 'success',
    danger: 'danger',
    question: 'question'
};

class Modal extends React.Component<ModalProps & OldModalProps, {transformOrigin: string}> {
    static contextType?: React.Context<any> | undefined = GlobalConfigContext;

    static confirm: (p: ModalConfirmProps) => ConfirmCallback

    static WithConfirm: React.FC<WithConfirmProps>

    static propTypes = {
        visible: PropTypes.bool.isRequired,
        title: PropTypes.node,
        status: PropTypes.oneOf(['normal', 'info', 'success', 'danger', 'question']),
        closable: PropTypes.bool,
        size: PropTypes.oneOf(['sm', 'default', 'lg', 'large', 'normal', 'compact', 'small']),
        width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        container: PropTypes.oneOfType([PropTypes.node, PropTypes.func]),
        popupContainer: PropTypes.oneOfType([PropTypes.node, PropTypes.func]),
        closeIcon: PropTypes.oneOfType([PropTypes.node, PropTypes.func]),
        footer: PropTypes.oneOfType([PropTypes.node, PropTypes.func]),
        backdrop: PropTypes.bool,
        backdropClosable: PropTypes.bool,
        backdropStyle: PropTypes.object,
        onClose: PropTypes.func,
        onOpen: PropTypes.func,
        onConfirm: PropTypes.func,
        onCancel: PropTypes.func,
        closeText: PropTypes.string,
        openText: PropTypes.string,
        confirmText: PropTypes.string,
        cancelText: PropTypes.string,
        showCloseButton: PropTypes.bool,
        showOpenButton: PropTypes.bool,
        showCancelButton: PropTypes.bool,
        showConfirmButton: PropTypes.bool,
        closeButtonProps: PropTypes.object,
        openButtonProps: PropTypes.object,
        className: PropTypes.string,
        lazy: PropTypes.bool,
        keyboard: PropTypes.bool,
        contentHeight: PropTypes.number,
        footerBtnPosition: PropTypes.oneOf(['left', 'center', 'right']),
        centered: PropTypes.bool,
        closeMotion: PropTypes.bool,
        styles: PropTypes.object,
        confirmLoading: PropTypes.bool,
        okType: PropTypes.oneOf(['normal', 'hollow', 'weaken', 'dashed', 'text', 'brand', 'brand-hollow', 'brand-text']),
        afterOpenChange: PropTypes.func,
        focusTriggerAfterClose: PropTypes.bool,
        classNames: PropTypes.object,
        modalRender: PropTypes.func,
        destroyOnHidden: PropTypes.bool,
        forceRender: PropTypes.bool,
    };

    static defaultProps = {
        title: undefined,
        backdrop: true,
        backdropClosable: true,
        status: undefined,
        container: undefined,
        popupContainer: undefined,
        closeIcon: undefined,
        footer: undefined,
        lazy: false,
        size: undefined,
        width: undefined,
        onClose: undefined,
        onOpen: undefined,
        onCancel: () => {},
        onConfirm: () => {},
        closeText: undefined,
        openText: undefined,
        cancelText: undefined,
        confirmText: undefined,
        closable: true,
        showCloseButton: true,
        showCancelButton: true,
        showOpenButton: true,
        showConfirmButton: true,
        closeButtonProps: null,
        openButtonProps: null,
        backdropStyle: null,
        className: undefined,
        keyboard: false,
        contentHeight: undefined,
        footerBtnPosition: 'right',
        centered: true,
        closeMotion: false,
        styles: {},
        confirmLoading: false,
        okType: 'normal',
        afterOpenChange: undefined,
        focusTriggerAfterClose: true,
        classNames: undefined,
        modalRender: undefined,
        destroyOnHidden: false,
        forceRender: false,
    };

    modalMotionRef = React.createRef<any>();

    wrapperRef: HTMLDivElement | null = null; // 容器
    triggerRef: HTMLElement | null = null;

    constructor(props: ModalProps & OldModalProps) {
        super(props);

        if (props.onClose || props.onOpen) {
            warning(false, 'Roo Modal: 1.0.0 之后 onClose 改为 onCancel, onOpen 改为 onConfirm, 请及时修改 API 名称');
        }
        this.state = {
            transformOrigin: ''
        };
    }

    componentDidUpdate(preProps: ModalProps & OldModalProps) {
        const { visible } = this.props;
        if (visible !== preProps.visible && visible) {
            this.triggerRef = document.activeElement as HTMLElement;
            setTimeout(() => {
                // esc键盘事件元素自动聚焦，解决直接按esc键不触发的问题
                // 解释：当前document.activeElement聚焦元素是否是wrapperRef，不是wrapperRef才聚焦，解决model中有使用聚焦元素冲突的问题
                if (!contains(this.wrapperRef, document.activeElement as any)) {
                    // eslint-disable-next-line no-unused-expressions
                    this.wrapperRef?.focus();
                }
            });
            disableBodyScroll();
        }
        if (visible !== preProps.visible && !visible) {
            enableBodyScroll();
        }
    }

    componentWillUnmount() {
        if (this.props.visible) {
            enableBodyScroll();
        }
    }

    // ============================= Style ==============================

    onPrepare = () => {
        const { closeMotion } = this.props;
        if (closeMotion) return;
        const elementOffset = offset(this.modalMotionRef.current);
        this.setState({
            transformOrigin: mousePosition
                ? `${mousePosition.x - elementOffset.left}px ${mousePosition.y - elementOffset.top}px`
                : '',
        });
    }

    handleClose = (e: React.SyntheticEvent<HTMLElement>, type: 'backdrop' | 'button' | 'closeIcon' | 'esc') => {
        const {
            onClose,
            onCancel,
        } = this.props;
        if (isFunction(onClose)) {
            onClose(e, type);
        }
        if (isFunction(onCancel)) {
            onCancel(e, type);
        }
    }

    handleBtnClose = (e?: React.SyntheticEvent<HTMLButtonElement>): void => {
        this.handleClose(e as React.SyntheticEvent<HTMLButtonElement>, 'button');
    }

    handleIconClose = (e: React.SyntheticEvent<HTMLButtonElement>) => {
        this.handleClose(e, 'closeIcon');
    }

    handleMaskClick = (event: React.SyntheticEvent<HTMLElement>) => {
        const {
            backdropClosable,
        } = this.props;
        // 阻止事件冒泡
        event?.stopPropagation();
        if (!backdropClosable) {
            return;
        }
        if (event.target === event.currentTarget) {
            this.handleClose(event, 'backdrop');
        }
    }

    handleConfirm = (e?: React.SyntheticEvent<HTMLButtonElement>) => {
        const {
            onOpen,
            onConfirm,
        } = this.props;
        if (isFunction(onOpen)) {
            onOpen(e as React.SyntheticEvent<HTMLButtonElement>);
        }
        if (isFunction(onConfirm)) {
            onConfirm(e as React.SyntheticEvent<HTMLButtonElement>);
        }
    }

    onWrapperKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
        const { keyboard } = this.props;
        if (keyboard && e.keyCode === KeyCode.ESC) {
            e.stopPropagation();
            this.handleClose(e, 'esc');
        }
        // keep focus inside dialog
        // if (visible) {
        //   if (e.keyCode === KeyCode.TAB) {
        //     contentRef.current.changeActive(!e.shiftKey);
        //   }
        // }
    }

    renderTitle = (): React.ReactNode | null => {
        const {
            title,
            status,
            styles = {},
            classNames: modalClassNames = {},
        } = this.props;

        if (title && (!status || status === 'normal')) {
            const headerClassName = classNames(`${this.context.prefixCls}-modal-header`, modalClassNames.header);
            return (
                <div className={headerClassName} style={styles.header}>
                    <h5 className={`${this.context.prefixCls}-modal-title`}>{title}</h5>
                </div>
            );
        }
        return null;
    }

    renderClose = () => {
        const {
            title,
            closable,
            closeIcon,
        } = this.props;

        const closeIconNode = isFunction(closeIcon) ? closeIcon() : closeIcon;

        return title && closable ? (
            <button
                type="button"
                className="close"
                data-dismiss={`${this.context.prefixCls}-modal`}
                onClick={this.handleIconClose}
            >
                {closeIconNode || <Icon name="close-new" />}
            </button>
        ) : null;
    }

    renderBody = () => {
        const {
            children,
            status,
            title,
            contentHeight,
            styles = {},
            classNames: modalClassNames = {},
        } = this.props;
        const isStatus = status && ModalStatus[status];
        const bodyCls = classNames(`${this.context.prefixCls}-modal-body`, {
            'has-icon': isStatus
        }, status);
        
        const bodyClassName = modalClassNames.body ? `${bodyCls} ${modalClassNames.body}` : bodyCls;
        
        const bodyStyle = {
            ...(contentHeight !== undefined ? { height: contentHeight } : {}),
            ...styles.body,
        };

        return (
            <div
                className={bodyClassName}
                style={bodyStyle}
            >
                {
                    title && isStatus ? (
                        <h5 className="title">{title}</h5>
                    ) : null
                }
                <div>{children}</div>
            </div>
        );
    }

    renderFooter = () => {
        const {
            closeText,
            cancelText,
            openText,
            confirmText,
            showCloseButton,
            showOpenButton,
            showConfirmButton,
            showCancelButton,
            closeButtonProps,
            cancelButtonProps,
            openButtonProps,
            confirmButtonProps,
            confirmLoading,
            okType = 'normal',
            footer,
            footerBtnPosition,
            size = this.context.size || 'normal',
            styles = {},
            classNames: modalClassNames = {},
        } = this.props;

        const off = showCloseButton && showCancelButton;
        const on = showOpenButton && showConfirmButton;

        if (footer === null) return '';

        if (footer) {
            const footerNode = isFunction(footer) ? footer() : <>{footer}</>;
            const footerClassName = classNames(`${this.context.prefixCls}-modal-footer ${footerBtnPosition ? `${this.context.prefixCls}-modal-footer-${footerBtnPosition}` : `${this.context.prefixCls}-modal-footer-right`}`);
            const combinedClassName = modalClassNames.footer ? `${footerClassName} ${modalClassNames.footer}` : footerClassName;

            return (
                <div className={combinedClassName} style={styles.footer}>
                    {footerNode}
                </div>
            );
        }
        if (off || on) {
            const footerClassName = classNames(`${this.context.prefixCls}-modal-footer ${footerBtnPosition ? `${this.context.prefixCls}-modal-footer-${footerBtnPosition}` : `${this.context.prefixCls}-modal-footer-right`}`);
            const combinedClassName = modalClassNames.footer ? `${footerClassName} ${modalClassNames.footer}` : footerClassName;
            
            return (
                <div className={combinedClassName} style={styles.footer}>
                    {
                        off ? (
                            <Button
                                type="hollow"
                                onClick={this.handleBtnClose}
                                size={SizeRename[size] || size}
                                {...closeButtonProps}
                                {...cancelButtonProps}
                            >
                                {cancelText || closeText || locale.lng('Modal.closeText')}
                            </Button>
                        ) : null
                    }
                    {
                        on ? (
                            <Button
                                type={okType}
                                onClick={this.handleConfirm}
                                size={SizeRename[size] || size}
                                loading={confirmLoading}
                                {...openButtonProps}
                                {...confirmButtonProps}
                            >
                                {confirmText || openText || locale.lng('Modal.openText')}
                            </Button>
                        ) : null
                    }
                </div>
            );
        }
        return null;
    }

    render(): React.ReactNode {
        const {
            visible,
            backdrop,
            size = this.context.size || 'default', // 兼容历史
            width,
            status,
            title,
            closable,
            className,
            backdropStyle,
            backdropClosable,
            lazy,
            container,
            popupContainer,
            zIndex,
            closeMotion,
            styles = {},
            classNames: modalClassNames = {},
            modalRender,
            destroyOnHidden,
            forceRender,
            centered = true,
            ...restProps
        } = this.props;

        const rest = omit(restProps,
            ['closeText', 'openText', 'onClose',
                'confirmText', 'cancelText', 'onConfirm', 'onCancel',
                'showCancelButton', 'showConfirmButton', 'confirmButtonProps', 'cancelButtonProps',
                'onOpen', 'showCloseButton', 'showOpenButton', 'closeIcon',
                'closeButtonProps', 'openButtonProps', 'backdropClosable', 'keyboard', 'contentHeight', 'footerBtnPosition',
                'afterClose', 'confirmLoading', 'okType', 'width', 'afterOpenChange', 'focusTriggerAfterClose', 'classNames',
                'modalRender', 'destroyOnHidden', 'forceRender', 'centered',
            ]);

        const wrapperClassName = classNames(`${this.context.prefixCls}-modal`, {
            backdrop,
        });
        const combinedWrapperClassName = modalClassNames?.wrapper ? `${wrapperClassName} ${modalClassNames.wrapper}` : wrapperClassName;
        
        let wrapStyle: React.CSSProperties;
        if (!backdrop) {
            wrapStyle = {
                top: '50%',
                left: '50%',
                right: 'auto',
                bottom: 'auto',
                transform: 'translate3d(-50%, -50%, 0)',
            };
        } else if (!centered) {
            wrapStyle = {
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'flex-start',
                paddingTop: '90px',
                ...styles.mask,
                ...backdropStyle,
            };
        } else {
            wrapStyle = styles.mask || backdropStyle || {};
        }
        
        const modalSize = SizeRename[size] || size;
        const modalCls = classNames(
            `${this.context.prefixCls}-modal-dialog`,
            `${this.context.prefixCls}-modal${sizeMap[modalSize]}`,
            className,
        );
        const contentClassName = classNames(`${this.context.prefixCls}-modal-content`);
        const combinedContentClassName = modalClassNames?.content ? `${contentClassName} ${modalClassNames.content}` : contentClassName;

        // 设置模态框宽度
        const modalStyle: React.CSSProperties = {
            ...rest.style,
            ...(width !== undefined ? { width } : {}),
            transformOrigin: this.state.transformOrigin,
        };

        return (
            <AdvancedPortal
                zIndex={zIndex}
                visible={visible || !!this.state.transformOrigin}
                lazy={lazy && !destroyOnHidden}
                container={container || popupContainer}
                forceRender={forceRender}
            >
                <CSSMotion
                    // key="mask"
                    visible={visible}
                    motionName={closeMotion ? `${this.context.prefixCls}-close-motion` : `${this.context.prefixCls}-mask-motion`}
                    onLeaveEnd={(ele: React.ReactElement, e: any) => {
                        if (closeMotion) return false;
                        // 判断动画结束事件触发的是否是我们设置动画的元素
                        if (e.target !== ele) {
                            return false;
                        }
                        this.setState({
                            transformOrigin: ''
                        });
                        return false;
                    }}
                    forceRender={forceRender}
                >
                    {({ className: maskMotionClassName, style: maskMotionStyle }, maskMotionRef) => {
                        const maskClassName = classNames(combinedWrapperClassName, maskMotionClassName);
                        const combinedMaskClassName = modalClassNames?.mask ? `${maskClassName} ${modalClassNames.mask}` : maskClassName;
                        
                        return (
                            <div
                                tabIndex={-1}
                                className={combinedMaskClassName}
                                style={{ ...maskMotionStyle, ...wrapStyle }}
                                onClick={this.handleMaskClick}
                                onKeyDown={this.onWrapperKeyDown}
                                ref={node => {
                                    this.wrapperRef = node;
                                    if (typeof maskMotionRef === 'function') {
                                        maskMotionRef(node);
                                    } else if (maskMotionRef && 'current' in maskMotionRef) {
                                        (maskMotionRef as any).current = node;
                                    }
                                }}
                            >
                                <CSSMotion
                                    // key="mask"
                                    visible={visible}
                                    motionName={closeMotion ? `${this.context.prefixCls}-close-motion` : `${this.context.prefixCls}-zoom-motion`}
                                    onAppearStart={this.onPrepare}
                                    onEnterStart={this.onPrepare}
                                    onAppearEnd={() => {
                                        if (closeMotion) return false;
                                        const { afterOpenChange } = this.props;
                                        if (afterOpenChange) {
                                            afterOpenChange(true);
                                        }
                                        return false;
                                    }}
                                    onEnterEnd={() => {
                                        if (closeMotion) return false;
                                        const { afterOpenChange } = this.props;
                                        if (afterOpenChange) {
                                            afterOpenChange(true);
                                        }
                                        return false;
                                    }}
                                    //@ts-ignore
                                    onLeaveEnd={(ele: React.ReactElement, e: any) => {
                                        if (closeMotion) return false;
                                        // 判断动画结束事件触发的是否是我们设置动画的元素
                                        if (e.target !== ele) {
                                            return false;
                                        }
                                        this.setState({
                                            transformOrigin: ''
                                        });
                                        const { afterClose, afterOpenChange, focusTriggerAfterClose } = this.props;

                                        if (focusTriggerAfterClose !== false && this.triggerRef) {
                                            try {
                                                if (document.body.contains(this.triggerRef)) {
                                                    this.triggerRef.focus();
                                                }
                                            } catch (err) {
                                                // 忽略焦点设置错误，不影响主要功能
                                            }
                                        }

                                        if (afterClose) {
                                            afterClose();
                                        }
                                        if (afterOpenChange) {
                                            afterOpenChange(false);
                                        }
                                        return false;
                                    }}
                                    ref={this.modalMotionRef}
                                    forceRender={forceRender}
                                >
                                    {/* @ts-ignore */}
                                    {({ className: motionClassName, style: motionStyle }: any, motionRef: any) => {
                                        const modalNode = (
                                            <div
                                                className={classNames(modalCls, motionClassName)}
                                                {...rest}
                                                style={{ ...motionStyle, ...modalStyle }}
                                                ref={motionRef}
                                            >
                                                <div className={combinedContentClassName} style={styles.content}>
                                                    {this.renderTitle()}
                                                    {this.renderBody()}
                                                    {this.renderFooter()}
                                                    {this.renderClose()}
                                                </div>
                                            </div>
                                        );

                                        return modalRender ? modalRender(modalNode) : modalNode;
                                    }}
                                </CSSMotion>
                            </div>
                        );
                    }}
                </CSSMotion>
            </AdvancedPortal>
        );
    }
}
export default Modal;
