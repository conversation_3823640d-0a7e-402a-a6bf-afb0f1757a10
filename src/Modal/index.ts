/*
 * Modal
 */
import RooModal from './Modal';
import RooConfirm from './Confirm';
import RooWithConfirm from './WithConfirm';
import withApiCompatibility from '../_utils/hoc/withApiCompatibility';

export * from './interface';

const WrappedModal = withApiCompatibility('Modal', RooModal);

type CompoundedComponent = typeof WrappedModal & {
    confirm: typeof RooConfirm;
    WithConfirm: typeof RooWithConfirm;
};

const Modal = WrappedModal as CompoundedComponent;

Modal.confirm = RooConfirm;
Modal.WithConfirm = RooWithConfirm;

export const Confirm = RooConfirm;
export const WithConfirm = RooWithConfirm;
export default Modal;
