import * as React from 'react';
import * as ReactDOM from 'react-dom';
import { disableBodyScroll, enableBodyScroll } from '@roo/roo/_utils/Dom';
import { globalConfig } from '@roo/roo/ConfigProvider';
import { createCompatibleRenderer } from '@roo/roo/_utils/ReactDOMRender';
import { isThenable } from '../_utils/promise';
import Modal from './Modal';
import { ModalBaseProps, ModalProps, ModalConfirmProps, ConfirmCallback, OldModalProps } from './interface';
import withApiCompatibility from '../_utils/hoc/withApiCompatibility';

interface ModalFuncProps extends ModalBaseProps {
    visible: boolean;
    closeWrap: () => void;
    /** 关闭回调: 关闭按钮，遮罩层，右上角 close 图标都会触发此事件 */
    onClose?: (e: React.MouseEvent<HTMLElement> | undefined, type: string) => void | boolean;
    /** 关闭回调: 关闭按钮，遮罩层，右上角 close 图标都会触发此事件 */
    onCancel?: (e: React.MouseEvent<HTMLElement> | undefined, type: string) => void | boolean;
    /** 打开/确认按钮回调 */
    onOpen?: (e: React.MouseEvent<HTMLElement> | undefined) => void | boolean;
    /** 打开/确认按钮回调 */
    onConfirm?: (e: React.MouseEvent<HTMLElement> | undefined) => void | boolean;
}

const ConfirmModal = (props: ModalFuncProps) => {
    const { onClose, onCancel, onOpen, onConfirm, closeWrap, status, ...restProps } = props;

    const handleClose = (e: React.MouseEvent<HTMLElement>, type: string) => {
        if (onClose) {
            const returnClose: Promise<any> | Boolean | void = onClose(e, type);
            if (returnClose === false) return;

            if (isThenable(returnClose)) {
                (returnClose as any)!.then(
                    (resolvedValue: any) => {
                        if (resolvedValue !== false) {
                            closeWrap();
                        }
                    },
                    (error: any) => {
                        console.error(error);
                    },
                );
                return;
            }
            closeWrap();
        }
        if (onCancel) {
            const returnCancel: Promise<any> | Boolean | void = onCancel(e, type);
            if (returnCancel === false) return;
            if (isThenable(returnCancel)) {
                (returnCancel as any)!.then(
                    (resolvedValue: any) => {
                        if (resolvedValue !== false) {
                            closeWrap();
                        }
                    },
                    (error: any) => {
                        console.error(error);
                    },
                );
                return;
            }
            closeWrap();
        }
        if (!onClose && !onCancel) closeWrap();
    };

    const handleOpen = (e: React.MouseEvent<HTMLElement>) => {
        if (onOpen) {
            const returnOpen: Promise<any> | Boolean | void = onOpen(e);
            if (returnOpen === false) return;
            if (isThenable(returnOpen)) {
                (returnOpen as any)!.then(
                    (resolvedValue: any) => {
                        if (resolvedValue !== false) {
                            closeWrap();
                        }
                    },
                    (error: any) => {
                        console.error(error);
                    },
                );
                return;
            }
            closeWrap();
        }
        if (onConfirm) {
            const returnConfirm: Promise<any> | Boolean | void = onConfirm(e);
            if (returnConfirm === false) return;
            if (isThenable(returnConfirm)) {
                (returnConfirm as any)!.then(
                    (resolvedValue: any) => {
                        if (resolvedValue !== false) {
                            closeWrap();
                        }
                    },
                    (error: any) => {
                        console.error(error);
                    },
                );
                return;
            }
            closeWrap();
        }
        if (!onOpen && !onConfirm) closeWrap();
    };
    const relStatue = status || 'normal';
    let isShowCloseButton = !(['success', 'info', 'danger'].indexOf(relStatue) > -1);

    if (restProps.showCancelButton !== undefined) {
        isShowCloseButton = true;
    }

    const currentProps: ModalProps = {
        closable: true,
        // @ts-ignore
        showCloseButton: isShowCloseButton,
        onCancel: handleClose,
        onConfirm: handleOpen,
        status,
        ...restProps,
    };

    return <Modal {...currentProps} />;
};

const WrappedConfirmModal = withApiCompatibility('Modal', ConfirmModal);
// ConfirmModal.defaultProps = {
//     onClose: undefined,
//     onCancel: undefined,
//     onOpen: undefined,
//     onConfirm: undefined,
// };

export default function confirm(config: ModalConfirmProps & OldModalProps): ConfirmCallback {
    let currentConfig = {
        ...config,
        closeWrap: close,
        visible: true,
    };
    const div = document.createElement('div');
    const renderer = createCompatibleRenderer(div);
    document.body.appendChild(div);

    const update = (newConfig: ModalConfirmProps & OldModalProps) => {
        currentConfig = {
            ...currentConfig,
            ...newConfig,
        };
        render(currentConfig);
    };

    function close() {
        currentConfig = {
            ...currentConfig,
            visible: false,
        };
        render(currentConfig);
        destroy();
    }

    function destroy() {
        setTimeout(() => { //动画300ms,延迟卸载销毁
            // const unmountResult = ReactDOM.unmountComponentAtNode(div);
            // if (unmountResult && div.parentNode) {
            //     div.parentNode.removeChild(div);
            // }
            renderer.unmount();
            enableBodyScroll();

            if (currentConfig.afterClose) {
                currentConfig.afterClose();
            }

            if (currentConfig.afterOpenChange) {
                currentConfig.afterOpenChange(false);
            }
        }, 300);
    }

    function render(props: any) {
        const global = globalConfig();
        disableBodyScroll();
        // 使用方法时，没有 React 组件的层级结构，所以挂载在一个独立的容器下。
        const dom = <WrappedConfirmModal {...props} />;
        renderer.render(global?.holderRender ? global.holderRender(dom) : dom);
    }

    render(currentConfig);

    return {
        close,
        update,
    };
}
