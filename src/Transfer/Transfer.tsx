import * as React from 'react';
import * as PropTypes from 'prop-types';
import Button from '@roo/roo/Button';
import { isFunction, isArray } from '@utiljs/is';
import locale from '@roo/roo/locale';
import { GlobalConfigContext } from '@roo/roo/ConfigProvider';
import { TransferProps, OptionKey, TransferState, FieldNameMap, Option } from './interface';
import TransferPanel from './TransferPanel';

class Transfer extends React.Component<TransferProps, TransferState> {
    static contextType?: React.Context<any> | undefined = GlobalConfigContext;

    static propTypes = {
        className: PropTypes.string,
        fieldNames: PropTypes.object,
        data: PropTypes.array,
        targetKeys: PropTypes.array,
        selectedKeys: PropTypes.array,
        filterable: PropTypes.bool,
        filterMethod: PropTypes.func,
        empty: PropTypes.node,
        filterPlaceholder: PropTypes.string,
        titles: PropTypes.arrayOf(PropTypes.node),
        operations: PropTypes.array,
        leftFooter: PropTypes.any,
        rightFooter: PropTypes.any,
        render: PropTypes.func,
        onChange: PropTypes.func,
        onSelectedChange: PropTypes.func,
        listStyle: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),
        operationStyle: PropTypes.object,
        selectAllLabels: PropTypes.array,
        showSelectAll: PropTypes.bool,
        onScroll: PropTypes.func,
        oneWay: PropTypes.bool,
        disabled: PropTypes.bool,
        selectionsIcon: PropTypes.oneOfType([PropTypes.node, PropTypes.bool]),
        pagination: PropTypes.oneOfType([PropTypes.bool, PropTypes.object]),
    };

    static defaultProps = {
        className: '',
        fieldNames: {
            key: 'key',
            label: 'label',
            disabled: 'disabled',
        },
        data: [],
        targetKeys: [],
        selectedKeys: [],
        filterable: false,
        filterMethod: undefined,
        empty: '暂无数据',
        filterPlaceholder: '',
        titles: [],
        operations: [],
        leftFooter: '',
        rightFooter: '',
        render: undefined,
        onChange: undefined,
        onSelectedChange: undefined,
        listStyle: undefined,
        operationStyle: undefined,
        selectAllLabels: [],
        showSelectAll: true,
        onScroll: undefined,
        oneWay: false,
        disabled: false,
        selectionsIcon: false,
        pagination: false,
    };

    constructor(props: TransferProps) {
        super(props);
        this.state = {
            sourceSelectedKeys: [],
            targetSelectedKeys: [],
        };
    }

    static getDerivedStateFromProps(nextProps: TransferProps) {
        if (nextProps.selectedKeys) {
            const targetKeys: OptionKey[] = nextProps.targetKeys || [];
            const isArraySelectedKeys = isArray(nextProps.selectedKeys);
            return {
                sourceSelectedKeys: isArraySelectedKeys ? nextProps.selectedKeys?.filter(key => !targetKeys?.includes(key)) : [],
                targetSelectedKeys: isArraySelectedKeys ? nextProps.selectedKeys?.filter(key => targetKeys?.includes(key)) : [],
            };
        }
        return null;
    }

    getFiled(filedName: keyof FieldNameMap): string {
        return this.props.fieldNames?.[filedName] || filedName;
    }

    get sourceData() {
        const { data, targetKeys = [] } = this.props;
        if (isArray(data)) {
            return data?.filter(item => !(targetKeys as OptionKey[])?.includes(item[this.getFiled('key')]));
        }
        return [];
    }

    get targetData() {
        const { data, targetKeys = [] } = this.props;
        if (isArray(data)) {
            return data?.filter(item => (targetKeys as OptionKey[])?.includes(item[this.getFiled('key')]));
        }
        return [];
    }

    addToRight = () => {
        const {
            sourceSelectedKeys,
            targetSelectedKeys,
        } = this.state;
        const {
            onChange,
            onSelectedChange,
            targetKeys,
        } = this.props;
        if (onChange && isFunction(onChange)) {
            const moveKeys: OptionKey[] = [];
            this.sourceData.forEach(item => {
                if (sourceSelectedKeys.includes(item[this.getFiled('key')]) && !item[this.getFiled('disabled')]) {
                    moveKeys.push(item[this.getFiled('key')]);
                }
            });
            onChange([...(targetKeys || []), ...moveKeys], 'right', moveKeys);
        }
        if (onSelectedChange && isFunction(onSelectedChange)) {
            onSelectedChange([], targetSelectedKeys);
        }
    }

    addToLeft = () => {
        const {
            sourceSelectedKeys,
            targetSelectedKeys,
        } = this.state;
        const {
            onChange,
            onSelectedChange,
        } = this.props;
        if (onChange && isFunction(onChange)) {
            const targetKeys: OptionKey[] = [];
            const moveKeys: OptionKey[] = [];
            this.targetData.forEach(item => {
                if (!targetSelectedKeys.includes(item[this.getFiled('key')])) {
                    targetKeys.push(item[this.getFiled('key')]);
                } else if (item[this.getFiled('disabled')]) {
                    targetKeys.push(item[this.getFiled('key')]);
                } else {
                    moveKeys.push(item[this.getFiled('key')]);
                }
            });
            onChange(targetKeys, 'left', moveKeys);
        }
        if (onSelectedChange && isFunction(onSelectedChange)) {
            onSelectedChange(sourceSelectedKeys, []);
        }
    };

    handleRemoveItem = (items: Option[]) => {
        const { sourceSelectedKeys } = this.state;
        const { onChange, onSelectedChange } = this.props;
        if (onChange && isFunction(onChange)) {
            const moveKeys: OptionKey[] = [];
            const targetKeys: OptionKey[] = [];
            this.targetData.forEach(i => {
                if (
                    !items.map(item => item[this.getFiled('key')]).includes(i[this.getFiled('key')]) ||
                    i[this.getFiled('disabled')]
                ) {
                    targetKeys.push(i[this.getFiled('key')]);
                } else {
                    moveKeys.push(i[this.getFiled('key')]);
                }
            });
            onChange(targetKeys, 'left', moveKeys);
        }
        if (onSelectedChange && isFunction(onSelectedChange)) {
            onSelectedChange(sourceSelectedKeys, []);
        }
    };

    handleListStyle = (direction: 'left' | 'right') => {
        const { listStyle } = this.props;
        if (isFunction(listStyle)) {
            return listStyle({ direction });
        }
        return listStyle ?? {};
    };

    getStatusClass = () => {
        const { status } = this.props;
        if (status === 'error') return `${this.context.prefixCls}-transfer-panel-error`;
        if (status === 'success') return `${this.context.prefixCls}-transfer-panel-success`;
        return '';
    };

    render() {
        const {
            className,
            titles,
            fieldNames,
            filterable,
            filterMethod,
            empty,
            operations,
            render,
            leftFooter,
            rightFooter,
            onSelectedChange,
            filterPlaceholder,
            operationStyle,
            selectAllLabels,
            showSelectAll,
            onScroll,
            onSearch,
            oneWay,
            disabled,
            selectionsIcon,
            pagination,
        } = this.props;
        const { sourceSelectedKeys, targetSelectedKeys } = this.state;
        return (
            <div className={`${this.context.prefixCls}-transfer ${className}`}>
                <TransferPanel
                    className={`${this.context.prefixCls}-transfer-panel-left ${this.getStatusClass()}`}
                    direction="left"
                    data={this.sourceData}
                    selectedKeys={sourceSelectedKeys}
                    otherSelectedKeys={targetSelectedKeys}
                    title={titles?.length ? titles[0] : ''}
                    fieldNames={fieldNames}
                    empty={empty}
                    filterable={filterable}
                    filterMethod={filterMethod}
                    render={render}
                    footer={leftFooter}
                    onSelectedChange={onSelectedChange}
                    filterPlaceholder={filterPlaceholder || (locale.lng('AutoComplete.placeholder') as string)}
                    style={this.handleListStyle('left')}
                    selectAllLabel={selectAllLabels?.[0]}
                    showSelectAll={showSelectAll}
                    onScroll={onScroll}
                    onSearch={onSearch}
                    disabled={disabled}
                    selectionsIcon={selectionsIcon}
                    pagination={pagination}
                />
                <div
                    className={`${this.context.prefixCls}-transfer-operations`}
                    style={operationStyle}
                >
                    <Button
                        icon={`${this.context.direction === 'RTL' ? 'chevron-left-new' : 'chevron-right-new'}`}
                        disabled={!sourceSelectedKeys.length}
                        onClick={this.addToRight}
                    >
                        {operations?.length ? operations[0] : ''}
                    </Button>
                    {oneWay ? null : (
                        <Button
                            icon={`${this.context.direction === 'RTL' ? 'chevron-right-new' : 'chevron-left-new'}`}
                            disabled={!targetSelectedKeys.length}
                            onClick={this.addToLeft}
                        >
                            {operations?.length && operations[1] ? operations[1] : ''}
                        </Button>
                    )}
                </div>
                <TransferPanel
                    className={`${this.context.prefixCls}-transfer-panel-right ${this.getStatusClass()}`}
                    direction="right"
                    data={this.targetData}
                    selectedKeys={targetSelectedKeys}
                    otherSelectedKeys={sourceSelectedKeys}
                    title={titles?.length && titles[1] ? titles[1] : ''}
                    fieldNames={fieldNames}
                    empty={empty}
                    filterable={filterable}
                    filterMethod={filterMethod}
                    render={render}
                    footer={rightFooter}
                    onSelectedChange={onSelectedChange}
                    filterPlaceholder={filterPlaceholder || (locale.lng('AutoComplete.placeholder') as string)}
                    style={this.handleListStyle('right')}
                    selectAllLabel={selectAllLabels?.[1]}
                    showSelectAll={showSelectAll}
                    onScroll={onScroll}
                    onSearch={onSearch}
                    oneWay={oneWay}
                    removeItem={this.handleRemoveItem}
                    disabled={disabled}
                    selectionsIcon={selectionsIcon}
                    pagination={pagination}
                />
            </div>
        );
    }
}

export default Transfer;
