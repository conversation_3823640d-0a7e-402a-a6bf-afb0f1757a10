import * as React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';

import { GlobalConfigContext } from '@roo/roo/ConfigProvider';
import { TimelineItemProps } from './interface';
import withApiCompatibility from '../_utils/hoc/withApiCompatibility';

class TimelineItem extends React.PureComponent<TimelineItemProps> {
    static contextType?: React.Context<any> | undefined = GlobalConfigContext;

    static propTypes = {
        last: PropTypes.bool,
        dot: PropTypes.node,
        className: PropTypes.string,
        children: PropTypes.node,
        classPrefix: PropTypes.string,
        color: PropTypes.string,
        position: PropTypes.oneOf(['left', 'right']),
        align: PropTypes.oneOf(['left', 'right', 'alternate']),
        itemIndex: PropTypes.number,
    };

    static defaultProps = {
        classPrefix: undefined,
        last: false,
        dot: undefined,
        className: '',
        children: null,
        color: undefined,
        position: undefined,
        align: 'left',
        itemIndex: 0,
    };

    render() {
        const {
            children,
            classPrefix = `${this.context.prefixCls}-timeline-item`,
            last,
            className,
            dot,
            time,
            color,
            position,
            align = 'left',
            itemIndex = 0,
            ...rest
        } = this.props;
        const addPrefix = (clx: string) => `${classPrefix}-${clx}`;

        const getAlignClass = () => {
            const finalAlign = ['left', 'right', 'alternate'].includes(align) ? align : 'left';
            const classP = `${this.context.prefixCls}-timeline-item-align-`;
            if (finalAlign === 'alternate') {
                if (position === 'left') {
                    return `${classP}left`;
                }
                if (position === 'right') {
                    return `${classP}right`;
                }
                return itemIndex % 2 === 0 ? `${classP}left` : `${classP}right`;
            }
            if (finalAlign === 'left') {
                return `${classP}left`;
            }
            return `${classP}right`;
        };

        const classes = classNames(classPrefix, className, getAlignClass(), {
            [addPrefix('last')]: last,
        });

        return (
            <li
                className={classes}
                {...rest}
            >
                <span className={addPrefix('tail')} />
                <span
                    className={classNames({
                        [addPrefix('custom-dot')]: !!dot,
                        [addPrefix('dot')]: !dot,
                    })}
                    style={{ backgroundColor: color }}
                >
                    {dot}
                </span>
                <div className={addPrefix('time')}>{time}</div>
                <div className={addPrefix('content')}>{children}</div>
            </li>
        );
    }
}

export default withApiCompatibility('Timeline', TimelineItem);
