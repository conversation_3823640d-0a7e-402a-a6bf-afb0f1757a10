import * as React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import some from 'lodash/some';

import { GlobalConfigContext } from '@roo/roo/ConfigProvider';
import { isArray } from '@utiljs/is';
import Icon from '@roo/roo/Icon';
import TimelineItem from './TimelineItem';
import { TimelineProps, TimelineItemProps } from './interface';

class Timeline extends React.Component<TimelineProps> {
    static propTypes = {
        className: PropTypes.string,
        // classPrefix: PropTypes.string,
        children: PropTypes.node,
        align: PropTypes.oneOf(['left', 'right', 'alternate']),
        items: PropTypes.array,
        reverse: PropTypes.bool,
        pending: PropTypes.node,
        pendingDot: PropTypes.node,
    };

    static contextType?: React.Context<any> | undefined = GlobalConfigContext;

    static defaultProps = {
        align: undefined,
        // classPrefix: 'timeline',
        className: '',
        children: null,
        items: undefined,
        reverse: false,
        pending: null,
        pendingDot: <Icon name="loading" />,
    };

    static Item = TimelineItem;

    render() {
        const {
            children,
            className,
            align = this.context.direction === 'RTL' ? 'right' : 'left',
            items,
            reverse,
            pending,
            pendingDot,
            ...rest
        } = this.props;

        const getWithTime = () => {
            if (items && isArray(items)) {
                return items.some(v => !!v.time || !!v.label);
            }
            return some(
                React.Children.toArray(
                    children as {
                        props: TimelineItemProps;
                    },
                ),
                ({ props }: React.Component<TimelineItemProps>) => !!props.time || !!props.label,
            );
        };

        const withTime = getWithTime();

        const getItems = () => {
            const count = React.Children.count(children);
            if (items && isArray(items)) {
                return items.map((v, i) => ({
                    ...v,
                    last: pending ? false : i + 1 === items.length,
                    align,
                    itemIndex: i,
                }));
            }
            return React.Children.toArray(children).map((ele, i) => ({
                ...((ele as React.ReactElement)?.props ?? {}),
                children: (ele as React.ReactElement)?.props?.children ?? '',
                last: pending ? false : i + 1 === count,
                align,
                itemIndex: i,
            }));
        };

        const mergeItems = getItems();

        if (pending) {
            mergeItems.push({
                dot: pendingDot,
                children: typeof pending === 'boolean' ? null : pending,
                align,
            });
        }

        if (reverse) {
            mergeItems.reverse();
        }

        return (
            <ul
                className={classNames(
                    `${this.context.prefixCls}-timeline`,
                    `${this.context.prefixCls}-timeline-align-${
                        ['left', 'right', 'alternate'].includes(align) ? align : 'left'
                    }`,
                    {
                        [`${this.context.prefixCls}-timeline-with-time`]: withTime,
                    },
                    className,
                )}
                {...rest}
            >
                {mergeItems.map((item, idx) => (
                    <TimelineItem
                        {...item}
                        key={idx}
                    />
                ))}
            </ul>
        );
    }
}

export default Timeline;
