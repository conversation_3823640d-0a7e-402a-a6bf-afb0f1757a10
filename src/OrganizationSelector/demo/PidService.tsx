import React from 'react';
import axios from 'axios';
import <PERSON><PERSON> from 'mockjs';
import { OrganizationSelector } from '@roo/roo'
const data = {
    "list": [
        {
            "orgType": 20,
            "editable": true,
            "id": 2132,
            "unreal": 0,
            "parentId": -100,
            "level": 1,
            "source": 1,
            "isSelected": false,
            "isLeaf": 0,
            "name": "Mock数据外卖总部",
            "bizType": 1,
            "childrenList": [
                {
                    "orgType": 21,
                    "editable": true,
                    "id": 2133,
                    "unreal": 0,
                    "parentId": 2132,
                    "level": 2,
                    "source": 1,
                    "isSelected": false,
                    "isLeaf": 0,
                    "name": "华北区域",
                    "bizType": 1,
                    "childrenList": [
                        {
                            "orgType": 22,
                            "editable": true,
                            "id": 2134,
                            "unreal": 0,
                            "parentId": 2133,
                            "level": 3,
                            "source": 1,
                            "isSelected": false,
                            "isLeaf": 0,
                            "name": "北京分部",
                            "bizType": 1,
                            "childrenList": [
                                {
                                    "orgType": 23,
                                    "editable": true,
                                    "id": 2135,
                                    "unreal": 0,
                                    "parentId": 2134,
                                    "level": 4,
                                    "source": 1,
                                    "isSelected": false,
                                    "isLeaf": 1,
                                    "name": "朝阳区团队",
                                    "bizType": 1,
                                    "childrenList": null,
                                    "isManager": false,
                                    "orgLevel": null
                                },
                                {
                                    "orgType": 23,
                                    "editable": true,
                                    "id": 2136,
                                    "unreal": 0,
                                    "parentId": 2134,
                                    "level": 4,
                                    "source": 1,
                                    "isSelected": false,
                                    "isLeaf": 1,
                                    "name": "海淀区团队",
                                    "bizType": 1,
                                    "childrenList": null,
                                    "isManager": false,
                                    "orgLevel": null
                                }
                            ],
                            "isManager": false,
                            "orgLevel": null
                        },
                        {
                            "orgType": 22,
                            "editable": true,
                            "id": 2137,
                            "unreal": 0,
                            "parentId": 2133,
                            "level": 3,
                            "source": 1,
                            "isSelected": false,
                            "isLeaf": 1,
                            "name": "天津分部",
                            "bizType": 1,
                            "childrenList": null,
                            "isManager": false,
                            "orgLevel": null
                        }
                    ],
                    "isManager": false,
                    "orgLevel": null
                },
                {
                    "orgType": 21,
                    "editable": true,
                    "id": 2138,
                    "unreal": 0,
                    "parentId": 2132,
                    "level": 2,
                    "source": 1,
                    "isSelected": false,
                    "isLeaf": 0,
                    "name": "华南区域",
                    "bizType": 1,
                    "childrenList": [
                        {
                            "orgType": 22,
                            "editable": true,
                            "id": 2139,
                            "unreal": 0,
                            "parentId": 2138,
                            "level": 3,
                            "source": 1,
                            "isSelected": false,
                            "isLeaf": 1,
                            "name": "深圳分部",
                            "bizType": 1,
                            "childrenList": null,
                            "isManager": false,
                            "orgLevel": null
                        },
                        {
                            "orgType": 22,
                            "editable": true,
                            "id": 2140,
                            "unreal": 0,
                            "parentId": 2138,
                            "level": 3,
                            "source": 1,
                            "isSelected": false,
                            "isLeaf": 1,
                            "name": "广州分部",
                            "bizType": 1,
                            "childrenList": null,
                            "isManager": false,
                            "orgLevel": null
                        }
                    ],
                    "isManager": false,
                    "orgLevel": null
                }
            ],
            "isManager": false,
            "orgLevel": null
        },
        {
            "orgType": 26,
            "editable": true,
            "id": 15998,
            "unreal": 0,
            "parentId": -100,
            "level": 1,
            "source": 4,
            "isSelected": false,
            "isLeaf": 0,
            "name": "Mock数据大连锁总部",
            "bizType": 4,
            "childrenList": [
                {
                    "orgType": 27,
                    "editable": true,
                    "id": 15999,
                    "unreal": 0,
                    "parentId": 15998,
                    "level": 2,
                    "source": 4,
                    "isSelected": false,
                    "isLeaf": 1,
                    "name": "连锁运营部",
                    "bizType": 4,
                    "childrenList": null,
                    "isManager": false,
                    "orgLevel": null
                },
                {
                    "orgType": 27,
                    "editable": true,
                    "id": 16000,
                    "unreal": 0,
                    "parentId": 15998,
                    "level": 2,
                    "source": 4,
                    "isSelected": false,
                    "isLeaf": 1,
                    "name": "品牌管理部",
                    "bizType": 4,
                    "childrenList": null,
                    "isManager": false,
                    "orgLevel": null
                }
            ],
            "isManager": false,
            "orgLevel": null
        },
        {
            "orgType": 6,
            "editable": true,
            "id": 579,
            "unreal": 0,
            "parentId": -100,
            "level": 1,
            "source": 6,
            "isSelected": false,
            "isLeaf": 0,
            "name": "Mock数据渠道总部",
            "bizType": 6,
            "childrenList": [
                {
                    "orgType": 7,
                    "editable": true,
                    "id": 580,
                    "unreal": 0,
                    "parentId": 579,
                    "level": 2,
                    "source": 6,
                    "isSelected": false,
                    "isLeaf": 1,
                    "name": "线上渠道部",
                    "bizType": 6,
                    "childrenList": null,
                    "isManager": false,
                    "orgLevel": null
                },
                {
                    "orgType": 7,
                    "editable": true,
                    "id": 581,
                    "unreal": 0,
                    "parentId": 579,
                    "level": 2,
                    "source": 6,
                    "isSelected": false,
                    "isLeaf": 1,
                    "name": "线下渠道部",
                    "bizType": 6,
                    "childrenList": null,
                    "isManager": false,
                    "orgLevel": null
                }
            ],
            "isManager": false,
            "orgLevel": null
        }
    ],
    "isHq": true
}
Mock.mock('/pidservice/mock', 'get', () => {
    return {
        code: 0,
        message: '获取成功',
        data: data
    }
});
const PidService = () => {

    const getMockData = async () => {
        const data: any = await axios.get('/pidservice/mock');
        return data
    }

    return (
        <OrganizationSelector
            placeholder="组织结构选择"
            pidUrl="https://yapi.sankuai.com/mock/2959/uicomponent/api/orgs/getByPid"
            searchUrl="https://yapi.sankuai.com/mock/2959/uicomponent/api/orgs/search"
            params={{
                sources: '1_4_5_6',
                backtrackOrgType: 20,
            }}
            multiple
            pidService={getMockData}
            showSearch={false}
            onValueChange={(values, options, tabIndex) => {
                console.log('点击节点的 level:', options[tabIndex]?.level);
                console.log('完整的节点数据:', options[tabIndex]);
            }}
        />
    );
};
export default PidService;