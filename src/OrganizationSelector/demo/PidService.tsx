import React, { useState } from 'react';
import axios from 'axios';
import Mock from 'mockjs';
import { OrganizationSelector } from '@roo/roo'

// 第一级数据
const levelOneData = {
    "list": [
        {
            "orgType": 20,
            "editable": true,
            "id": 2132,
            "unreal": 0,
            "parentId": -100,
            "level": 1,
            "source": 1,
            "isSelected": false,
            "isLeaf": 0, // 不是叶子节点，可以展开
            "name": "Mock数据外卖总部",
            "bizType": 1,
            "childrenList": null,
            "isManager": false,
            "orgLevel": null
        },
        {
            "orgType": 26,
            "editable": true,
            "id": 15998,
            "unreal": 0,
            "parentId": -100,
            "level": 1,
            "source": 4,
            "isSelected": false,
            "isLeaf": 0, // 不是叶子节点，可以展开
            "name": "Mock数据大连锁总部",
            "bizType": 4,
            "childrenList": null,
            "isManager": false,
            "orgLevel": null
        },
        {
            "orgType": 6,
            "editable": true,
            "id": 579,
            "unreal": 0,
            "parentId": -100,
            "level": 1,
            "source": 6,
            "isSelected": false,
            "isLeaf": 0, // 不是叶子节点，可以展开
            "name": "Mock数据渠道总部",
            "bizType": 6,
            "childrenList": null,
            "isManager": false,
            "orgLevel": null
        }
    ],
    "isHq": true
};

// 第二级数据
const levelTwoData = {
    "list": [
        {
            "orgType": 21,
            "editable": true,
            "id": 3001,
            "unreal": 0,
            "parentId": 2132,
            "level": 2,
            "source": 1,
            "isSelected": false,
            "isLeaf": 0,
            "name": "华北区域",
            "bizType": 1,
            "childrenList": null,
            "isManager": false,
            "orgLevel": null
        },
        {
            "orgType": 21,
            "editable": true,
            "id": 3002,
            "unreal": 0,
            "parentId": 2132,
            "level": 2,
            "source": 1,
            "isSelected": false,
            "isLeaf": 1, // 叶子节点
            "name": "华南区域",
            "bizType": 1,
            "childrenList": null,
            "isManager": false,
            "orgLevel": null
        }
    ],
    "isHq": false
};

// 第三级数据
const levelThreeData = {
    "list": [
        {
            "orgType": 22,
            "editable": true,
            "id": 4001,
            "unreal": 0,
            "parentId": 3001,
            "level": 3,
            "source": 1,
            "isSelected": false,
            "isLeaf": 1, // 叶子节点
            "name": "北京分公司",
            "bizType": 1,
            "childrenList": null,
            "isManager": false,
            "orgLevel": null
        },
        {
            "orgType": 22,
            "editable": true,
            "id": 4002,
            "unreal": 0,
            "parentId": 3001,
            "level": 3,
            "source": 1,
            "isSelected": false,
            "isLeaf": 1, // 叶子节点
            "name": "天津分公司",
            "bizType": 1,
            "childrenList": null,
            "isManager": false,
            "orgLevel": null
        }
    ],
    "isHq": false
};

// Mock不同层级的数据
Mock.mock('/pidservice/mock/level0', 'get', () => ({
    code: 0,
    message: '获取第一级数据成功',
    data: levelOneData
}));

Mock.mock('/pidservice/mock/level1', 'get', () => ({
    code: 0,
    message: '获取第二级数据成功',
    data: levelTwoData
}));

Mock.mock('/pidservice/mock/level2', 'get', () => ({
    code: 0,
    message: '获取第三级数据成功',
    data: levelThreeData
}));

const PidService = () => {
    const [logMessages, setLogMessages] = useState<string[]>([]);

    const addLog = (message: string) => {
        setLogMessages(prev => [...prev, `[${new Date().toLocaleTimeString()}] ${message}`]);
    };

    // 自定义pidService，直接从option中获取level
    const getMockData = async (params: any) => {
        const { parentId, option } = params;
        
        // 从option中直接获取level信息！
        const level = option?.level || 0;
        addLog(`pidService调用 - parentId: ${parentId}, level: ${level}, optionId: ${option?.id || 'undefined'}`);
        
        let apiUrl = '/pidservice/mock/level0'; // 默认第一级
        
        // 根据level决定请求哪个接口
        if (level === 1) {
            apiUrl = '/pidservice/mock/level1';
            addLog(`请求第二级数据，level=${level}`);
        } else if (level === 2) {
            apiUrl = '/pidservice/mock/level2';
            addLog(`请求第三级数据，level=${level}`);
        } else {
            addLog(`请求第一级数据，level=${level}`);
        }
        
        try {
            const response = await axios.get(apiUrl);
            addLog(`接口返回成功，数据条数: ${response.data.data.list.length}`);
            return response;
        } catch (error) {
            addLog(`请求失败: ${error}`);
            return {
                data: {
                    code: -1,
                    message: '请求失败',
                    data: { list: [] }
                }
            };
        }
    };

    return (
        <div style={{ padding: 16 }}>
            <h3>测试通过option获取level</h3>
            
            {/* 日志显示区域 */}
            <div style={{ 
                marginBottom: 16, 
                padding: 12, 
                background: '#f5f5f5', 
                borderRadius: 4,
                maxHeight: 200,
                overflowY: 'auto'
            }}>
                <h4>调用日志:</h4>
                {logMessages.length === 0 ? (
                    <p style={{ color: '#999' }}>暂无日志，请点击组织节点...</p>
                ) : (
                    logMessages.map((msg, index) => (
                        <div key={index} style={{ fontSize: 12, marginBottom: 4, color: '#666' }}>
                            {msg}
                        </div>
                    ))
                )}
                <button 
                    onClick={() => setLogMessages([])}
                    style={{ marginTop: 8, fontSize: 12 }}
                >
                    清空日志
                </button>
            </div>
            
            <OrganizationSelector
                placeholder="组织结构选择（点击节点查看level获取效果）"
                pidUrl="https://yapi.sankuai.com/mock/2959/uicomponent/api/orgs/getByPid"
                searchUrl="https://yapi.sankuai.com/mock/2959/uicomponent/api/orgs/search"
                params={{
                    sources: '1_4_5_6',
                    backtrackOrgType: 20,
                }}
                multiple
                pidService={getMockData}
                showSearch={false}
                defaultLevelId={-100}
                asyncFetchTree={true}
            />
        </div>
    );
};

export default PidService;