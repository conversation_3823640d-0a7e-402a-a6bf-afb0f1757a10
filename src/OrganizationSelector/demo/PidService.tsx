import React from 'react';
import axios from 'axios';
import <PERSON><PERSON> from 'mockjs';
import { OrganizationSelector } from '@roo/roo'
const data = {
    "list": [
        {
            "orgType": 20,
            "editable": true,
            "id": 2132,
            "unreal": 0,
            "parentId": -100,
            "level": 1,
            "source": 1,
            "isSelected": false,
            "isLeaf": 1,
            "name": "Mock数据外卖总部",
            "bizType": 1,
            "childrenList": null,
            "isManager": false,
            "orgLevel": null
        },
        {
            "orgType": 26,
            "editable": true,
            "id": 15998,
            "unreal": 0,
            "parentId": -100,
            "level": 1,
            "source": 4,
            "isSelected": false,
            "isLeaf": 1,
            "name": "Mock数据大连锁总部",
            "bizType": 4,
            "childrenList": null,
            "isManager": false,
            "orgLevel": null
        },
        {
            "orgType": 6,
            "editable": true,
            "id": 579,
            "unreal": 0,
            "parentId": -100,
            "level": 1,
            "source": 6,
            "isSelected": false,
            "isLeaf": 1,
            "name": "Mock数据渠道总部",
            "bizType": 6,
            "childrenList": null,
            "isManager": false,
            "orgLevel": null
        }
    ],
    "isHq": true
}
Mock.mock('/pidservice/mock', 'get', () => {
    return {
        code: 0,
        message: '获取成功',
        data: data
    }
});
const PidService = () => {

    const getMockData = async () => {
        const data: any = await axios.get('/pidservice/mock');
        return data
    }

    return (
        <OrganizationSelector
            placeholder="组织结构选择"
            pidUrl="https://yapi.sankuai.com/mock/2959/uicomponent/api/orgs/getByPid"
            searchUrl="https://yapi.sankuai.com/mock/2959/uicomponent/api/orgs/search"
            params={{
                sources: '1_4_5_6',
                backtrackOrgType: 20,
            }}
            multiple
            pidService={getMockData}
            showSearch={false}
            onValueChange={(values, options, tabIndex) => {
                console.log('点击节点的 level:', options[tabIndex]?.level);
                console.log('完整的节点数据:', options[tabIndex]);
            }}
        />
    );
};
export default PidService;