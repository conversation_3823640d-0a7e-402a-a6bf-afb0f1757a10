import * as React from 'react';
import * as PropTypes from 'prop-types';
import Tooltip from '@roo/roo/Tooltip';
import Button from '@roo/roo/Button';
import locale from '@roo/roo/locale';
import classNames from 'classnames';
import { isBoolean, isFunction } from '@utiljs/is';
import { GlobalConfigContext } from '@roo/roo/ConfigProvider';
import { executeFoo } from '../_utils';
import { PopConfirmProps } from './interface';
import { innerPlacement } from '../_utils/types';

interface PState {
    visible: boolean;
}

class PopConfirm extends React.Component<PopConfirmProps, PState> {
    static contextType?: React.Context<any> | undefined = GlobalConfigContext;

    static defaultProps = {
        title: undefined,
        icon: undefined,
        visible: undefined,
        effect: 'light',
        trigger: 'click',
        className: undefined,
        disabled: false,
        disablePortal: false,
        cancelBtnProps: undefined,
        confirmBtnProps: undefined,
        cancelText: undefined,
        confirmText: undefined,
        showCancel: true,
        placement: 'top',
        defaultVisible: false,
        size: undefined,
        onPopupClick: undefined,
        okType: 'normal',
    }

    static propTypes = {
        title: PropTypes.node,
        icon: PropTypes.node,
        visible: PropTypes.bool,
        effect: PropTypes.oneOf(['light', 'dark']),
        trigger: PropTypes.oneOf(['hover', 'click']),
        className: PropTypes.string,
        disabled: PropTypes.bool,
        disablePortal: PropTypes.bool,
        cancelBtnProps: PropTypes.object,
        confirmBtnProps: PropTypes.object,
        confirmText: PropTypes.string,
        cancelText: PropTypes.string,
        placement: PropTypes.oneOf(innerPlacement),
        defaultVisible: PropTypes.bool,
        showCancel: PropTypes.bool,
        size: PropTypes.oneOf(['normal', 'compact']),
        onPopupClick: PropTypes.func,
        okType: PropTypes.oneOf(['normal', 'brand', 'text', 'hollow', 'weaken', 'dashed', 'brand-hollow', 'brand-text']),
    }

    state = {
        visible: (isBoolean(this.props.defaultVisible) ? this.props.defaultVisible : this.props.visible) || false,
    }

    static getDerivedStateFromProps(nextProps: PopConfirmProps, preState: PState) {
        if (preState.visible !== nextProps.visible && nextProps.visible !== undefined) {
            return {
                visible: nextProps.visible
            };
        }
        return null;
    }

    get isControlled() { return this.props.visible !== undefined; }

    handleVisibleChange = (s: boolean, cb?: (() => void) | undefined) => {
        this.setState({
            visible: s
        }, cb);
    }

    handleConfirm = async () => {
        const {
            onConfirm,
        } = this.props;

        if (this.isControlled) {
            await executeFoo(onConfirm)();
        } else {
            await executeFoo(onConfirm)();
            this.setState({ visible: false });
        }
    }

    handleCancel = async () => {
        const {
            onCancel,
        } = this.props;

        if (this.isControlled) {
            await executeFoo(onCancel)();
        } else {
            await executeFoo(onCancel)();
            this.setState({ visible: false });
        }
    }

    handleTVisibleChange = (s: boolean) => {
        const {
            onVisibleChange
        } = this.props;

        if (this.isControlled) {
            executeFoo(onVisibleChange)(s);
        } else {
            this.handleVisibleChange(s, () => {
                executeFoo(onVisibleChange)(s);
            });
        }
    }

    renderContent = () => {
        const {
            title,
            icon,
            effect,
            content,
            cancelBtnProps,
            confirmBtnProps,
            cancelText,
            confirmText,
            showCancel,
            size = this.context.size || 'normal',
            onPopupClick,
            okType = 'normal',
        } = this.props;
        const type = effect === 'light' ? okType : 'brand';
        const btnSize = size === 'compact' ? 'compact' : 'mini';

        return (
            <div onClick={onPopupClick}>
                <div className={`${this.context.prefixCls}-popconfirm-message`}>
                    {
                        icon ? <span className={`${this.context.prefixCls}-popconfirm-icon`}>{icon}</span> : null
                    }
                    <div className={`${this.context.prefixCls}-popconfirm-text`}>
                        {
                            title
                                ? (
                                    <div className={`${this.context.prefixCls}-popconfirm-title ${this.context.prefixCls}-popconfirm-${effect}-title`}>{isFunction(title) ? title() : title}</div>
                                )
                                : null
                        }
                        <div className={`${this.context.prefixCls}-popconfirm-${effect}-content`}>{ isFunction(content) ? content() : content }</div>
                    </div>
                </div>
                <div className={`${this.context.prefixCls}-popconfirm-btn ${this.context.prefixCls}-popconfirm-${effect}-btn`}>
                    {showCancel ? (
                        <Button
                            size={btnSize}
                            type="weaken"
                            onClick={this.handleCancel}
                            {...cancelBtnProps}
                        >
                            {cancelText || locale.lng('Modal.closeText')}
                        </Button>
                    ) : null}
                    <span>&nbsp;&nbsp;</span>
                    <Button
                        size={btnSize}
                        type={type}
                        onClick={this.handleConfirm}
                        {...confirmBtnProps}
                    >
                        {confirmText || locale.lng('Modal.openText')}
                    </Button>
                </div>
            </div>
        );
    }

    render(): React.ReactNode {
        const {
            children,
            effect,
            className,
            disabled,
            placement,
            disablePortal,
            // @ts-ignore FIXME: hover 时状态变化多次
            trigger,
            size = this.context.size || 'normal',
        } = this.props;

        if (disabled) {
            return children;
        }
        const clx = classNames(`${this.context.prefixCls}-popconfirm`, className, `${this.context.prefixCls}-popconfirm-${size}`);

        return (
            <Tooltip
                effect={effect}
                popupClassName={clx}
                trigger={trigger}
                placement={placement}
                disablePortal={disablePortal}
                manual
                content={this.renderContent()}
                visible={this.state.visible}
                onVisibleChange={this.handleTVisibleChange}
            >
                { React.Children.only(children) }
            </Tooltip>
        );
    }
}

export default PopConfirm;
