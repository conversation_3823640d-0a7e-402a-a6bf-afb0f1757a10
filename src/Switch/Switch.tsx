import * as React from 'react';
import * as PropTypes from 'prop-types';
import classnames from 'classnames';
import { polyfill } from 'react-lifecycles-compat';
import { isFunction } from '@utiljs/is';
import { GlobalConfigContext } from '@roo/roo/ConfigProvider';
import { SwitchProps } from './interface';
import withDisabled from '../_utils/hoc/withDisabled';

class Switch extends React.Component<SwitchProps, {}> {
    static contextType?: React.Context<any> | undefined = GlobalConfigContext;

    static rooName = 'RSwitch'

    static propTypes = {
        onText: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
        offText: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
        disabled: PropTypes.bool,
        // size: PropTypes.oneOf(['normal', 'mini']),
        checked: PropTypes.bool,
        loading: PropTypes.bool,
        readOnly: PropTypes.bool,
        onChange: PropTypes.func,
        defaultChecked: PropTypes.bool,
        value: PropTypes.bool,
        autoFocus: PropTypes.bool,
        defaultValue: PropTypes.bool,
    }

    static defaultProps = {
        onText: '',
        offText: '',
        disabled: false,
        loading: false,
        readOnly: false,
        // size: 'normal',
        checked: undefined,
        defaultChecked: undefined,
        onChange: () => { },
        value: undefined,
        autoFocus: false,
        defaultValue: undefined,
    }

    state = {
        // checked: this.props.checked === undefined ? !!this.props.defaultChecked : this.props.checked,
        checked: this.props.value || this.props.checked || this.props.defaultValue || this.props.defaultChecked,
    }

    /**
     * 非受控时可以使用该值访问 input
     */
    inputNode: HTMLInputElement | null = null;

    switchRef=React.createRef<HTMLDivElement>()

    static getDerivedStateFromProps(nextProps: SwitchProps) {
        if (nextProps.value !== undefined) {
            return {
                checked: nextProps.value,
            };
        } else if (nextProps.checked !== undefined) {
            return {
                checked: nextProps.checked,
            };
        }
        return {};
    }

    componentDidMount() {
        // 获取焦点
        if (this.props.autoFocus) {
            // eslint-disable-next-line no-unused-expressions
            this.switchRef.current?.focus();
        }
    }

    getInput = (ref: HTMLInputElement) => {
        if (ref) {
            this.inputNode = ref;
        }
    }

    handleClick = () => {
        const {
            disabled,
            readOnly,
            loading,
            defaultChecked,
            checked,
            onChange,
            value,
            defaultValue,
            onClick,
        } = this.props;

        if (disabled || readOnly || loading) {
            return;
        }

        const nextState = !this.state.checked;
        if ((defaultChecked !== undefined || defaultValue !== undefined) && (checked === undefined && value === undefined)) {
            // 非受控模式传入了 checked 则不改变状态，按照受控逻辑处理
            this.setState({
                checked: nextState,
            });
        }
        if (isFunction(onClick)) {
            onClick(!nextState);
        }
        if (isFunction(onChange)) {
            onChange(nextState);
        }
    }

    render() {
        const {
            onText,
            offText,
            disabled,
            loading,
            size = this.context.size || 'normal',
            autoFocus,
            className,
            style,
        } = this.props;
        return (
            <div
                ref={this.switchRef}
                // 让div可以触发:focus伪类
                /* eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex */
                tabIndex={0}
                className={classnames(`${this.context.prefixCls}-switch`,
                    {
                        [`${this.context.prefixCls}-switch-mini`]: size === 'mini',
                        [`${this.context.prefixCls}-switch-compact`]: size === 'compact',
                        [`${this.context.prefixCls}-switch-loading`]: loading,
                        disabled,
                        focus: autoFocus
                    })}
                onClick={this.handleClick}
            >
                <input
                    type="checkbox"
                    checked={this.state.checked}
                    disabled={disabled}
                    ref={this.getInput}
                    readOnly
                />
                <span
                    className={classnames(`${this.context.prefixCls}-switch-checkbox`, className)}
                    style={style}
                >
                    {
                        this.state.checked && onText ? <span>{onText}</span> : null
                    }
                    {
                        !this.state.checked && offText ? <span>{offText}</span> : null
                    }
                </span>
            </div>
        );
    }
}

polyfill(Switch);
export default withDisabled<typeof Switch>(Switch);
