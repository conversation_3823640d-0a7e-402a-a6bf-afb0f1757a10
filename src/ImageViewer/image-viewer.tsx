import * as React from 'react';
import * as ReactDOM from 'react-dom';
import * as PropTypes from 'prop-types';
import { isString } from '@utiljs/is';
import { GlobalConfigContext } from '@roo/roo/ConfigProvider';
import classnames from 'classnames';
import { ImageViewerProps, ImageDecoratorProps } from './interface';
import Viewer from './viewer';
// import { prefixCls } from './constants';
import { $light } from '../_utils/ThemeColor';
import DEFAULT_IMG from '../assets/images/DEFAULT_IMG.png';

interface IState {
    init: boolean;
    visible: boolean;
    activeControlsIndex: number;
}

const PreviewIcon = () => (
    <svg
        viewBox="64 64 896 896"
        style={{ color: $light }}
        focusable="false"
        className=""
        data-icon="eye"
        width="1em"
        height="1em"
        fill="currentColor"
        aria-hidden="true"
    >
        <path d="M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z" />
    </svg>
);

const DownloadIcon = () => (
    <svg
        viewBox="0 0 20 20"
        style={{ color: $light }}
        focusable="false"
        className=""
        data-icon="eye"
        width="1em"
        height="1em"
        fill="currentColor"
        aria-hidden="true"
    >
        <g
            id="icon/数据录入-下载-文件"
            stroke="none"
            strokeWidth="1"
            fill="none"
            fillRule="evenodd"
            fillOpacity="0.9"
        >
            <path
                d="M5.738,8.678 L9.501,4.914 L9.501,13.5 L10.501,13.5 L10.501,4.914 L14.265,8.678 L14.972,7.971 L10.355,3.354 C10.159,3.158 9.843,3.158 9.648,3.354 L5.03,7.971 L5.738,8.678 Z"
                id="路径"
                fill={$light}
                fillRule="nonzero"
                transform="translate(10.001000, 8.353500) scale(1, -1) translate(-10.001000, -8.353500) "
            />
            <path
                d="M4,13 L4,15 C4,15.552 4.448,16 5,16 L15,16 C15.552,16 16,15.552 16,15 L16,13 L15,13 L15,15 L5,15 L5,13 L4,13 Z"
                id="路径"
                fill={$light}
                fillRule="nonzero"
            />
        </g>
    </svg>
);
class ImageViewer extends React.Component<ImageViewerProps, IState> {
    static contextType?: React.Context<any> | undefined = GlobalConfigContext;

    static defaultProps = {
        visible: undefined,
        className: '',
        onChange: undefined,
        zIndex: 2000,
        drag: true,
        activeIndex: 0,
        onClose: undefined,
        defaultImg: {
            src: DEFAULT_IMG,
            alt: '',
        },
        width: '',
        height: '',
        closeOnBackground: false,
        isCloseMotion: false,
        previewable: true,
        mask: true,
        forceRender: false,
    };

    static propTypes = {
        visible: PropTypes.bool,
        className: PropTypes.string,
        zIndex: PropTypes.number,
        onChange: PropTypes.func,
        drag: PropTypes.bool,
        activeIndex: PropTypes.number,
        onClose: PropTypes.func,
        defaultImg: PropTypes.object,
        width: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
        height: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
        closeOnBackground: PropTypes.bool,
        isCloseMotion: PropTypes.bool,
        previewable: PropTypes.bool,
        mask: PropTypes.bool,
        forceRender: PropTypes.bool,
    };

    private defaultContainer: HTMLElement | null = null;

    constructor(props: ImageViewerProps) {
        super(props);
        let { visible } = props;
        if (visible === undefined) {
            visible = false;
        }
        // 创造一个默认容器
        this.state = {
            init: false,
            visible,
            activeControlsIndex: 0,
        };
        this.defaultContainer = typeof document !== 'undefined' ? document.createElement('div') : null;
    }

    static getDerivedStateFromProps(nextProps: ImageViewerProps, state: IState) {
        const { visible } = nextProps;
        const { init } = state;
        // 兼容受控场景，props显示隐藏状态同步state
        if (typeof visible !== 'undefined') {
            if (visible) {
                return {
                    ...state,
                    visible,
                    init: true,
                };
            }
            if (!visible && init) {
                return {
                    ...state,
                    visible,
                };
            }
        }
        return null;
    }

    componentDidMount() {
        if (!this.props.container) {
            document.body.appendChild(this.defaultContainer as HTMLElement);
        }
    }

    componentDidUpdate(prevProps: ImageViewerProps, prevState: IState) {
        if (prevState.visible !== this.state.visible && this.props.onVisibleChange) {
            this.props.onVisibleChange(this.state.visible, prevState.visible);
        }
    }

    handelDefaultImgPreview(index: number) {
        const { visible } = this.state;
        this.setState({
            visible: !visible,
            init: true,
            activeControlsIndex: index,
        });
    }

    fetchToDataUrl(url: string, options?: any): Promise<string> {
        return fetch(url, options).then(res => {
            if (res.ok && res.status === 200) {
                return res.blob().then(blob => URL.createObjectURL(blob));
            } else {
                // @ts-ignore
                throw new Error(`download url fetch error, ${url}`, { cause: res.statusText });
            }
        });
    }

    quickDownload(url: string, name?: string | null) {
        this.fetchToDataUrl(url)
            .then(imageURL => {
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = imageURL;
                a.download = name || '下载图片';

                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            })
            .catch(e => {
                console.warn('download > failed to download', e);
            });
    }

    handelDefaultDownload(index: number, e: any) {
        e.stopPropagation(); // 阻止冒泡预览
        const { images } = this.props;
        const downloadImage = images[index];
        if (downloadImage && downloadImage.src && isString(downloadImage.src)) {
            this.quickDownload(downloadImage.src);
        }
    }

    handleControlsClose = () => {
        const { visible: initVisible } = this.props;
        if (typeof initVisible === 'undefined') {
            this.setState({
                visible: false,
            });
        }
    };

    imgError = (e: any) => {
        const { defaultImg } = this.props;
        if (defaultImg?.src) {
            const img = e.srcElement ? e.srcElement : e.target;
            img.src = defaultImg?.src;
        }
    };

    // 非受控图片预览
    initImage = () => {
        const { width, height, images, download, previewable } = this.props;
        if (!images?.length) return null;

        const prefixCls = `${this.context.prefixCls}-image-viewer`;

        return images?.map((item: ImageDecoratorProps, index: number) => (
            <div
                className={`${prefixCls}-img`}
                style={{ width, height }}
                key={`${index}-idx`}
                onClick={() => previewable && this.handelDefaultImgPreview(index)}
            >
                <img
                    className={`${prefixCls}-image-img`}
                    style={{ height }}
                    src={item.src}
                    alt={item.alt}
                    onError={this.imgError}
                />
                {previewable ? (
                    <div className={`${prefixCls}-image-mask`}>
                        <div className={`${prefixCls}-image-mask-info`}>
                            <span className={classnames('icon-eye', `${prefixCls}-icon-eyes`)}>
                                <PreviewIcon />
                            </span>
                            {download && (
                                <span
                                    className={`${prefixCls}-icon-down`}
                                    onClick={e => this.handelDefaultDownload(index, e)}
                                >
                                    <DownloadIcon />
                                </span>
                            )}
                        </div>
                    </div>
                ) : null}
            </div>
        ));
    };

    render() {
        const { init, visible, activeControlsIndex } = this.state;
        const { visible: initVisible, forceRender, ...restProps } = this.props;
        const isControls = typeof initVisible === 'undefined';
        const activeIndexDefault = isControls ? { activeIndex: activeControlsIndex } : {};
        const viewerProps = { ...restProps, ...activeIndexDefault, forceRender, visible };
        return (
            <>
                {isControls && this.initImage()}
                {(init || forceRender)
                    ? ReactDOM.createPortal(
                        <Viewer
                            {...viewerProps}
                            onControlsClose={this.handleControlsClose}
                        />,
                        this.props.container || (this.defaultContainer as HTMLElement),
                    )
                    : null}
            </>
        );
    }
}

export default ImageViewer;
