import * as React from 'react';
import * as PropTypes from 'prop-types';
import pickBy from 'lodash/pickBy';
import { BoxProps } from './interface';

const prefixIE = (flex: string | undefined) => {
    if (/MSIE|Trident/.test(window.navigator.userAgent) && (flex === 'flex' || flex === 'inline-flex')) {
        return `-ms-${flex}`;
    }
    return flex;
};

const gapSizeMap = {
    small: '8px',
    middle: '16px',
    large: '24px',
};

class Box extends React.PureComponent<BoxProps> {
    static propTypes = {
        htmlType: PropTypes.string,
        display: PropTypes.string,
        alignItems: PropTypes.string,
        justifyContent: PropTypes.string,
        gap: PropTypes.oneOfType([PropTypes.oneOf(['small', 'middle', 'large']), PropTypes.string, PropTypes.number]),
    };

    static defaultProps = {
        htmlType: 'div',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'flex-start',
        gap: undefined,
    };

    render() {
        const {
            htmlType,
            alignContent,
            alignItems,
            alignSelf,
            flex,
            flexBasis,
            flexDirection,
            flexFlow,
            flexGrow,
            flexShrink,
            flexWrap,
            justifyContent,
            order,
            className,
            children,
            display,
            color,
            backgroundColor,
            padding,
            margin,
            width,
            height,
            style,
            onClick,
            gap,
        } = this.props;

        const gapValue = (() => {
            if (typeof gap === 'number') {
                return `${gap}px`;
            } else if (gapSizeMap[gap as keyof typeof gapSizeMap]) {
                return gapSizeMap[gap as keyof typeof gapSizeMap];
            } else {
                return gap;
            }
        })();

        const flexStyle: React.CSSProperties = {
            display: prefixIE(display),
            alignContent,
            justifyContent,
            alignItems,
            alignSelf,
            flexBasis,
            flexFlow,
            flexDirection,
            flexWrap,
            flex,
            flexGrow,
            flexShrink,
            order,
            color,
            backgroundColor,
            padding,
            margin,
            width,
            height,
            gap: gapValue,
            ...style,
        };

        return React.createElement(htmlType, {
            className,
            style: pickBy(flexStyle, value => value !== undefined && value !== null),
            onClick
        },
        children);
    }
}

export default Box;
