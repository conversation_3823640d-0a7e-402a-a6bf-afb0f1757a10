/**
 * https://rsuitejs.com/en/components/multi-cascader
 * MIT license
 */
/*
 * MultiCascader
 */
import OriginalMultiCascader from './MultiCascader';
import withApiCompatibility from '../_utils/hoc/withApiCompatibility';

export * from './interface';

const MultiCascader = withApiCompatibility(
    'MultiCascader',
    OriginalMultiCascader as any,
) as unknown as typeof OriginalMultiCascader;

export default MultiCascader;
