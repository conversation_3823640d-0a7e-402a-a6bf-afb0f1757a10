import * as React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import isUndefined from 'lodash/isUndefined';
import get from 'lodash/get';
import omit from 'lodash/omit';
import pick from 'lodash/pick';

import { polyfill } from 'react-lifecycles-compat';
import Checkbox from '@roo/roo/CheckBox';
import Popover from '@roo/roo/core/Popover';
import Icon from '@roo/roo/Icon';
import Locale from '@roo/roo/locale';

import { isFunction } from '@utiljs/is';
import { GlobalConfigContext } from '@roo/roo/ConfigProvider';
import Empty from '@roo/roo/core/Empty';
import shallowEqualArray from '../_utils/shallowEqualArray';
import { innerPlacement } from '../_utils/types';
import findNodeOfTree from '../_utils/findNodeOfTree';
import { $light } from '../_utils/ThemeColor';

import createUtils, { UtilType, curryPrefix as prefix, getSafeRegExpString } from './utils';
import { batchGetLeafNodes, flattenTree, getNodeParents, getOptionsByKeys, getShowedPane } from './treeUtils';

import DropdownMenu, { dropdownMenuPropTypes } from './DropdownMenu';

import { SearchBar, SelectedElement } from './Picker';

import { CheckedStrategy, ItemType, MultiCascaderProps } from './interface';
import withDisabled from '../_utils/hoc/withDisabled';

interface ChangedObjProps {
    value: any[];
    changedEvent: React.SyntheticEvent<any, Event> | null;
}

let hasChanged = false;
const hasSelectedObj: any = {};
const changedObj: ChangedObjProps = {
    value: [],
    changedEvent: null,
};

interface MultiCascaderState {
    selectNode?: ItemType | null;
    value?: any[];
    prevValue?: any[];
    activePaths?: ItemType[];
    items?: ItemType[][];
    data?: ItemType[];
    flattenData?: ItemType[];
    active?: boolean;
    hover?: boolean;
    innerVisible?: boolean;
    searchKeyword?: string;
    defaultLocale: {
        placeholder: string;
        checkAll: string;
        searchPlaceholder: string;
        noResultsText: string;
        checkAllButtonText: string;
        noData: string;
        showCheckAllText: boolean;
    };
    checkedAll: boolean;
    firstPannelItems: ItemType[];
    searchResultList: ItemType[];
    flattenDataMap: Record<string, ItemType>;
}

class MultiCascader extends React.Component<MultiCascaderProps, MultiCascaderState> {
    static propTypes = {
        cascade: PropTypes.bool,
        data: PropTypes.array,
        disabledItemValues: PropTypes.array,
        className: PropTypes.string,
        menuClassName: PropTypes.string,
        menuStyle: PropTypes.object,
        childrenKey: PropTypes.string,
        valueKey: PropTypes.string,
        labelKey: PropTypes.string,
        disabled: PropTypes.bool,
        value: PropTypes.array,
        defaultValue: PropTypes.array,
        placeholder: PropTypes.string,
        locale: PropTypes.object,
        cleanable: PropTypes.bool,
        visible: PropTypes.bool,
        defaultVisible: PropTypes.bool,
        countable: PropTypes.bool,
        placement: PropTypes.oneOf(innerPlacement),
        style: PropTypes.object,
        uncheckableItemValues: PropTypes.array,
        filterUnCheckableFn: PropTypes.func,
        searchable: PropTypes.oneOfType([PropTypes.bool, PropTypes.object]),
        searchValue: PropTypes.string,
        renderMenuItem: PropTypes.func,
        renderMenu: PropTypes.func,
        renderValue: PropTypes.func,
        renderExtraFooter: PropTypes.func,
        onSearch: PropTypes.func,
        onChange: PropTypes.func,
        onClean: PropTypes.func,
        onSelect: PropTypes.func,
        inline: PropTypes.bool,
        // size: PropTypes.oneOf(['large', 'default', 'small', 'mini']),
        closeable: PropTypes.bool,
        expandedValues: PropTypes.array,
        disablePortal: PropTypes.bool,
        allowCheckAll: PropTypes.bool,
        loadData: PropTypes.func,
        showCheckedStrategy: PropTypes.string,
        defaultExpandedKeys: PropTypes.array,
        noData: PropTypes.object,
        valueAsChain: PropTypes.bool,
        displayRender: PropTypes.func,
        tagRender: PropTypes.func,
        optionRender: PropTypes.func,
        expandIcon: PropTypes.node,
        removeIcon: PropTypes.node,
        suffixIcon: PropTypes.node,
        expandTrigger: PropTypes.oneOf(['hover', 'click']),
        maxTagTextLength: PropTypes.number,
        inputStyle: PropTypes.oneOf(['plaintext', 'line']),
        bordered: PropTypes.bool,
        status: PropTypes.oneOf(['success', 'error', 'normal']),
        popupContainer: PropTypes.func,
        autoClearSearchValue: PropTypes.bool,
        virtual: PropTypes.bool,
        virtualListProps: PropTypes.shape({
            height: PropTypes.number,
            itemHeight: PropTypes.number,
            threshold: PropTypes.string,
        }),
        forceLoadData: PropTypes.bool,
        autoExpandFirstItem: PropTypes.bool,
        isCloseMotion: PropTypes.bool,
        searchInput: PropTypes.object,
        onlyShowLeafWhenSearch: PropTypes.bool,
    };

    static contextType = GlobalConfigContext;
    context!: React.ContextType<typeof GlobalConfigContext>;

    static SHOW_CHILD: CheckedStrategy | undefined = CheckedStrategy.SHOW_CHILD;

    static SHOW_PARENT: CheckedStrategy | undefined = CheckedStrategy.SHOW_PARENT;

    static defaultProps = {
        className: undefined,
        menuClassName: undefined,
        menuStyle: undefined,
        cascade: true,
        data: [],
        value: undefined,
        defaultValue: undefined,
        placeholder: undefined,
        disabledItemValues: [],
        uncheckableItemValues: [],
        filterUnCheckableFn: undefined,
        childrenKey: 'children',
        valueKey: 'value',
        labelKey: 'label',
        locale: {
            placeholder: '',
            checkAll: '',
            searchPlaceholder: '',
            noResultsText: '',
            checkAllButtonText: '',
            showCheckAllText: true,
        },
        cleanable: true,
        searchable: true,
        countable: true,
        disabled: false,
        style: undefined,
        placement: undefined,
        visible: undefined,
        defaultVisible: false,
        // size: 'default',
        renderMenuItem: undefined,
        renderMenu: undefined,
        renderExtraFooter: undefined,
        renderValue: undefined,
        onSearch: undefined,
        onClean: undefined,
        onChange: undefined,
        onSelect: undefined,
        inline: false,
        closeable: false,
        expandedValues: [],
        disablePortal: false,
        allowCheckAll: false,
        loadData: null,
        showCheckedStrategy: 'SHOW_PARENT',
        defaultExpandedKeys: [],
        noData: null,
        valueAsChain: false,
        displayRender: undefined,
        tagRender: undefined,
        optionRender: undefined,
        expandIcon: undefined,
        removeIcon: undefined,
        suffixIcon: undefined,
        expandTrigger: 'click',
        maxTagTextLength: undefined,
        inputStyle: undefined,
        bordered: true,
        status: 'normal',
        popupContainer: undefined,
        autoClearSearchValue: false,
        virtualListProps: undefined,
        virtual: false,
        forceLoadData: false,
        autoExpandFirstItem: false,
        isCloseMotion: false,
        searchInput: undefined,
        onlyShowLeafWhenSearch: false,
        searchValue: undefined,
    };

    static utils: UtilType = {} as UtilType;

    isControlled: boolean | null = null;

    timer: null | number = null;

    menuContainerRef: React.RefObject<DropdownMenu>;

    positionRef: React.RefObject<any>;

    triggerRef: React.RefObject<any>;

    classPrefix: string = '';

    constructor(props: MultiCascaderProps) {
        super(props);

        const {
            data,
            value,
            defaultValue,
            expandedValues: historyDefaultExpandedKeys,
            defaultExpandedKeys,
            childrenKey = 'children',
            valueKey = 'value',
        } = props;
        let expandedValues = defaultExpandedKeys?.length ? defaultExpandedKeys : historyDefaultExpandedKeys;
        MultiCascader.utils = createUtils({ valueKey, childrenKey });
        const { flattenData, flattenDataMap } = flattenTree(
            Array.isArray(data) ? data : [],
            props.childrenKey,
            props.valueKey,
        );

        // 当且仅当defaultExpandedKeys数组长度为1时，需要帮助用户计算父辈结点，其他场景，读取用户设置
        expandedValues =
            expandedValues?.length === 1
                ? MultiCascader.utils
                    .getParents({ item: flattenDataMap[expandedValues[expandedValues.length - 1]], valueKey })
                    .map(item => item?.[valueKey])
                    .concat(expandedValues[expandedValues.length - 1])
                    .filter(Boolean)
                : expandedValues;

        const optionParams = {
            values: expandedValues,
            originList: flattenData,
            valueName: valueKey,
        };
        const initState = {
            data,
            searchKeyword: '',
            prevValue: value,
            value: defaultValue || [],
            selectNode: null,
            innerVisible: false,
            hover: false,
            /**
             * 选中值的路径
             */
            activePaths: getOptionsByKeys(optionParams),
            defaultLocale: {
                placeholder: Locale.lng('MultiCascader.placeholder'),
                checkAll: Locale.lng('MultiCascader.checkAll'),
                searchPlaceholder: Locale.lng('MultiCascader.searchPlaceholder'),
                noResultsText: Locale.lng('MultiCascader.noResultsText'),
                checkAllButtonText: Locale.lng('MultiCascader.checkAllButtonText'),
                noData: Locale.lng('MultiCascader.noData'),
                showCheckAllText: true,
            },
            checkedAll: false,
            searchResultList: [],
        };

        this.isControlled = !isUndefined(value);

        // 判断是否有默认展示项
        const hasExpandedKey = expandedValues && expandedValues.length > 0;
        const showedPaneParams = {
            originList: flattenData,
            expandedValues: expandedValues || [],
            childrenKey,
            valueKey,
        };

        const defautlgetShowedPane = getShowedPane(showedPaneParams);
        const firstPannelItems = flattenData.filter(item => !item.parent);

        this.state = {
            ...initState,
            flattenData,
            flattenDataMap,
            firstPannelItems,
            /**
             * 用于展示面板的数据列表，是一个二维的数组
             * 是通过 data 树结构转换成的二维的数组，其中只包含页面上展示的数据
             */
            items: hasExpandedKey ? defautlgetShowedPane : [firstPannelItems],
            ...MultiCascader.getCascadeState(props, { data: flattenData, dataMap: flattenDataMap }),
        };

        // for test
        this.menuContainerRef = React.createRef();
        this.positionRef = React.createRef();
        this.triggerRef = React.createRef();
    }

    static getCascadeState(
        nextProps: MultiCascaderProps,
        flattenData: { data: ItemType[]; dataMap: Record<string, ItemType> },
        nextValue?: any[],
    ) {
        const {
            data,
            cascade,
            value,
            defaultValue,
            uncheckableItemValues,
            filterUnCheckableFn,
            childrenKey = 'children',
            valueKey = 'value',
            valueAsChain,
        } = nextProps;
        let cascadeValue: any[] = nextValue || value || defaultValue || [];
        let allData = flattenData.data;
        if (cascade && data) {
            const transformValueProps = {
                value: cascadeValue,
                flattenData,
                uncheckableItemValues,
                filterUnCheckableFn,
                childrenKey,
                valueKey,
                valueAsChain,
            };
            allData = data;
            cascadeValue = MultiCascader.utils.transformValue(transformValueProps);
        }
        const allValue: any = (Array.isArray(allData) ? allData : []).map(item => item[valueKey]);

        const isLengthValid = Boolean(cascadeValue.length && allValue.length);
        const isLengthEqual = cascadeValue.length === allValue.length;
        const isFilteredArrayEmpty = cascadeValue.filter((item: string) => allValue.indexOf(item) === -1).length === 0;
        const isEqual = isLengthValid && isFilteredArrayEmpty && isLengthEqual;

        return {
            value: cascadeValue,
            checkedAll: isEqual,
        };
    }

    static getDerivedStateFromProps(nextProps: MultiCascaderProps, prevState: MultiCascaderState) {
        const { data, valueKey = 'value', childrenKey = 'children', searchValue, searchable } = nextProps;

        const value = nextProps.value || prevState.value || [];
        const { prevValue, selectNode = {} as ItemType, items } = prevState;
        let { flattenData = [], firstPannelItems = [], flattenDataMap } = prevState;

        const isChangedData = data !== prevState.data;
        const isChangedValue = !shallowEqualArray(prevValue || [], nextProps.value || []);
        const isChangedSearchValue = searchable && searchValue !== undefined && searchValue !== prevState.searchKeyword;

        if (isChangedData || isChangedValue || isChangedSearchValue) {
            if (isChangedData) {
                const flattenTreeList = flattenTree(data, childrenKey, valueKey);
                flattenData = flattenTreeList.flattenData;
                flattenDataMap = flattenTreeList.flattenDataMap;
                firstPannelItems = flattenData.filter(item => !item.parent);
            }

            /**
             * 如果更新了 data,
             * 首先获取到被点击节点的值 `selectNode`， 然后再拿到新增后的 `newChildren`,
             */
            const nextSelectNode = flattenData.find(n => selectNode && n[valueKey] === selectNode[valueKey]);
            const newChildren = (get(nextSelectNode, childrenKey) || []).map((item: ItemType) => {
                item.parent = nextSelectNode;
                return item;
            });

            if (newChildren.length && items) {
                items[items.length - 1] = newChildren;
            }

            const otherProps = {
                valueKey,
                childrenKey,
                firstPannelItems,
            };
            const nextState: Partial<MultiCascaderState> = {
                selectNode: nextSelectNode,
                flattenData,
                flattenDataMap,
                data,
                firstPannelItems,
                items: MultiCascader.utils.getItems(nextSelectNode, otherProps).filter(i => i?.length),
                ...MultiCascader.getCascadeState(nextProps, { data: flattenData || [], dataMap: flattenDataMap }, value)
            };

            if (isChangedValue) {
                nextState.prevValue = nextProps.value;
            }

            if (isChangedSearchValue) {
                nextState.searchKeyword = searchValue;
            }

            return nextState;
        }

        return null;
    }

    getValue() {
        return this.state.value || [];
    }

    getItemByValue(value: any) {
        const { valueKey = 'value' } = this.props;
        const { flattenData = [] } = this.state;

        let result: ItemType | null = null;

        flattenData.forEach(node => {
            if (node[valueKey] !== value) return;
            result = node;
        });

        return result;
    }

    handleCheck = (itemValue: any, event: React.SyntheticEvent<any>, checked: boolean) => {
        const { flattenData = [] } = this.state;
        const {
            valueKey = 'value',
            onChange,
            cascade,
            uncheckableItemValues = [],
            childrenKey = 'children',
            filterUnCheckableFn,
        } = this.props;
        const item = this.getItemByValue(itemValue);
        let value = [];

        hasChanged = true;

        changedObj.changedEvent = event;
        this.setState({
            selectNode: item,
        });

        if (cascade) {
            if (!item) return;

            const splitValueProps = {
                item,
                checked,
                value: this.getValue(),
                uncheckableItemValues,
                filterUnCheckableFn,
                valueKey,
                childrenKey,
                flattenData,
            };
            value = MultiCascader.utils.splitValue(splitValueProps).value;

            changedObj.value = value;
        } else {
            value = this.getValue();
            let _value;
            if (checked) {
                // 非受控的时候，再改变内部value，受控的时候不改变
                if (!this.isControlled) {
                    value.push(itemValue);
                    _value = value.concat([]);
                } else {
                    _value = value.concat([itemValue]);
                }
            } else {
                if (!this.isControlled) {
                    value = value.filter(n => n !== itemValue);
                }
                _value = value.filter(n => n !== itemValue);
            }

            changedObj.value = _value;
        }

        const selectedoptions = this.getSelectedOptions(changedObj.value);
        const checkedAll = this.getCheckAllState(changedObj.value);
        this.setState({
            checkedAll,
        });
        if (isFunction(onChange)) {
            this.handleChange(changedObj.value, event, selectedoptions);
        }

        if (!this.isControlled) {
            this.setState({
                value,
            });
        }
    };

    handleChange(value: any, event: any, options: any) {
        const {
            onChange,
            showCheckedStrategy,
            valueAsChain,
            valueKey = 'value',
            childrenKey = 'children',
        } = this.props;
        let resultValue = value;
        // 支持同步antd的value类型
        if (valueAsChain) {
            if (showCheckedStrategy === MultiCascader.SHOW_CHILD) {
                resultValue = batchGetLeafNodes(options, childrenKey).map((leafItem: any) => {
                    const parentIds = getNodeParents(leafItem).map((parentItem: any) => parentItem[valueKey]);
                    return parentIds.concat(leafItem[valueKey]);
                });
            } else {
                resultValue = options.map((item: any) => {
                    const parentIds = getNodeParents(item).map((parentItem: any) => parentItem[valueKey]);
                    return parentIds.concat(item[valueKey]);
                });
            }
        }
        // eslint-disable-next-line no-unused-expressions
        onChange?.(resultValue, event, options);
    }

    handleCheckAll = (event: React.ChangeEvent<HTMLInputElement>) => {
        const { onChange, cascade, valueKey = 'value' } = this.props;
        const { data, flattenData } = this.state;
        const checked = event.target.checked;
        const allData = cascade ? data : flattenData;
        const allValue: any = checked ? allData?.map(item => item[valueKey]) : [];
        const selectedoptions = this.getSelectedOptions(allValue);
        // 在受控模式下，我们不直接更新状态，而是调用 onChange
        if (!this.isControlled) {
            this.setState({
                value: allValue,
                checkedAll: checked,
            });
        }
        if (isFunction(onChange)) {
            this.handleChange(allValue, event, selectedoptions);
        }
    };

    getCheckAllState = (value: any[]) => {
        const { cascade, valueKey = 'value' } = this.props;
        const { data, flattenData } = this.state;
        const allData = cascade ? data : flattenData;
        const allValue: any = allData?.map(item => item[valueKey]);
        const isEqual =
            value.filter((item: string) => allValue.indexOf(item) === -1).length === 0 &&
            value.length === allValue.length;
        return isEqual;
    };

    handleChangeForSearchItem = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (this.props.autoClearSearchValue) {
            this.setState({
                searchKeyword: '',
            });
        }
        const value = event.target.value;
        const checked = event.target.checked;
        this.handleCheck(value, event, checked);
    };

    handleSelect = (
        node: ItemType,
        cascadeItems: ItemType[][],
        activePaths: ItemType[],
        event: React.SyntheticEvent<any>,
    ) => {
        const {
            onSelect,
            onChange,
            valueKey = 'value',
            childrenKey = 'children',
            loadData,
            forceLoadData,
        } = this.props;

        this.setState(
            {
                selectNode: node,
                items: cascadeItems,
                activePaths,
            },
            () => {
                if (this.positionRef.current) {
                    this.positionRef.current.updatePosition();
                }
            },
        );

        const createConcatChildrenFunction =
            (nodeItem: ItemType, nodeValue?: ItemType) =>
                (data: ItemType[], children: ItemType[]): ItemType[] => {
                    if (nodeValue) {
                        nodeItem = findNodeOfTree(data, (item: ItemType) => nodeValue === item[valueKey]);
                    }
                    nodeItem[childrenKey] = children;
                    hasSelectedObj[node[valueKey]] = true;

                    if (hasChanged) {
                        hasChanged = false;
                        const { value, changedEvent = null } = changedObj;
                        const { flattenDataMap } = flattenTree(data, childrenKey, valueKey);
                        const selectedoptions = this.getSelectedOptions(value, flattenDataMap);

                        // eslint-disable-next-line
                    this.handleChange(value, changedEvent, selectedoptions);

                    changedObj.changedEvent = null;
                    changedObj.value = [];
                }
                return data.concat([]);
            }

        if (
            isFunction(loadData) &&
            (node.isLeaf === false && (!node[childrenKey]?.length || forceLoadData))
        ) {
            loadData([...getNodeParents(node), node]);
        }
        if (isFunction(onSelect)) {
            onSelect(node, activePaths, createConcatChildrenFunction(node, node[valueKey]), event);
        }
    };

    handleSearch = (searchKeyword: string, event: React.SyntheticEvent<any>) => {
        const { searchValue, searchable } = this.props;

        if (!searchable) return;

        if (searchValue === undefined) {
            this.setState(
                {
                    searchKeyword,
                },
                () => {
                    this.getSearchResult();
                },
            );
        }

        if (this.props.onSearch) {
            this.props.onSearch(searchKeyword, event);
        }
    };

    // 获取选中项数据
    getSelectedOptions = (curValue: any[] = [], flattenDataMap?: Record<string, ItemType>) => {
        const { value, flattenDataMap: flattenDataMapFromState } = this.state;
        const newValue = curValue || value;

        const selectedOptions = newValue.map(item => (flattenDataMap || flattenDataMapFromState)?.[item]);
        return selectedOptions.filter(item => item !== undefined);
    };

    // 获取展开项数据
    // getExpandedOptions = (expandedValues: any[] = []) => {
    //     const { flattenData } = this.state;
    //     const { valueKey = 'value' } = this.props;

    //     const expandedOptions = flattenData?.filter(item => {
    //         const has = expandedValues?.includes(item[valueKey]);

    //         return has;
    //     });

    //     return expandedOptions || [];
    // }

    handleClean = (event: React.SyntheticEvent<any>) => {
        event.stopPropagation();

        const { disabled, onChange, onClean, data } = this.props;
        if (disabled) {
            return;
        }
        const nextState: Partial<MultiCascaderState> = {
            items: [data],
            selectNode: null,
            activePaths: [],
            checkedAll: false,
        };

        if (!this.isControlled) {
            nextState.value = [];
        }

        this.setState(nextState as MultiCascaderState);

        // eslint-disable-next-line
        this.handleChange([], event, []);
        // eslint-disable-next-line
        onClean?.(event);
    };

    handleClose = (event: React.SyntheticEvent<any>, val: any) => {
        event.stopPropagation();

        const { onChange, showCheckedStrategy, childrenKey, valueKey } = this.props;
        const beforeValue = this.getValue();
        const beforeSelectedItems = this.getSelectedOptions(beforeValue);
        let value =
            showCheckedStrategy === MultiCascader.SHOW_PARENT
                ? beforeValue
                : batchGetLeafNodes(beforeSelectedItems, childrenKey).map(item => item[valueKey]);
        value = value.filter(n => n !== val);

        const selectedoptions = this.getSelectedOptions(value);
        const checkedAll = this.getCheckAllState(changedObj.value);
        this.setState({ checkedAll });
        if (!this.isControlled) {
            this.setState({
                value,
            });
        }

        // eslint-disable-next-line
        this.handleChange(value, event, selectedoptions);
    };

    addPrefix = (name: string) => prefix(this.classPrefix)(name);

    async getSearchResult() {
        const {
            labelKey,
            valueKey,
            uncheckableItemValues = [],
            searchable,
            filterUnCheckableFn,
            childrenKey = 'children',
            onlyShowLeafWhenSearch,
        } = this.props;
        const { searchKeyword, flattenData } = this.state;

        const items = [];
        let result: ItemType[] | undefined = flattenData?.filter(item => {
            if (item.checkable === false || uncheckableItemValues.some(value => item[valueKey as string] === value) || filterUnCheckableFn?.(item)) {
                return false;
            }
            if (typeof searchable === 'object' && searchable?.filter) {
                // 如果自定义了过滤函数
                return searchable.filter(searchKeyword || '', [...getNodeParents(item), item]);
            }
            if (item[labelKey as string].match(new RegExp(getSafeRegExpString(searchKeyword as string), 'i'))) {
                return true;
            }
            return false;
        });
        if (typeof searchable === 'object' && isFunction(searchable?.loadData)) {
            // 异步返回数据，再次更新result
            let searchResult = [] as any;
            try {
                searchResult = await searchable.loadData(searchKeyword || '');
            } catch (e) {
                console.log(e);
            }
            result = flattenTree(searchResult, childrenKey, valueKey).flattenData;
        }
        // 是否只展示叶子结点
        if (onlyShowLeafWhenSearch) {
            result = result?.filter(item => item?.isLeaf || !item.children?.length);
        }
        if (result?.length) {
            for (let i = 0; i < result.length; i++) {
                items.push(result![i]);

                if (i === 99) {
                    // return items;
                    break;
                }
            }
        }
        this.setState({
            searchResultList: items,
        });
        return items;
    }

    renderSearchRow = (item: ItemType, key: number) => {
        const {
            labelKey = 'label',
            valueKey = 'value',
            cascade,
            disabledItemValues = [],
            childrenKey = 'children',
            searchable,
        } = this.props;
        const hasCustomeRender = typeof searchable === 'object' && searchable?.render;
        const { searchKeyword, flattenDataMap } = this.state;
        const values = this.getValue();
        const nodes = getNodeParents(item);
        const regx = new RegExp(getSafeRegExpString(searchKeyword || ''), 'ig');
        const labelElements = [];
        const a = item[labelKey].split(regx);
        const b = item[labelKey].match(regx);

        for (let i = 0; i < a.length; i++) {
            labelElements.push(a[i]);
            if (b?.[i]) {
                labelElements.push(
                    <span
                        className={`${this.context.prefixCls}-active-text`}
                        key={i}
                    >
                        {b[i]}
                    </span>,
                );
            }
        }

        nodes.push({
            ...item,
            [labelKey]: hasCustomeRender ? item[labelKey] : labelElements,
        });

        const active = values.some(value => {
            if (cascade) {
                return nodes.some(node => node[valueKey] === value);
            }
            return item[valueKey] === value;
        });
        const disabled = nodes.some(
            node => disabledItemValues?.some(value => node[valueKey] === value) || node.disabled,
        );
        const itemClasses = classNames(this.addPrefix('menu-item'), {
            [this.addPrefix('cascader-row-disabled')]: disabled,
        });
        const otherPropsForUtils = {
            valueKey,
            childrenKey,
            flattenDataMap,
        };

        const defaultRender = (
            <span className={this.addPrefix('cascader-cols')}>
                {nodes.map((node, index) => (
                    <span
                        key={`col-${index}`}
                        className={this.addPrefix('cascader-col')}
                    >
                        {node[labelKey]}
                    </span>
                ))}
            </span>
        );

        return (
            <li
                key={key}
                className={itemClasses}
                title={item[labelKey]}
            >
                <Checkbox
                    disabled={disabled}
                    checked={active}
                    value={item[valueKey]}
                    indeterminate={
                        cascade && !active && MultiCascader.utils.isSomeChildChecked(item, values, otherPropsForUtils)
                    }
                    onChange={this.handleChangeForSearchItem}
                >
                    {hasCustomeRender ? (searchable as any).render(searchKeyword || '', nodes) : defaultRender}
                </Checkbox>
            </li>
        );
    };

    renderSearchResultPanel() {
        const { locale, notFoundContent } = this.props;
        const { searchKeyword, defaultLocale, searchResultList } = this.state;

        if (searchKeyword === '') {
            return null;
        }

        const clx = classNames(
            `${this.context.prefixCls}-picker-search-result ${this.context.prefixCls}-picker-menu ${this.context.prefixCls}-picker-cascader-menu-column`,
        );

        return (
            <div className={clx}>
                {searchResultList.length ? (
                    <ul>{searchResultList.map(this.renderSearchRow)}</ul>
                ) : (
                    <div className={this.addPrefix('none')}>
                        {notFoundContent || locale?.noResultsText || defaultLocale.noResultsText}
                    </div>
                )}
            </div>
        );
    }

    renderDropdownMenu() {
        const {
            items,
            activePaths,
            searchKeyword,
            defaultLocale,
            flattenData = [],
            checkedAll,
            flattenDataMap,
        } = this.state;
        const {
            renderMenu,
            renderExtraFooter,
            menuClassName,
            menuStyle,
            searchable,
            locale,
            inline,
            disabledItemValues = [],
            uncheckableItemValues = [],
            filterUnCheckableFn,
            disabled,
            allowCheckAll,
            noData,
            expandIcon,
            expandTrigger,
            virtual,
            virtualListProps,
            autoExpandFirstItem,
            searchInput,
            optionRender,
        } = this.props;

        const classes = classNames(this.addPrefix('multi-cascader-menu'), menuClassName, {
            [this.addPrefix('inline')]: inline,
        });

        const menuProps = omit(pick(this.props, Object.keys(dropdownMenuPropTypes)), ['className', 'style']);

        const cascaderWrapperStyle = {
            position: 'relative' as 'relative',
            width: '100%',
            top: 'auto',
            left: 'auto',
            backgroundColor: $light,
            ...menuStyle,
        };

        const searchPlaceholder = locale?.searchPlaceholder || defaultLocale?.searchPlaceholder;

        const hasUnCheckableInData = flattenData.some(item => item.checkable === false);
        const hasDisabledInData = flattenData.some(item => item.disabled === true);

        return (
            <div
                className={classes}
                style={cascaderWrapperStyle}
            >
                {/* 查询输入框 */}
                {searchable && (
                    <SearchBar
                        searchInput={searchInput}
                        placeholder={searchPlaceholder}
                        onChange={this.handleSearch}
                        value={searchKeyword}
                    />
                )}

                {searchKeyword === '' && allowCheckAll && (
                    <div
                        className={`${this.context.prefixCls}-multi-cascader-checkall`}
                        // style={{ marginLeft: 10, marginTop: 5 }}
                        title={locale?.checkAllButtonText || defaultLocale?.checkAllButtonText}
                    >
                        <Checkbox
                            onChange={this.handleCheckAll}
                            checked={checkedAll}
                            disabled={
                                !!disabledItemValues?.length ||
                                !!uncheckableItemValues?.length ||
                                !!filterUnCheckableFn ||
                                disabled ||
                                hasDisabledInData ||
                                hasUnCheckableInData
                            }
                            indeterminate={!!this.getValue().length && !this.getCheckAllState(this.getValue())}
                        >
                            {locale?.checkAllButtonText || defaultLocale?.checkAllButtonText}
                        </Checkbox>
                    </div>
                )}

                {/* 查询结果 */}
                {this.renderSearchResultPanel()}
                {/* 下拉选择框 */}
                {searchKeyword === '' && (
                    <DropdownMenu
                        {...menuProps}
                        flattenData={flattenData}
                        classPrefix={this.classPrefix}
                        ref={this.menuContainerRef}
                        cascadeItems={items}
                        cascadePathItems={activePaths}
                        value={this.getValue()}
                        onSelect={this.handleSelect}
                        onCheck={this.handleCheck}
                        renderMenu={renderMenu}
                        noDataRender={noData || <Empty title={locale?.noData || defaultLocale.noData} />}
                        flattenDataMap={flattenDataMap}
                        expandIcon={expandIcon}
                        expandTrigger={expandTrigger}
                        virtual={virtual}
                        virtualListProps={virtualListProps}
                        autoExpandFirstItem={autoExpandFirstItem}
                        optionRender={optionRender}
                    />
                )}

                {/* 弹窗注脚 */}
                {renderExtraFooter?.()}
            </div>
        );
    }

    get isVisibleControlled() {
        const { visible } = this.props;

        return visible !== undefined;
    }

    handleVisibleChange = (s: boolean) => {
        const { onVisibleChange } = this.props;

        this.setState({
            innerVisible: s,
        });
        if (isFunction(onVisibleChange)) {
            onVisibleChange(s);
        }
    };

    handleEnter = () => {
        if (this.props.disabled) return;
        if (!this.props.cleanable) return;
        if (this.timer) {
            window.clearTimeout(this.timer);
        }
        if (this.state.hover) return;

        this.setState({
            hover: true,
        });
    };

    handleExit = () => {
        if (!this.props.cleanable) return;
        if (!this.state.hover) return;

        this.timer = window.setTimeout(() => {
            this.setState({
                hover: false,
            });
        }, 100);
    };

    componentDidUpdate(prevProps: MultiCascaderProps) {
        const { searchable, searchValue } = this.props;
        if (searchable && searchValue !== undefined && prevProps.searchValue !== searchValue) {
            this.getSearchResult();
        }
    }

    componentDidMount() {
        const { searchable, searchValue } = this.props;
        if (searchable && searchValue !== undefined) {
            this.setState({
                searchKeyword: searchValue
            }, () => {
                this.getSearchResult();
            });
        }
    }

    render() {
        // render中获取最新的prefix
        this.classPrefix = `${this.context.prefixCls}-picker`;

        const {
            valueKey = 'value',
            labelKey = 'label',
            childrenKey,
            placeholder,
            renderValue,
            disabled,
            cleanable,
            locale,
            style,
            countable,
            cascade,
            inline,
            visible,
            defaultVisible,
            size = this.context.size && this.context.size !== 'normal' ? this.context.size : 'default',
            className,
            closeable,
            maxTagCount,
            maxTagPlaceholder,
            placement = this.context.direction === 'RTL' ? 'bottomRight' : 'bottomLeft',
            disablePortal,
            showCheckedStrategy,
            showCheckedPath,
            displayRender,
            tagRender,
            maxTagTextLength,
            inputStyle,
            bordered,
            status,
            popupContainer,
            isCloseMotion,
        } = this.props;
        const { innerVisible, hover, defaultLocale } = this.state;

        if (inline) {
            return this.renderDropdownMenu();
        }

        const value = this.getValue();

        const selectedItems = this.getSelectedOptions(value);
        const count = selectedItems.length;
        const hasValue = !!count;

        let selectedElement: React.ReactNode = placeholder || locale?.placeholder || defaultLocale?.placeholder;
        const iconSize = ['default', 'normal', 'large'].includes(size) ? 16 : 14;
        if (count > 0) {
            selectedElement = (
                <SelectedElement
                    disabled={disabled}
                    selectedItems={selectedItems}
                    countable={!!countable}
                    valueKey={valueKey}
                    labelKey={labelKey}
                    childrenKey={childrenKey}
                    prefix={this.addPrefix}
                    cascade={cascade}
                    locale={{ ...defaultLocale, ...locale }}
                    closeable={closeable}
                    handleClose={this.handleClose}
                    maxTagCount={maxTagCount}
                    maxTagPlaceholder={maxTagPlaceholder}
                    showCheckedStrategy={showCheckedStrategy}
                    showCheckedPath={showCheckedPath}
                    size={size}
                    displayRender={displayRender}
                    tagRender={tagRender}
                    maxTagTextLength={maxTagTextLength}
                    inputStyle={inputStyle}
                />
            );
            if (renderValue) {
                selectedElement = renderValue(value, selectedItems, selectedElement);
            }
        }

        const toogleClx = classNames(`${this.context.prefixCls}-multi-cascader-toggle`, className, {
            [`${this.context.prefixCls}-multi-cascader-border-color`]: this.isVisibleControlled
                ? visible
                : innerVisible,
            [`${this.context.prefixCls}-multi-cascader-disabled`]: disabled,
            [`${this.context.prefixCls}-multi-cascader-toggle-${size}`]: size,
            [`${this.context.prefixCls}-multi-cascader-nobordered`]: !bordered,
            [`${this.context.prefixCls}-multi-cascader-line`]: inputStyle === 'line',
            [`${this.context.prefixCls}-multi-cascader-plaintext`]: inputStyle === 'plaintext',
            'has-success-hook': status === 'success',
            'has-error': status === 'error',
        });

        const renderRemoveIcon = () => {
            const { removeIcon } = this.props;
            return (
                <div
                    style={{ display: 'flex' }}
                    onClick={this.handleClean}
                >
                    {removeIcon ?? (
                        <Icon
                            name="times-circle-new"
                            size={iconSize}
                        />
                    )}
                </div>
            );
        };

        return (
            <Popover
                defaultVisible={defaultVisible}
                visible={visible}
                trigger="click"
                placement={placement}
                display="block"
                useClone
                disabled={disabled}
                onVisibleChange={this.handleVisibleChange}
                onMouseEnter={this.handleEnter}
                onMouseLeave={this.handleExit}
                content={this.renderDropdownMenu()}
                disablePortal={disablePortal}
                closeMotion={isCloseMotion}
                container={popupContainer}
            >
                <div
                    className={toogleClx}
                    style={style}
                    onMouseEnter={this.handleEnter}
                    onMouseLeave={this.handleExit}
                >
                    <div className={`${this.context.prefixCls}-multi-cascader-selected`}>
                        {hasValue ? (
                            selectedElement
                        ) : (
                            <span className={`${this.context.prefixCls}-multi-cascader-toggle-placeholder`}>
                                {selectedElement}
                            </span>
                        )}
                    </div>
                    {hover && cleanable ? (
                        renderRemoveIcon()
                    ) : (
                        this.props.suffixIcon ?? (
                            <Icon
                                name="chevron-down-new"
                                size={iconSize}
                            />
                        )
                    )}
                </div>
            </Popover>
        );
    }
}

// @ts-ignore
polyfill(MultiCascader);

export default withDisabled<typeof MultiCascader>(MultiCascader);
