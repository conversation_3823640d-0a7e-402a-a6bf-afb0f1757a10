import * as React from 'react';
import * as PropTypes from 'prop-types';
import { isFunction } from '@utiljs/is';
import { Manager, Reference, PopperPortal } from '@roo/roo/core/Popper';
import { GlobalConfigContext } from '@roo/roo/ConfigProvider';
import {
    effects, placementTypes, TooltipProps, TooltipState, triggers
} from './interface';
import Trigger from './Trigger';
import Popup, { PlacementType } from './Popup';
import { executeFoo } from '../_utils';

class Tooltip extends React.Component<TooltipProps, TooltipState> {
    static contextType?: React.Context<any> | undefined = GlobalConfigContext;

    static propTypes = {
        effect: PropTypes.oneOf(effects),
        content: PropTypes.any,
        trigger: PropTypes.oneOf(triggers),
        placement: PropTypes.oneOf(placementTypes),
        visible: PropTypes.bool,
        defaultVisible: PropTypes.bool,
        manual: PropTypes.bool,
        enterable: PropTypes.bool,
        disabled: PropTypes.bool,
        disablePortal: PropTypes.bool,
        flip: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
        popupContainer: PropTypes.oneOfType([PropTypes.instanceOf(Element), PropTypes.func]),
        className: PropTypes.string,
        onVisibleChange: PropTypes.func,
        closeMotion: PropTypes.bool,
        arrow: PropTypes.bool,
        color: PropTypes.string,
        mouseEnterDelay: PropTypes.number,
        mouseLeaveDelay: PropTypes.number,
        destroyTooltipOnHide: PropTypes.bool,
    }

    static defaultProps = {
        effect: 'dark',
        content: '',
        trigger: 'hover',
        placement: 'top',
        visible: false,
        defaultVisible: false,
        manual: false,
        enterable: true,
        disabled: false,
        disablePortal: false,
        flip: true,
        popupContainer: document.body,
        className: '',
        onVisibleChange: undefined,
        closeMotion: false,
        arrow: true,
        color: '',
        mouseEnterDelay: 0,
        mouseLeaveDelay: 0,
        destroyTooltipOnHide: true,
    }

    state = {
        visible: this.props.defaultVisible || false,
    }

    private target: any;

    static getDerivedStateFromProps(nextProps: TooltipProps) {
        if (nextProps.manual) {
            return {
                visible: nextProps.visible,
            };
        }
        return null;
    }

    // 非受控模式使用componentDidUpdate判断visible值知否改动过来执行onVisibleChange
    componentDidUpdate(prevProps: Readonly<TooltipProps>, prevState: Readonly<TooltipState>) {
        const {
            manual,
            // @ts-ignore 暂时不暴露
            onVisibleChange,
        } = this.props;
        if (!manual && prevState.visible !== this.state.visible) {
            executeFoo(onVisibleChange)(this.state.visible);
        }
    }

    toggleVisible = (v?: boolean) => {
        const {
            manual,
            // @ts-ignore 暂时不暴露
            onVisibleChange,
        } = this.props;
        if (manual) {
            if (isFunction(onVisibleChange)) {
                const t = this.state.visible;
                onVisibleChange(v !== undefined ? v : !t);
            }
            return;
        }
        this.setState(state => ({
            visible: v !== undefined ? v : !state.visible,
        }));
    }

    handleClickOutside = (e: MouseEvent): void => {
        const {
            trigger,
        } = this.props;

        if (trigger === 'click' && this.state.visible && e.target !== this.target) {
            this.toggleVisible(false);
        }
    };

    firePropsEvent = (eventName: string, event: React.MouseEvent<Element, MouseEvent> | React.FocusEvent<Element>): void => {
        const compatibleChildren = typeof (this.props.children) === 'string' ? <span>{this.props.children}</span> : this.props.children; // 兼容用户children为字符串的情况
        const childProps = (compatibleChildren as React.Component<any>).props;
        if (childProps[eventName] && !childProps.disabled && !childProps.readOnly) {
            childProps[eventName](event);
        }
    }

    handleClick = (e: React.MouseEvent<Element, MouseEvent>): void => {
        const { trigger } = this.props;
        if (trigger === 'click') {
            this.target = e.target;
            this.toggleVisible();
        }
        this.firePropsEvent('onClick', e);
    }

    handleMouseEnter = (e: React.MouseEvent<Element, MouseEvent>): void => {
        const { trigger } = this.props;
        if (trigger === 'hover') {
            this.toggleVisible(true);
        }
        this.firePropsEvent('onMouseEnter', e);
    }

    handlePopupMouseEnter = (e: React.MouseEvent<Element, MouseEvent>): void => {
        const { trigger, enterable } = this.props;
        if (!enterable) {
            return;
        }
        if (trigger === 'hover') {
            this.toggleVisible(true);
        }
        this.firePropsEvent('onMouseEnter', e);
    }

    handleMouseLeave = (e: React.MouseEvent<Element, MouseEvent>): void => {
        const { trigger } = this.props;
        if (trigger === 'hover') {
            this.toggleVisible(false);
        }
        this.firePropsEvent('onMouseLeave', e);
    }

    handlePopupMouseLeave = (e: React.MouseEvent<Element, MouseEvent>): void => {
        const { trigger, enterable } = this.props;
        if (!enterable) {
            return;
        }
        if (trigger === 'hover') {
            this.toggleVisible(false);
        }
        this.firePropsEvent('onMouseLeave', e);
    }

    handleFocus = (e: React.FocusEvent<Element>): void => {
        const { trigger } = this.props;
        if (trigger === 'focus') {
            this.toggleVisible(true);
        }
        this.firePropsEvent('onFocus', e);
    }

    handleBlur = (e: React.FocusEvent<Element>): void => {
        const { trigger } = this.props;
        if (trigger === 'focus') {
            this.toggleVisible(false);
        }
        this.firePropsEvent('onBlur', e);
    }

    getPlacement = (placement: string): string => placement.replace('Left', '-start').replace('Right', '-end').replace('Top', '-start').replace('Bottom', '-end');

    render(): React.ReactNode {
        const { visible } = this.state;
        const {
            effect,
            content,
            popupClassName,
            popupStyle,
            className,
            style,
            placement: position,
            children,
            popupContainer,
            disabled,
            disablePortal,
            flip,
            zIndex,
            closeMotion,
            arrow,
            color,
            mouseEnterDelay,
            mouseLeaveDelay,
            destroyTooltipOnHide,
        } = this.props;

        const childrenIsStr = typeof (this.props.children) === 'string';
        if (disabled) {
            return childrenIsStr ? <span>{children}</span> : children;
        }

        let delayFn: any = null;

        return (
            <Manager>
                <Reference>
                    {({ ref }) => (
                        <Trigger
                            innerRef={ref}
                            onMouseEnter={e => {
                                if (delayFn) {
                                    clearTimeout(delayFn);
                                }
                                if (mouseEnterDelay) {
                                    delayFn = setTimeout(() => {
                                        this.handleMouseEnter(e);
                                    }, mouseEnterDelay * 1000);
                                } else {
                                    this.handleMouseEnter(e);
                                }
                            }}
                            onMouseLeave={e => {
                                if (delayFn) {
                                    clearTimeout(delayFn);
                                }
                                if (mouseLeaveDelay) {
                                    delayFn = setTimeout(() => {
                                        this.handleMouseLeave(e);
                                    }, mouseLeaveDelay * 1000);
                                } else {
                                    this.handleMouseLeave(e);
                                }
                            }}
                            onFocus={this.handleFocus}
                            onBlur={this.handleBlur}
                            onClick={this.handleClick}
                            className={className}
                            style={style}
                        >
                            {childrenIsStr ? <span>{children}</span> : children}
                        </Trigger>
                    )}
                </Reference>
                <PopperPortal
                    // destroyTooltipOnHide=false时,保留元素，使用display控制显示隐藏
                    visible={destroyTooltipOnHide === false ? true : visible}
                    modifiers={[{
                        name: 'flip',
                        enabled: flip,
                        options: {
                            flipVariations: flip === 'flip',
                        }
                    }]}
                    disablePortal={disablePortal}
                    container={popupContainer}
                    placement={this.getPlacement(position || 'top')}
                    onClickOutside={this.handleClickOutside}
                    zIndex={zIndex}
                    motionName={closeMotion ? `${this.context.prefixCls}-close-motion` : `${this.context.prefixCls}-popup-fast-motion`}
                    isCloseMotion={closeMotion}
                    style={{ display: visible ? 'block' : 'none' }}
                >
                    {
                        ({ placement, ref }: { placement: PlacementType; ref: React.RefObject<HTMLDivElement> }) => (
                            <Popup
                                innerRef={ref}
                                effect={effect}
                                placement={placement}
                                content={content}
                                popupClassName={popupClassName}
                                popupStyle={popupStyle}
                                onMouseEnter={this.handlePopupMouseEnter}
                                onMouseLeave={this.handlePopupMouseLeave}
                                arrow={arrow}
                                color={color}
                            />
                        )
                    }
                </PopperPortal>
            </Manager>
        );
    }
}

export default Tooltip;
