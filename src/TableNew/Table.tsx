import * as React from 'react';
import classNames from 'classnames';
import Loading, { LoadingProps } from '@roo/roo/Loading';
import Pagination, { sizes, PaginationProps } from '@roo/roo/Pagination';
import omit from 'lodash/omit';
import { isFunction } from '@utiljs/is';
import Table, { VirtualTable, INTERNAL_HOOKS, Summary, EXPAND_COLUMN } from 'rc-table7481';
import type { TableProps as RcTableProps } from 'rc-table7481/lib/Table';
import locale from '@roo/roo/locale';
import { ConfigContext } from '../_utils/context';
import Empty from '../core/Empty';
import scrollTo from '../_utils/scrollTo';
import {
    TableProps,
    ExpandType,
    ExpandableConfig,
    GetRowKey,
    SorterResult,
    FilterValue,
    TableAction,
    ColumnsType,
    ColumnTitleProps,
    Column,
    SetData,
    ILocale,
} from './interface';
import { handleSortColumns, getStorageData } from './util';
import useBreakpoint from './hooks/useBreakpoint';
import useSelection, { SELECTION_COLUMN } from './hooks/useSelection';
import useLazyKVMap from './hooks/useLazyKVMap';
import type { FilterState } from './hooks/useFilter';
import useFilter, { getFilterData } from './hooks/useFilter';
import type { SortState } from './hooks/useSorter';
import useSorter, { getSortData } from './hooks/useSorter';
import useTitleColumns from './hooks/useTitleColumns';
import renderExpandIcon from './ExpandIcon';
import ColumnsSet from './ColumnsSet';
import { GlobalConfigContext } from '../ConfigProvider';

interface ChangeEventInfo<RecordType> {
    pagination: {
        current?: number;
        pageSize?: number;
        total?: number;
    };
    filters: Record<string, FilterValue | null>;
    sorter: SorterResult<RecordType> | SorterResult<RecordType>[];
    filterStates: FilterState<RecordType>[];
    sorterStates: SortState<RecordType>[];
    resetPagination: Function;
}

type TextAlignPosition = 'start' | 'end' | 'left' | 'right' | 'center' | 'justify' | 'match-parent';

function InternalTableNew<RecordType extends object = any>(
    props: TableProps<RecordType>,
    ref: React.MutableRefObject<HTMLDivElement>,
) {
    const layoutValue = React.useContext(GlobalConfigContext);
    const {
        className,
        style,
        bordered,
        hover,
        columns,
        data,
        loading,
        pagination: paginationConfig,
        emptyComponent,
        rowClassName,
        rowKey = 'key',
        scroll,
        rowSelection,
        expandable,
        onChange,
        stripe,
        getPopupContainer = () => document.body, // 设置表格内各类浮层的渲染节点，如筛选菜单
        showSorterTooltip = false,
        setConfig = false,
        localPage = false,
        tableLayout,
        locale: tableLocale,
        size = layoutValue.size || 'normal',
        summary,
        onScroll,
    } = props;
    let paginationSticky = false;
    let pagination = paginationConfig;
    if (typeof paginationConfig === 'boolean') {
        pagination = paginationConfig;
    } else if (paginationConfig) {
        const { sticky, ...rest } = paginationConfig;
        paginationSticky = sticky || false;
        pagination = rest;
    }
    const defaultLocale: ILocale = {
        cancelSort: locale.lng('Table.cancelSort') as string,
        triggerAsc: locale.lng('Table.triggerAsc') as string,
        triggerDesc: locale.lng('Table.triggerDesc') as string,
        filterSearchPlaceholder: locale.lng('Table.filterSearchPlaceholder') as string,
        filterReset: locale.lng('Table.filterReset') as string,
        filterConfirm: locale.lng('Table.filterConfirm') as string,
        filterCheckall: locale.lng('Table.filterCheckall') as string,
        filterCheckNotall: locale.lng('Table.filterCheckNotall') as string,
        filterEmptyText: locale.lng('Table.filterEmptyText') as string,
        noData: locale.lng('Table.noData') as string,
        fixedForbid: locale.lng('Table.fixedForbid') as string,
        fixedCancel: locale.lng('Table.fixedCancel') as string,
        fixedLeft: locale.lng('Table.fixedLeft') as string,
        fixedRight: locale.lng('Table.fixedRight') as string,
        fixedColumn: locale.lng('Table.fixedColumn') as string,
        columnShow: locale.lng('Table.columnShow') as string,
        columnReset: locale.lng('Table.columnReset') as string,
        columnSet: locale.lng('Table.columnSet') as string,
        selectionAll: locale.lng('Table.selectionAll') as string,
        selectInvert: locale.lng('Table.selectInvert') as string,
        selectNone: locale.lng('Table.selectNone') as string,
    };
    const mergeLocale = { ...defaultLocale, ...(tableLocale || {}) };

    // 原始表格数据
    /* istanbul ignore next */
    const rawData: readonly RecordType[] = data || [];
    // 展开数据合并
    const mergedExpandable: any = { ...expandable };
    // 展开数据的子节点key
    const { childrenColumnName = 'children' } = mergedExpandable as ExpandableConfig<RecordType>;
    // 展开类型（nest/row）
    const expandType = React.useMemo<ExpandType>(() => {
        // 表格数据中存在子节点key 展示网状
        if (rawData.some(item => (item as any)?.[childrenColumnName])) return 'nest';
        // 或者有展开函数 expandedRowRender 使用行展示
        if (expandable && expandable.expandedRowRender) return 'row';
        // 都没有时不进行展开
        return null;
    }, [rawData]);
    // table 的 dom
    const internalRefs = { body: React.useRef<HTMLDivElement>() };
    // 处理后的 columns
    //   const baseColumns = React.useMemo(() => columns, [columns]);
    // 过滤无效数据
    const initColumns = columns?.filter(item => item instanceof Object);

    // 处理列的通用函数
    const processColumns = React.useCallback(() => {
        if (typeof setConfig === 'object' && setConfig.storageKey) {
            const dataNew: Array<Column<RecordType>> = [];
            const storageData = getStorageData(`roo-table-setConfig-cols--${setConfig.storageKey}` || '');
            if (storageData?.length) {
                // 根据比较结果赋值
                storageData.forEach(item => {
                    if (item.show) {
                        const itemData = columns?.find(i => i.dataIndex === item.dataIndex);
                        if (itemData) {
                            dataNew.push({
                                ...itemData,
                                fixed: item.fixed,
                            });
                        }
                    }
                });
                return handleSortColumns(dataNew as any);
            }
        }
        if (typeof setConfig === 'object' && setConfig.value) {
            const dataNew: Array<Column<RecordType>> = [];
            setConfig.value.forEach(item => {
                if (item.show) {
                    const itemData = columns?.find(i => i.dataIndex === item.dataIndex);
                    if (itemData) {
                        dataNew.push({
                            ...itemData,
                            fixed: item.fixed,
                        });
                    }
                }
            });
            // 使用 handleSortColumns 处理固定列的顺序
            return handleSortColumns(dataNew as any);
        }
        return handleSortColumns(initColumns as any);
    }, [setConfig, columns]);

    // 初始化使用
    const [baseColumns, setBaseColumns] = React.useState(processColumns());
  
    // 处理响应式
    const needResponsive = React.useMemo(
      () => baseColumns.some((col: any) => col.responsive),
      [baseColumns],
    );
    const screens = useBreakpoint(needResponsive);
    const mergedColumns = React.useMemo(() => {
        // @ts-ignore
        const matched = new Set(Object.keys(screens).filter(m => screens[m]));
        // @ts-ignore
        return baseColumns.filter(c => !c.responsive || c.responsive.some(r => matched.has(r)));
      }, [baseColumns, screens]);
    // 获取行 key
    // @ts-ignore
    const getRowKey = React.useMemo<GetRowKey<RecordType>>(() => {
        // 传入函数直接使用
        if (typeof rowKey === 'function') {
            return rowKey;
        }
        // 传入字符串，获取data的对应值返回
        return (record: RecordType) => (record as any)?.[rowKey as string];
    }, [rowKey]);
    // 通过key拿到对应的record【将 data 数据根据 rowKey 转化为Map结构。 例： { record[rowKey]: record }，key为rowKey对应的value，value: rowKey对应的 record】
    const [getRecordByKey] = useLazyKVMap(rawData, childrenColumnName, getRowKey);

    // ============================ Events =============================
    const changeEventInfo: Partial<ChangeEventInfo<RecordType>> = {};
    const triggerOnChange = (
        info: Partial<ChangeEventInfo<RecordType>>,
        action: TableAction,
        /* istanbul ignore next */
        // @ts-ignore
        reset: boolean = false,
    ) => {
        const changeInfo = { ...changeEventInfo, ...info };
        // 分页事件处理，暂不使用，注释
        // if (reset) {
        //   changeEventInfo.resetPagination!();
        //   // Reset event param
        //   if (changeInfo.pagination!.current) {
        //     changeInfo.pagination!.current = 1;
        //   }
        //   // Trigger pagination events
        //   if (pagination && pagination.onChange) {
        //     pagination.onChange(1, changeInfo.pagination!.pageSize!);
        //   }
        // }

        if (scroll && scroll.scrollToFirstRowOnChange !== false && internalRefs.body.current) {
            scrollTo(0, {
                getContainer: () => internalRefs.body.current!,
            });
        }

        // eslint-disable-next-line no-unused-expressions
        onChange?.(
            changeInfo.sorter!,
            {
                currentDataSource: getFilterData(
                    getSortData(rawData, changeInfo.sorterStates!, childrenColumnName),
                    changeInfo.filterStates!,
                ),
                action,
            },
            changeInfo.filters!,
        );
    };
    const direction = layoutValue.direction === 'RTL' ? 'rtl' : '';

    // 获取公共样式前缀
    const { getPrefixCls } = React.useContext(ConfigContext);
    const prefixCls = getPrefixCls('tableNew');
    const sizeClassMap = {
        large: `${prefixCls}-lg-wrapper`,
        normal: '',
        small: `${prefixCls}-sm-wrapper`,
        mini: `${prefixCls}-xs-wrapper`,
        compact: `${prefixCls}-compact-wrapper`,
    };
    const dropdownPrefixCls = getPrefixCls('tableNew-filter-dropdown');
    const wrapperClassNames = classNames(
        {
            [`${prefixCls}-wrapper`]: true,
            [`${prefixCls}-wrapper-rtl`]: direction === 'rtl',
        },
        className,
    );
    const tableProps = omit(props, ['className', 'style', 'columns']) as TableProps<RecordType>;

    // ============================ Sorter =============================
    const onSorterChange = (
        sorter: SorterResult<RecordType> | SorterResult<RecordType>[],
        sorterStates: SortState<RecordType>[],
    ) => {
        triggerOnChange({ sorter, sorterStates }, 'sort', false);
    };

    const [transformSorterColumns, sortStates, sorterTitleProps, getSorters] = useSorter<RecordType>({
        prefixCls,
        // @ts-ignore
        mergedColumns,
        onSorterChange,
        sortDirections: ['ascend', 'descend'],
        tableLocale: mergeLocale,
        showSorterTooltip,
    });

    const sortedData = React.useMemo(() => getSortData(rawData, sortStates, childrenColumnName), [rawData, sortStates]);

    changeEventInfo.sorter = getSorters();
    changeEventInfo.sorterStates = sortStates;

    // ============================ Filter ============================
    const onFilterChange = (filters: Record<string, FilterValue>, filterStates: FilterState<RecordType>[]) => {
        triggerOnChange({ filters, filterStates }, 'filter', true);
    };

    const [transformFilterColumns, filterStates, filters] = useFilter<RecordType>({
        prefixCls,
        locale: mergeLocale,
        dropdownPrefixCls,
        // @ts-ignore
        mergedColumns,
        // @ts-ignore
        onFilterChange,
        getPopupContainer,
    });

    // 排序及筛选数据合并
    const mergedData = getFilterData(sortedData, filterStates);

    changeEventInfo.filters = filters;
    changeEventInfo.filterStates = filterStates;

    // ============================ Column ============================
    const columnTitleProps = React.useMemo<ColumnTitleProps<RecordType>>(() => {
        const mergedFilters: Record<string, FilterValue> = {};
        Object.keys(filters).forEach(filterKey => {
            if (filters[filterKey] !== null) {
                mergedFilters[filterKey] = filters[filterKey]!;
            }
        });
        return {
            ...sorterTitleProps,
            filters: mergedFilters,
        };
    }, [sorterTitleProps, filters]);

    const [transformTitleColumns] = useTitleColumns(columnTitleProps);

    // ========================== Selections ==========================
    const [transformSelectionColumns, selectedKeySet] = useSelection<RecordType>(rowSelection, {
        prefixCls,
        data: mergedData,
        pageData: mergedData,
        getRowKey,
        getRecordByKey,
        expandType,
        childrenColumnName,
        locale: mergeLocale,
        getPopupContainer,
    });

    // ========================== Expandable ==========================
    (mergedExpandable as any).__PARENT_RENDER_ICON__ = mergedExpandable.expandIcon;

    mergedExpandable.expandIcon = mergedExpandable.expandIcon || renderExpandIcon();

    // Adjust expand icon index, no overwrite expandIconColumnIndex if set.
    if (expandType === 'nest' && mergedExpandable.expandIconColumnIndex === undefined) {
        mergedExpandable.expandIconColumnIndex = rowSelection ? 1 : 0;
    } else if (mergedExpandable.expandIconColumnIndex! > 0 && rowSelection) {
        mergedExpandable.expandIconColumnIndex! -= 1;
    }

    if (typeof mergedExpandable.indentSize !== 'number') {
        mergedExpandable.indentSize = 15;
    }

    // 行样式
    // @ts-ignore
    const internalRowClassName = (record: RecordType, index: number, indent: number) => {
        let mergedRowClassName: string;
        if (typeof rowClassName === 'function') {
            mergedRowClassName = classNames(rowClassName(record, index));
        } else {
            mergedRowClassName = classNames(rowClassName);
        }
        // 行可选自定义是否展示选中样式，默认展示样式
        const isDisplaySelectedRowStyle =
            rowSelection && rowSelection.isDisplaySelectedRowStyle !== undefined
                ? rowSelection.isDisplaySelectedRowStyle
                : true;
        return classNames(
            {
                [`${prefixCls}-row-selected`]:
                    selectedKeySet.has(getRowKey(record, index)) && isDisplaySelectedRowStyle,
            },
            mergedRowClassName,
            { [`${prefixCls}-row--even`]: index % 2 === 1 },
        );
    };

    // Table Loading
    let loadingProps: LoadingProps | undefined;
    if (typeof loading === 'boolean') {
        loadingProps = { visible: loading };
    } else if (typeof loading === 'object') {
        loadingProps = { visible: true, ...loading };
    }

    // Table emptyComponent
    const renderEmptyComponent = () => {
        if (!emptyComponent) return <Empty title={mergeLocale.noData} />;
        return <div style={style}>{isFunction(emptyComponent) ? emptyComponent() : emptyComponent}</div>;
    };
    // column改变，定义渲染行
    const transformColumns = React.useCallback(
        (innerColumns: ColumnsType<RecordType>): ColumnsType<RecordType> =>
            transformTitleColumns(
                transformSelectionColumns(transformFilterColumns(transformSorterColumns(innerColumns))),
            ),
        [transformSorterColumns, transformFilterColumns, transformSelectionColumns],
    );

    const handleChangeValue = (value: SetData[]) => {
        if (!setConfig) return;
        const dataNew: Array<Column<RecordType>> = [];
        value.forEach(item => {
            const itemData = columns.find(i => i.dataIndex === item.dataIndex);
            if (itemData && item.show) {
                dataNew.push({
                    ...itemData,
                    fixed: item.fixed,
                });
            }
        });
        setBaseColumns(dataNew);
        if (typeof setConfig !== 'boolean') {
            // eslint-disable-next-line no-unused-expressions
            setConfig.onChange && setConfig?.onChange(value, dataNew);
            try {
                if (setConfig.storageKey) {
                    localStorage.setItem(
                        `roo-table-setConfig-cols--${setConfig.storageKey}`,
                        JSON.stringify(value),
                    );
                }
            } catch (error) {
                // console.error('localStorage.setItem', error);
            }
        }
    };

    React.useEffect(() => {
        setBaseColumns(processColumns());
    }, [columns, setConfig]);
    const [currentPage, setCurrentPage] = React.useState(1);
    const [localPageSize, setpageSize] = React.useState(10);
    // 中间数据，用户传了用用户传的
    const localCurrentPage: number = pagination?.currentPage || currentPage;
    const localPageNum: number = pagination?.pageSize || localPageSize;

    // Table pagination
    let topPaginationNode: React.ReactNode;
    let bottomPaginationNode: React.ReactNode;
    const paginationSize = (sizes.includes(size as any) ? size : 'normal') as PaginationProps['size'];
    const paginationStyle = paginationSticky ? { position: 'sticky', bottom: 0, background: '#ffffff' ,zIndex: 2 }  :  {}
    if (localPage) {
        // 前端分页
        const renderPagination = (position: TextAlignPosition) => (
            <div className={`${prefixCls}-pagination-container`} style={{ textAlign: `${position}`, ...paginationStyle }}>
                <Pagination
                    size={paginationSize}
                    showTotal={total => `共 ${total} 条数据`}
                    pageSizeOptions={[10, 20, 30, 100]}
                    {...pagination}
                    onChange={(page, pageSize) => {
                        setCurrentPage(page);
                        if (pageSize) {
                            setpageSize(pageSize);
                        }
                        if (pagination?.onChange) {
                            pagination.onChange(page, pageSize);
                        }
                    }}
                    pageSize={localPageNum}
                    currentPage={localCurrentPage}
                    total={mergedData.length} // 前端分页的话total需要我们计算
                />
            </div>
        );
        const defaultPosition = direction === 'rtl' ? 'left' : 'right';
        const { position = null } = pagination || {};
        if (position !== null && Array.isArray(position)) {
            const topPos = position.find(p => p.indexOf('top') !== -1);
            const bottomPos = position.find(p => p.indexOf('bottom') !== -1);
            const isDisable = position.every(p => `${p}` === 'none');
            if (!topPos && !bottomPos && !isDisable) {
                bottomPaginationNode = renderPagination(defaultPosition);
            }
            if (topPos) {
                topPaginationNode = renderPagination(topPos!.toLowerCase().replace('top', '') as TextAlignPosition);
            }
            if (bottomPos) {
                bottomPaginationNode = renderPagination(
                    bottomPos!.toLowerCase().replace('bottom', '') as TextAlignPosition,
                );
            }
        } else {
            bottomPaginationNode = renderPagination(defaultPosition);
        }
    } else {
        if (pagination !== false && pagination?.total) {
            const renderPagination = (position: TextAlignPosition) => (
                // @ts-ignore
                <div className={`${prefixCls}-pagination-container`} style={{ textAlign: `${position}`, ...paginationStyle }}>
                    <Pagination
                        size={paginationSize}
                        {...pagination}
                    />
                </div>
            );
            const defaultPosition = direction === 'rtl' ? 'left' : 'right';
            const { position } = pagination;
            if (position !== null && Array.isArray(position)) {
                const topPos = position.find(p => p.indexOf('top') !== -1);
                const bottomPos = position.find(p => p.indexOf('bottom') !== -1);
                const isDisable = position.every(p => `${p}` === 'none');
                if (!topPos && !bottomPos && !isDisable) {
                    bottomPaginationNode = renderPagination(defaultPosition);
                }
                if (topPos) {
                    topPaginationNode = renderPagination(topPos!.toLowerCase().replace('top', '') as TextAlignPosition);
                }
                if (bottomPos) {
                    bottomPaginationNode = renderPagination(
                        bottomPos!.toLowerCase().replace('bottom', '') as TextAlignPosition,
                    );
                }
            } else {
                bottomPaginationNode = renderPagination(defaultPosition);
            }
        }
    }
    const currentTableData = React.useMemo(() => {
        const firstItemIndex = (localCurrentPage - 1) * localPageNum;
        return mergedData.slice(firstItemIndex, firstItemIndex + localPageNum);
    }, [localCurrentPage, localPageNum, mergedData]);

    // virtual props
    const virtualProps: { listItemHeight?: number } = {};

    const listItemHeight = React.useMemo(() => {
        const fontHeight = Math.floor(14 * 1.5);
        switch (size) {
            case 'large':
                return 20 * 2 + fontHeight;
            case 'small':
                return 12 * 2 + fontHeight;
            case 'compact':
                return 4 * 2 + fontHeight;
            case 'mini':
                return 8 * 2 + fontHeight;
            default:
                return 16 * 2 + fontHeight;
        }
    }, [size]);

    if (props.virtual) {
        virtualProps.listItemHeight = listItemHeight;
    }

    const TableComponent = props.virtual ? VirtualTable : Table;

    return (
        <div
            ref={ref}
            className={classNames(wrapperClassNames, sizeClassMap[size])}
            style={style}
        >
            {/*// @ts-ignore*/}
            <Loading
                visible={false}
                {...loadingProps}
            >
                {topPaginationNode}
                {setConfig ? (
                    <ColumnsSet
                        onChange={handleChangeValue}
                        popupContainer={ref?.current}
                        setConfig={setConfig}
                        // @ts-ignore
                        columns={columns}
                        prefixCls={prefixCls}
                        locale={mergeLocale}
                    />
                ) : null}
                <TableComponent
                    {...tableProps}
                    {...virtualProps}
                    className={classNames({
                        [`${prefixCls}-bordered`]: bordered,
                        [`${prefixCls}-hover`]: hover,
                        [`${prefixCls}-empty`]: mergedData.length === 0,
                        [`${prefixCls}-stripe`]: stripe,
                    })}
                    expandable={mergedExpandable}
                    emptyText={renderEmptyComponent}
                    rowClassName={internalRowClassName}
                    prefixCls={prefixCls}
                    columns={mergedColumns as RcTableProps<RecordType>['columns']}
                    data={localPage ? currentTableData : mergedData}
                    rowKey={getRowKey}
                    internalHooks={INTERNAL_HOOKS}
                    internalRefs={internalRefs as any}
                    transformColumns={transformColumns as unknown as RcTableProps<RecordType>['transformColumns']}
                    direction={direction}
                    tableLayout={tableLayout}
                    summary={summary}
                    onScroll={onScroll}
                />
                {bottomPaginationNode}
            </Loading>
        </div>
    );
}

type TableNewComponent = (<RecordType extends object = any>(
    props: React.PropsWithChildren<TableProps<RecordType>> & { ref?: React.Ref<HTMLDivElement> },
) => React.ReactElement) & {
    Summary: typeof Summary;
    SELECTION_COLUMN: typeof SELECTION_COLUMN;
    EXPAND_COLUMN: typeof EXPAND_COLUMN;
};

const TableNew = (React.forwardRef(InternalTableNew as any) as unknown) as TableNewComponent;

TableNew.Summary = Summary;
TableNew.SELECTION_COLUMN = SELECTION_COLUMN;
TableNew.EXPAND_COLUMN = EXPAND_COLUMN;

export default TableNew;
