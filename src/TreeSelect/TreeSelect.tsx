import * as React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import Tree from '@roo/roo/Tree';
import Icon from '@roo/roo/Icon';
import { Manager, Reference, PopperPortal } from '@roo/roo/core/Popper';
import { createRef } from '@roo/create-react-ref';
import locale from '@roo/roo/locale';
import { isFunction, isArray } from '@utiljs/is';
import Empty from '@roo/roo/core/Empty';
import { GlobalConfigContext } from '@roo/roo/ConfigProvider';
import { $gray600, $lineColor, $light } from '../_utils/ThemeColor';
import {
    TreeSelectProps,
    TreeSelectState,
    PrimitiveValue,
    TreeData,
    TreeOption,
} from './interface';
import withDisabled from '../_utils/hoc/withDisabled';

const defaultFieldNames = Object.freeze({
    value: 'value',
    label: 'label',
    disabled: 'disabled',
    children: 'children',
});

function isDef(value: any): boolean {
    return value !== undefined && value !== null;
}
export class TreeSelect extends React.Component<TreeSelectProps, TreeSelectState> {
    static contextType?: React.Context<any> | undefined = GlobalConfigContext;

    static propTypes = {
        options: PropTypes.arrayOf(PropTypes.object),
        value: PropTypes.oneOfType([PropTypes.array, PropTypes.number, PropTypes.string]),
        placeholder: PropTypes.string,
        disabled: PropTypes.bool,
        // size: PropTypes.string,
        inputType: PropTypes.string,
        readOnly: PropTypes.bool,
        multiple: PropTypes.bool,
        fieldNames: PropTypes.object,
        popupVisible: PropTypes.bool,
        defaultPopupVisible: PropTypes.bool,
        onSelect: PropTypes.func,
        onChange: PropTypes.func,
        onPopupVisibleChange: PropTypes.func,
        nodeRender: PropTypes.func,
        disablePortal: PropTypes.bool,
        autoExpandParent: PropTypes.bool,
        notFoundContent: PropTypes.node,
        closeMotion: PropTypes.bool,
    }

    static defaultProps = {
        options: [],
        value: undefined,
        placeholder: '',
        disabled: false,
        // size: 'normal',
        inputType: '',
        readOnly: false,
        multiple: false,
        fieldNames: defaultFieldNames,
        popupVisible: undefined,
        defaultPopupVisible: false,
        onSelect: undefined,
        onChange: undefined,
        onPopupVisibleChange: undefined,
        nodeRender: undefined,
        disablePortal: false,
        autoExpandParent: false,
        notFoundContent: null,
        closeMotion: false,
    }

    // select 框体
    private selectRef: HTMLElement | null = null;

    // tree 选项
    private treeRef: React.RefObject<Tree> = createRef();

    // select 中的 input 框
    private inputRef: React.RefObject<HTMLInputElement> = createRef();

    constructor(props: TreeSelectProps) {
        super(props);
        this.state = {
            showPopover: false,
            width: undefined,
        };
        this.handleInputClick = this.handleInputClick.bind(this);
        this.handleNodeClick = this.handleNodeClick.bind(this);
        this.handleCheckChange = this.handleCheckChange.bind(this);
        this.handleClickOutside = this.handleClickOutside.bind(this);
    }

    static getDerivedStateFromProps(props: TreeSelectProps, state: TreeSelectState) {
        // 受控
        if (props.popupVisible !== undefined && props.popupVisible !== state.showPopover) {
            return {
                showPopover: props.popupVisible,
            };
        }
        return null;
    }

    componentDidMount() {
        const {
            defaultPopupVisible,
            popupVisible,
        } = this.props;
        
        // Always get initial width when component mounts
        if (this.selectRef) {
            const { width } = this.selectRef.getBoundingClientRect();
            this.setState({ width });
        }

        const isVisible = popupVisible || defaultPopupVisible;
        if (isVisible) {
            this.setState({
                showPopover: true,
            });
        }
    }

    // 弹框可见性变化触发此事件
    handlePopupVisibleChange(status: boolean) {
        const {
            onPopupVisibleChange,
        } = this.props;
        if (isFunction(onPopupVisibleChange)) {
            onPopupVisibleChange(status);
        }
    }

    // 点击input展示下拉选项
    handleInputClick() {
        const { disabled } = this.props;
        if (disabled) return;
        if (this.selectRef) {
            const { width } = this.selectRef.getBoundingClientRect();
            this.setState({
                width,
                showPopover: true,
            }, this.handlePopupVisibleChange.bind(this, true));
        } else {
            this.setState({ showPopover: true }, this.handlePopupVisibleChange.bind(this, true));
        }
    }

    // 鼠标点击下拉选框外
    handleClickOutside(event: MouseEvent) {
        if (this.selectRef && this.selectRef.contains(event.target as HTMLElement)) {
            return;
        }
        this.setState({
            showPopover: false,
        }, this.handlePopupVisibleChange.bind(this, false));
    }

    // 节点被点击时
    handleNodeClick(node: any) {
        const { onChange, onSelect, multiple } = this.props;
        const _value = node.id;
        if (isFunction(onChange) && !multiple && isDef(_value)) {
            onChange(_value);
        }
        if (isFunction(onSelect) && !multiple && isDef(_value)) {
            const _node = {
                value: node.id,
                label: node.label,
                isChecked: true,
            };
            onSelect(_node, [] as TreeData[]);
        }
    }

    // checkbox显示、选中节点时
    handleCheckChange(node: any, nodes: any[]) {
        const { onChange, onSelect, fieldNames = defaultFieldNames } = this.props;
        const selectedNodes: TreeData[] = [];
        nodes.forEach(item => {
            if (isDef(item[fieldNames.value || defaultFieldNames.value])) {
                selectedNodes.push(item);
            }
        });
        if (isFunction(onChange)) {
            const _node: PrimitiveValue[] = [];
            nodes.forEach(item => {
                const _pValue = item.parent && item.parent[fieldNames.value || defaultFieldNames.value];
                const _value = item && item[fieldNames.value || defaultFieldNames.value];
                if (item.parent && !item.parent.isChecked && isDef(_value)) {
                    _node.push(_value);
                } else if (item.parent && item.parent.isChecked && !isDef(_pValue) && isDef(_value)) {
                    _node.push(_value);
                } else if (!item.parent && item.isChecked && isDef(_value)) {
                    _node.push(_value);
                }
            });
            onChange(_node, selectedNodes);
        }
        if (isFunction(onSelect)) {
            const _node = {
                value: node.id,
                label: node.label,
                isChecked: true,
            };

            onSelect(_node, selectedNodes);
        }
    }

    // 点击input中选中项的删除图标时
    handleItemClick(raw: any, event?: React.MouseEvent) {
        if (event) {
            event.preventDefault();
        }
        const { value, onChange, fieldNames = defaultFieldNames } = this.props;
        if (isArray(value) && isFunction(onChange)) {
            const _value = value.filter(val => val !== raw[fieldNames.value || defaultFieldNames.value]);
            onChange(_value);
        }
    }

    // input输入框样式
    getInputClassName(): string {
        const { disabled, size = this.context.size || 'normal', inputType, multiple } = this.props;
        const list = [`${this.context.prefixCls}-input`];
        if (multiple) {
            list.push(`${this.context.prefixCls}-input-tag`);
        }
        if (disabled) {
            list.push('disabled');
        }
        if (size) {
            const sizeMap = {
                large: 'lg',
                normal: 'md',
                small: 'sm',
                mini: 'xs',
                compact: 'compact',
            };
            list.push(`${this.context.prefixCls}-input-${sizeMap[size]}`);
        }
        if (inputType) {
            list.push(`${this.context.prefixCls}-input-${inputType}`);
        }
        return classNames(list);
    }

    // !仅单选用,单选时使用，选中项的展示内容
    getDisplayValue(): string | undefined {
        const {
            value,
            options,
            fieldNames = defaultFieldNames,
        } = this.props;
        if (isDef(value) && !isArray(value)) {
            if (options) {
                // 全部选项列表，分组选项拍平
                const list = this.getOptionsList();

                for (let i = 0; i < list.length; ++i) {
                    const opt = list[i] as any;
                    const optValue = opt?.[fieldNames.value || defaultFieldNames.value];
                    if (optValue === value) {
                        return opt[fieldNames.label || defaultFieldNames.label];
                    }
                }
            }
            return value as string;
        }
        return '';
    }

    // 将分组中 options 拍平
    getOptionsList(): TreeOption[] {
        const { options = [], fieldNames = defaultFieldNames } = this.props;
        const ret = [] as TreeOption[];
        if (options.length !== 0) {
            let queue = [] as TreeOption[];
            queue = queue.concat(options);
            while (queue.length !== 0) {
                const item = queue.pop() as any;
                ret.push(item);
                const children = (item?.[fieldNames.children || defaultFieldNames.children] ?? []) as TreeOption[];
                for (let i = 0; i < children.length; i++) { queue.push(children[i]); }
            }
        }
        return ret;
    }

    getSelectedKey(): string {
        const { value } = this.props;
        if (isDef(value) && !isArray(value)) { return value as string; }
        return '';
    }

    getSelectedKeys(): string[] {
        const { value } = this.props;
        if (isDef(value) && isArray(value)) { return value as string[]; }
        return [];
    }

    // treeoptions 为空的默认展示
    renderNotFoundContent(): React.ReactNode {
        const { notFoundContent } = this.props;
        return notFoundContent || (
            <Empty title={locale.lng('TreeSelect.noData') as string} />
        );
    }

    // 树型下拉框渲染
    renderTreeOptions(): React.ReactNode {
        const {
            options, multiple, fieldNames = defaultFieldNames, defaultExpandAll, defaultExpandedKeys, onNodeCollapse, onNodeExpand, expandedKeys,
            showSearch, searchInput, nodeRender, controlled, autoExpandParent
        } = this.props;
        const selectedKey = this.getSelectedKey();
        const selectedKeys = this.getSelectedKeys();
        // 传递过来的treeoptions为空数组或者不是数组，展示空内容
        if ((isArray(options) && options.length < 1) || !isArray(options)) {
            return this.renderNotFoundContent();
        }
        return (
            <Tree
                ref={this.treeRef}
                nodeKey={fieldNames.value || defaultFieldNames.value}
                data={options}
                nodeChildren={fieldNames.children || defaultFieldNames.children}
                selectedKey={selectedKey}
                selectedKeys={selectedKeys}
                defaultExpandAll={defaultExpandAll}
                defaultExpandedKeys={defaultExpandedKeys}
                expandedKeys={expandedKeys}
                search={showSearch}
                searchInput={({ placeholder: locale.lng('TreeSelect.searchPlaceholder'), suffix: <Icon name="search" />, ...searchInput })}
                showCheckbox={multiple}
                onCheckChange={this.handleCheckChange}
                onNodeClick={this.handleNodeClick}
                onNodeCollapse={onNodeCollapse}
                onNodeExpand={onNodeExpand}
                controlled={controlled}
                blockNode
                nodeLabel={fieldNames.label || defaultFieldNames.label}
                autoExpandParent={autoExpandParent}
                nodeRender={nodeRender}
            />
        );
    }

    // 单选输入框渲染
    renderSingleContainer(): React.ReactNode {
        const {
            placeholder,
            disabled,
            readOnly,
        } = this.props;
        const displayValue = this.getDisplayValue();
        const cStyle: React.CSSProperties = {
            flexBasis: 0,
            minWidth: '20px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
        };
        return (
            <input
                ref={this.inputRef}
                type="text"
                placeholder={placeholder || locale.lng('Select.placeholder') as string}
                autoComplete="off"
                style={cStyle}
                className={this.getInputClassName()}
                disabled={disabled}
                readOnly={readOnly}
                value={displayValue}
                onChange={() => {}}
            />
        );
    }

    // 多选输入框渲染
    renderMultipleContainer(): React.ReactNode {
        const {
            value,
            disabled,
            fieldNames = defaultFieldNames,
            placeholder
        } = this.props;
        const cStyle = {
            flexBasis: 0,
            minWidth: '20px',
        };
        const disabledStyle = disabled ? {
            background: $gray600,
        } : undefined;

        Object.assign(cStyle, disabledStyle);
        const tags: any[] = [];
        if (isArray(value)) {
            const optionsList = this.getOptionsList() as any[];
            for (let i = 0; i < value.length; ++i) {
                const val = value[i] as (number | string);
                const opt = optionsList.find(item => item[fieldNames.value || defaultFieldNames.value] === val);
                if (opt) {
                    tags.push(opt);
                }
            }
        }
        const renderTagContent = (opt: any) => opt[fieldNames.label || defaultFieldNames.label];
        return (
            <div
                className={this.getInputClassName()}
            >
                <div className="tags-box">
                    {
                        tags.map(item => (
                            <span
                                className={`${this.context.prefixCls}-tag ${this.context.prefixCls}-tag-gray`}
                                key={item[fieldNames.value || defaultFieldNames.value]}
                            >
                                {renderTagContent(item)}
                                {item[fieldNames.disabled || defaultFieldNames.disabled] ? null : (
                                    <i
                                        className="roo-icon roo-icon-close-new"
                                        onClick={this.handleItemClick.bind(this, item)}
                                    />
                                )}
                            </span>
                        ))
                    }
                    <input
                        type="text"
                        style={cStyle}
                        placeholder={(!value || (Array.isArray(value) && value.length === 0)) ? placeholder || locale.lng('Select.placeholder') as string : ''}
                        disabled={disabled}
                        readOnly
                    />
                </div>
            </div>
        );
    }

    render() {
        const { multiple, disablePortal, showSearch, closeMotion } = this.props;

        const { showPopover, width } = this.state;
        return (
            <Manager>
                <Reference
                    innerRef={node => { this.selectRef = node; }}
                >
                    <div
                        className={`${this.context.prefixCls}-input-group has-icon`}
                        onClick={this.handleInputClick}
                    >
                        {multiple ? this.renderMultipleContainer() : this.renderSingleContainer()}
                    </div>
                </Reference>
                <PopperPortal
                    zIndex={1000}
                    disablePortal={disablePortal}
                    visible={showPopover}
                    modifiers={[{
                        name: 'flip',
                        options: {
                            fallbackPlacements: ['bottom', 'top'],
                        }
                    }]}
                    placement="bottom-start"
                    onClickOutside={this.handleClickOutside}
                    className={`${this.context.prefixCls}-tree-select-popper`}
                    isCloseMotion={closeMotion}
                >
                    <div
                        style={{
                            width, padding: showSearch ? 10 : 0, backgroundColor: $light, border: `1px solid ${$lineColor}`, maxHeight: '256px', overflow: 'auto',
                        }}
                        className={`${this.context.prefixCls}-tree-select-popper-content`}
                    >
                        {this.renderTreeOptions()}
                    </div>
                </PopperPortal>
            </Manager>
        );
    }
}

export default withDisabled<typeof TreeSelect>(TreeSelect);
