import type { TextareaHTMLAttributes, ComponentClass } from 'react';
import Input from './Input';
import Textarea from './Textarea';
import InputGroup from './InputGroup';
import type { TextareaProps } from './interface';
import withApiCompatibility from '../_utils/hoc/withApiCompatibility';

type TextareaComponentType = ComponentClass<TextareaProps & TextareaHTMLAttributes<HTMLTextAreaElement>>;

const WrappedInput = withApiCompatibility('Input', Input) as typeof Input;

WrappedInput.Textarea = Textarea as TextareaComponentType;
WrappedInput.InputGroup = InputGroup;

export { Textarea, InputGroup };
export * from './interface';
export default WrappedInput;
