/* eslint-disable react/jsx-props-no-spreading */
import * as React from 'react';
import * as PropTypes from 'prop-types';
import classNames from 'classnames';
import { clone } from '@utiljs/clone';
import { isFunction } from '@utiljs/is';
import Icon from '@roo/roo/Icon';
import { InputBaseProps, TextareaProps } from './interface';
import { Omit } from '../_utils/types';
import { GlobalConfigContext } from '../ConfigProvider';
import { $gray400 } from '../_utils/ThemeColor';
import withDisabled from '../_utils/hoc/withDisabled';

const classDirectory = {
    mini: 'xs',
    small: 'sm',
    normal: '',
    large: 'lg',
    compact: 'compact',
};
export type InputProps = InputBaseProps & Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size' | 'prefix'>;

interface S {
    visiblePassword: boolean;
    countWidth: number;
}

class Input extends React.Component<InputProps, S> {
    static rooName = 'RInput'

    static Textarea: React.ComponentClass<TextareaProps & React.TextareaHTMLAttributes<any>>;

    static InputGroup: React.FunctionComponent<any>;

    static contextType?: React.Context<any> | undefined = GlobalConfigContext;
    context!: React.ContextType<typeof GlobalConfigContext>;

    static propTypes = {
        // size: PropTypes.oneOf(['mini', 'small', 'normal', 'large']),
        styleType: PropTypes.oneOf(['plaintext', 'line']),
        status: PropTypes.oneOf(['error', 'success']),
        message: PropTypes.string,
        preAppend: PropTypes.node,
        afterAppend: PropTypes.node,
        prefix: PropTypes.node,
        suffix: PropTypes.node,
        prefixIcon: PropTypes.string,
        suffixIcon: PropTypes.string,
        readOnly: PropTypes.bool,
        disabled: PropTypes.bool,
        placeholder: PropTypes.string,
        type: PropTypes.string,
        className: PropTypes.string,
        wrapClassName: PropTypes.string,
        inputRef: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),
        onChange: PropTypes.func,
        onPressEnter: PropTypes.func,
        onAllowClear: PropTypes.func,
        maxLengthCheckRule: PropTypes.shape({
            checkMaxLength: PropTypes.number,
            chineseLength: PropTypes.number,
        }),
        inputStatus: PropTypes.oneOf(['normal', 'success', 'error']),
        allowClear: PropTypes.oneOfType([PropTypes.bool, PropTypes.object]),
        onKeyDown: PropTypes.func,
        bordered: PropTypes.bool,
        visibilityToggle: PropTypes.bool,
        iconRender: PropTypes.func,
        // eslint-disable-next-line react/require-default-props
        customPreIcon: PropTypes.oneOfType([PropTypes.bool, PropTypes.string, PropTypes.node]),
        appendNoBorder: PropTypes.bool,
        showCount: PropTypes.oneOfType([PropTypes.bool, PropTypes.object]),
    };

    static defaultProps = {
        // size: 'normal',
        styleType: null,
        status: null,
        message: null,
        preAppend: null,
        afterAppend: null,
        prefix: null,
        suffix: null,
        suffixIcon: null,
        prefixIcon: null,
        readOnly: false,
        disabled: false,
        placeholder: '',
        type: 'text',
        className: '',
        wrapClassName: '',
        inputRef: null,
        onChange: () => {},
        onPressEnter: undefined,
        onAllowClear: null,
        maxLengthCheckRule: {
            chineseLength: 1,
        },
        inputStatus: null,
        allowClear: false,
        onKeyDown: null,
        bordered: true,
        visibilityToggle: true,
        iconRender: null,
        appendNoBorder: false,
        showCount: false,
    }

    event: any = { target: {} };

    countRef = React.createRef<HTMLSpanElement>();
    constructor(props: InputProps) {
        super(props);
        this.state = {
            visiblePassword: false,
            countWidth: 0,
        };
    }

    componentDidMount() {
        this.updateCountWidth();
    }

    componentDidUpdate(prevProps: InputProps) {
        if (
            prevProps.value !== this.props.value ||
            prevProps.showCount !== this.props.showCount ||
            prevProps.maxLength !== this.props.maxLength
        ) {
            this.updateCountWidth();
        }
    }

    updateCountWidth = () => {
        if (this.props.showCount && this.countRef.current) {
            const width = this.countRef.current.offsetWidth;
            if (width !== this.state.countWidth) {
                this.setState({ countWidth: width });
            }
        }
    };

    fixControlledValue<T>(value: T) {
        return String(value);
    }

    renderInput = () => {
        const {
            size = this.context.size || 'normal',
            styleType,
            status,
            readOnly,
            disabled,
            preAppend,
            afterAppend,
            prefix,
            suffix,
            prefixIcon,
            suffixIcon,
            type,
            placeholder,
            className,
            style,
            wrapClassName,
            wrapStyle,
            inputRef,
            children, // 防止传入 children 的情况,
            maxLengthCheckRule,
            allowClear,
            inputStatus,
            bordered,
            visibilityToggle,
            onChange,
            onPressEnter,
            onAllowClear,
            iconRender,
            customPreIcon,
            appendNoBorder, // 解构此属性，确保不传递给原生input元素
            showCount, // 解构此属性，确保不传递给原生input元素
            ...restProps
        } = this.props;
        const { visiblePassword } = this.state;
        const inputCls = classNames(
            `${this.context.prefixCls}-input`, {
                [`${this.context.prefixCls}-input-${classDirectory[size]}`]: size !== 'normal',
                [`${this.context.prefixCls}-input-${styleType}`]: styleType,
                [`${this.context.prefixCls}-input-noborder`]: !bordered,
                'pre-icon': !!(this.context.direction === 'RTL' && (prefix || prefixIcon || customPreIcon))
            },
            className,
        );
        const forkedProps = {
            readOnly,
            disabled,
        };
        // 设置统计字符的宽度，如果showCount为true，则设置统计字符的宽度，防止统计字符与输入框内容重合
        const { countWidth } = this.state;
        let mergedStyle = style;
        if (showCount && countWidth) {
            const isRTL = this.context.direction === 'RTL';
            if (isRTL) {
                mergedStyle = { ...style, paddingLeft: (style?.paddingLeft ? parseInt(style.paddingLeft as string, 10) : 0) + countWidth + 16 };
            } else {
                mergedStyle = { ...style, paddingRight: (style?.paddingRight ? parseInt(style.paddingRight as string, 10) : 0) + countWidth + 16 };
            }
        }

        return (
            <input
                {...forkedProps}
                {...restProps}
                onChange={this.hackOnChange.bind(this)}
                onKeyDown={this.handleKeyDown}
                type={visiblePassword && visibilityToggle ? 'text' : type}
                ref={inputRef}
                className={inputCls}
                style={mergedStyle}
                placeholder={placeholder}
            />
        );
    };

    hackOnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const checkMaxLength = this.props.maxLengthCheckRule?.checkMaxLength;
        // this.event = { ...e };
        if (checkMaxLength) {
            const checkValue = e.target.value;
            const length: number = this.calculateLength(checkValue);
            if (this.props.onChange) {
                this.props.onChange(e, length);
            }
        } else if (this.props.onChange) {
            this.props.onChange(e);
        }
    };

    handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        const { onPressEnter, onKeyDown } = this.props;
        if (onPressEnter && e.keyCode === 13) {
            onPressEnter(e);
        }
        if (isFunction(onKeyDown)) {
            onKeyDown(e);
        }
    };

    handleReset = (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
        const { onAllowClear, onChange } = this.props;
        if (onAllowClear) onAllowClear(e);
        if (onChange) {
            this.event.target.value = '';
            onChange(this.event);
        }
    };

    getClearIcon = (style: React.CSSProperties): React.ReactNode => {
        const { disabled, readOnly, value, suffix, suffixIcon } = this.props;
        if (value) this.event.target.value = value;
        const checkMaxLength = this.props.maxLengthCheckRule?.checkMaxLength;
        const allowClear = this.props.allowClear as boolean | { clearIcon?: React.ReactNode };
        const needClear = !disabled && !readOnly && value;
        const suffixClear = clone(style);
        if (!allowClear || !needClear) { return null; }
        // 后缀图标与清除图标一起使用时，解决与后缀图标重复问题
        Object.assign(suffixClear, { cursor: 'pointer' });
        if (suffix || suffixIcon || checkMaxLength) {
            const rtlPosition =
                this.context.direction === 'RTL'
                    ? { left: suffix || suffixIcon ? '2.5em' : '2.9em' }
                    : { right: suffix || suffixIcon ? '2.5em' : '2.9em' };
            Object.assign(suffixClear, {
                position: 'relative',
                transform: 'translateY(0%)',
                width: '0px',
                alignItems: 'center',
                ...rtlPosition,
            });
        }
        let iconNode: React.ReactNode | null;
        if (needClear) {
            if (typeof allowClear === 'object' && allowClear?.clearIcon) {
                iconNode = allowClear?.clearIcon;
            } else {
                iconNode = <i className={classNames('roo-icon', 'roo-icon-times-circle-new')} />;
            }
        }
        return this.fixControlledValue(value) && (
            <span
                className="addon-icon"
                style={suffixClear}
                onClick={this.handleReset}
            >
                {iconNode}
            </span>
        );
    }

    renderStatus = () => {
        const {
            status,
            message,
            wrapClassName,
            wrapStyle,
        } = this.props;
        const wrapCls = classNames({
            'has-error': status === 'error',
            'has-success-hook': status === 'success',
        }, wrapClassName);

        return status ? (
            <div
                className={wrapCls}
                style={wrapStyle}
            >
                {this.renderInput()}
                <p className="help-block">{message}</p>
            </div>
        ) : this.renderInput();
    }

    getPasswordIcon = (suffixfixStyle: React.CSSProperties) => {
        const { iconRender } = this.props;
        const { visiblePassword } = this.state;
        const icon = isFunction(iconRender) && iconRender(visiblePassword);
        return (
            <div
                className="addon-icon"
                style={suffixfixStyle}
                onClick={e => {
                    this.setState({
                        visiblePassword: !visiblePassword,
                    });
                    e.preventDefault();
                }}
            >
                {
                    React.isValidElement(icon) ? icon : (
                        <svg
                            viewBox="0 0 1024 1024"
                            style={{ color: $gray400 }}
                            focusable="false"
                            data-icon="eye"
                            width="1em"
                            height="1em"
                            fill="currentColor"
                            aria-hidden="true"
                        >
                            {
                                !visiblePassword
                                    ? (
                                        <>
                                            <path d="M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z" />
                                            <path d="M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z" />
                                        </>
                                    )
                                    : <path d="M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z" />
                            }
                        </svg>
                    )
                }
            </div>
        );
    }

    renderInputStatus = () => {
        const {
            inputStatus,
            message,
            wrapClassName,
            wrapStyle,
            preAppend,
            afterAppend,
            size = this.context.size || 'normal',
            prefix,
            prefixIcon,
            suffixIcon,
            suffix,
            allowClear,
            type,
            visibilityToggle,
            styleType,
            bordered,
            appendNoBorder,
            showCount,
        } = this.props;
        const checkMaxLength = this.props.maxLengthCheckRule?.checkMaxLength;
        const isShowPasswordIcon = type?.toLocaleLowerCase() === 'password' && visibilityToggle;
        const groupCls = classNames(
            // 如果有自定义图标或者统计字符，则进行flex布局
            { [`${this.context.prefixCls}-input-group`]: preAppend || afterAppend || prefix || suffix || prefixIcon || suffixIcon || checkMaxLength || allowClear || isShowPasswordIcon || showCount }, {
                [`${this.context.prefixCls}-input-group-${classDirectory[size]}`]: size !== 'normal',
                'has-icon': prefix || suffix || checkMaxLength || allowClear || isShowPasswordIcon || prefixIcon || suffixIcon || showCount,
                'is-count-check': checkMaxLength || showCount,
            }, {
                'has-error': inputStatus === 'error',
                // 如果有后缀的图标或者统计字符，则不显示success的默认的图标，只是输入框颜色变化
                'has-success-hook': inputStatus === 'success' && !(suffix || suffixIcon || afterAppend || checkMaxLength || allowClear || showCount),
                'has-error-hook': inputStatus === 'error' && !(suffix || suffixIcon || afterAppend || checkMaxLength || allowClear || showCount),
                'has-success-color': inputStatus === 'success',
            }, wrapClassName
        );

        const fixStyle = {
            display: 'inline-flex',
        };

        const suffixfixStyle = {
            display: 'inline-flex',
        };

        // 不改变input结构，hack 解决suffix与afterAppend两部分icon重合问题
        if (afterAppend || checkMaxLength || showCount) {
            const rtlPosition = this.context.direction === 'RTL' ? { left: '1.5em', } : { right: '1.5em' };
            Object.assign(suffixfixStyle, {
                position: 'relative',
                transform: 'translateY(0%)',
                width: '0px',
                alignItems: 'center',
                ...rtlPosition
            });
        }
        return (
            <div>
                <div
                    className={groupCls}
                    style={wrapStyle}
                >
                    {!preAppend && (prefix || prefixIcon) ? (
                        <div
                            className="prefix-icon"
                            style={fixStyle}
                        >
                            {prefixIcon ? <Icon name={prefixIcon} /> : prefix}
                        </div>
                    ) : null}
                    {preAppend ? (
                        <div
                            className={classNames(`${this.context.prefixCls}-input-group-prepend`, {
                                [`${this.context.prefixCls}-input-group-prepend-${styleType}`]: styleType,
                                [`${this.context.prefixCls}-input-${classDirectory[size]}`]: size !== 'normal',
                                [`${this.context.prefixCls}-input-noborder`]: !bordered,
                                [`${this.context.prefixCls}-input-group-prepend-no-border`]: appendNoBorder,
                            })}
                        >
                            {preAppend}
                        </div>
                    ) : null}
                    {this.renderInput()}
                    {this.getClearIcon(suffixfixStyle)}
                    {checkMaxLength && !(suffix || suffixIcon) ? (
                        <div className={`${this.context.prefixCls}-input-value-count`}>
                            <span className={`${this.context.prefixCls}-input-count`}>
                                {this.calculateLength(this.props.value)}
                            </span>
                            <span>/</span>
                            {checkMaxLength}
                        </div>
                    ) : null}
                    {/* 密码框的图标高于默认后缀图标 */}
                    {(suffix || suffixIcon) && !isShowPasswordIcon ? (
                        <div
                            className="addon-icon"
                            style={suffixfixStyle}
                        >
                            {suffixIcon ? <Icon name={suffixIcon} /> : suffix}
                        </div>
                    ) : null}
                    {isShowPasswordIcon && this.getPasswordIcon(suffixfixStyle)}
                    {showCount && this.renderShowCount()}
                    {afterAppend ? (
                        <div
                            className={classNames(`${this.context.prefixCls}-input-group-append`, {
                                [`${this.context.prefixCls}-input-group-append-${styleType}`]: styleType,
                                [`${this.context.prefixCls}-input-${classDirectory[size]}`]: size !== 'normal',
                                [`${this.context.prefixCls}-input-noborder`]: !bordered,
                                [`${this.context.prefixCls}-input-group-append-no-border`]: appendNoBorder,
                            })}
                        >
                            {afterAppend}
                        </div>
                    ) : null}
                </div>
                {inputStatus === 'error' && message ? <p className="help-block">{message}</p> : null}
            </div>
        );
    }

    calculateLength = (checkValue?: string) => {
        if (!checkValue) {
            return 0;
        }
        const {
            maxLengthCheckRule
        } = this.props;
        const chineseLength = maxLengthCheckRule?.chineseLength || 1;
        const replaceString = '*'.repeat(chineseLength);
        const replaceResult = checkValue?.replace(/[\u4e00-\u9fa5]/g, replaceString);
        return replaceResult?.length || 0;
    }

    renderGroup = () => {
        const {
            preAppend,
            afterAppend,
            size = this.context.size || 'normal',
            prefix,
            prefixIcon,
            suffixIcon,
            suffix,
            wrapClassName,
            wrapStyle,
            allowClear,
            type,
            visibilityToggle,
            styleType,
            bordered,
            appendNoBorder,
            showCount,
        } = this.props;
        const checkMaxLength = this.props.maxLengthCheckRule?.checkMaxLength;
        const isShowPasswordIcon = type?.toLocaleLowerCase() === 'password' && visibilityToggle;

        const groupCls = classNames(`${this.context.prefixCls}-input-group`, {
            [`${this.context.prefixCls}-input-group-${classDirectory[size]}`]: size !== 'normal',
            'has-icon': prefix || suffix || checkMaxLength || allowClear || isShowPasswordIcon || suffixIcon || prefixIcon || showCount,
            'is-count-check': checkMaxLength || showCount,
        }, wrapClassName);

        const fixStyle = {
            display: 'inline-flex',
        };

        const suffixfixStyle = {
            display: 'inline-flex',
        };

        // 不改变input结构，hack 解决suffix与afterAppend两部分icon重合问题
        if (afterAppend || checkMaxLength || showCount) {
            const rtlPosition = this.context.direction === 'RTL' ? { left: '1.5em', } : { right: '1.5em' };
            Object.assign(suffixfixStyle, {
                position: 'relative',
                transform: 'translateY(0%)',
                width: '0px',
                alignItems: 'center',
                ...rtlPosition
            });
        }

        return (
            <div
                className={groupCls}
                style={wrapStyle}
            >
                {!preAppend && (prefix || prefixIcon) ? (
                    <div
                        className="prefix-icon"
                        style={fixStyle}
                    >
                        {prefixIcon ? <Icon name={prefixIcon} /> : prefix}
                    </div>
                ) : null}
                {preAppend ? (
                    <div
                        className={classNames(`${this.context.prefixCls}-input-group-prepend`, {
                            [`${this.context.prefixCls}-input-group-prepend-${styleType}`]: styleType,
                            [`${this.context.prefixCls}-input-${classDirectory[size]}`]: size !== 'normal',
                            [`${this.context.prefixCls}-input-noborder`]: !bordered,
                            [`${this.context.prefixCls}-input-group-prepend-no-border`]: appendNoBorder,
                        })}
                    >
                        {preAppend}
                    </div>
                ) : null}
                {this.renderInput()}
                {this.getClearIcon(suffixfixStyle)}
                {checkMaxLength && !(suffix || suffixIcon) ? (
                    <div className={`${this.context.prefixCls}-input-value-count`}>
                        <span className={`${this.context.prefixCls}-input-count`}>
                            {this.calculateLength(this.props.value)}
                        </span>
                        <span>/</span>
                        {checkMaxLength}
                    </div>
                ) : null}
                {/* 密码框的图标高于默认后缀图标 */}
                {(suffix || suffixIcon) && !isShowPasswordIcon ? (
                    <div
                        className="addon-icon"
                        style={suffixfixStyle}
                    >
                        {suffixIcon ? <Icon name={suffixIcon} /> : suffix}
                    </div>
                ) : null}
                {isShowPasswordIcon && this.getPasswordIcon(suffixfixStyle)}
                {showCount && this.renderShowCount()}
                {afterAppend ? (
                    <div
                        className={classNames(`${this.context.prefixCls}-input-group-append`, {
                            [`${this.context.prefixCls}-input-group-append-${styleType}`]: styleType,
                            [`${this.context.prefixCls}-input-${classDirectory[size]}`]: size !== 'normal',
                            [`${this.context.prefixCls}-input-noborder`]: !bordered,
                            [`${this.context.prefixCls}-input-group-append-no-border`]: appendNoBorder,
                        })}
                    >
                        {afterAppend}
                    </div>
                ) : null}
            </div>
        );
    }

    renderShowCount = () => {
        const { showCount, maxLength, value = '' } = this.props;
        if (!showCount) return null;

        const valueLength = String(value).length;
        let displayValue: React.ReactNode;

        if (typeof showCount === 'object' && showCount.formatter) {
            displayValue = showCount.formatter({
                count: valueLength,
                maxLength,
            });
        } else {
            displayValue = `${valueLength}${maxLength ? `/${maxLength}` : ''}`;
        }

        return (
            <span ref={this.countRef} className={`${this.context.prefixCls}-input-value-count`}>
                {displayValue}
            </span>
        );
    };

    render() {
        const {
            status,
            preAppend,
            afterAppend,
            suffix,
            suffixIcon,
            prefixIcon,
            prefix,
            inputStatus,
            allowClear,
            type,
            visibilityToggle,
            showCount,
        } = this.props;
        const checkMaxLength = this.props.maxLengthCheckRule?.checkMaxLength;
        const isShowPasswordIcon = type?.toLocaleLowerCase() === 'password' && visibilityToggle;
        if (inputStatus) {
            return this.renderInputStatus();
        } else {
            if (status) {
                return this.renderStatus();
            } else if (preAppend || afterAppend || suffix || prefix || checkMaxLength || allowClear || isShowPasswordIcon || suffixIcon || prefixIcon || showCount) {
                return this.renderGroup();
            } else {
                return this.renderInput();
            }
        }
    }
}

export default withDisabled<typeof Input>(Input);
