import * as React from 'react';
import * as PropTypes from 'prop-types';
import classNames from 'classnames';
import { polyfill } from 'react-lifecycles-compat';
import { Guid } from '@utiljs/guid';
import { isFunction, isNumber } from '@utiljs/is';
import { debounce } from '@utiljs/functional';
import Selector from '@roo/roo/Selector';
import locale from '@roo/roo/locale';
import { GlobalConfigContext } from '@roo/roo/ConfigProvider';
import Page from './Page';
import Jump from './Jump';

import { PaginationProps, PaginationState } from './interface';

const timestamp = new Date().getTime();
// @ts-ignore
const g = new Guid(timestamp);

const preventDefault = (e: React.MouseEvent) => {
    e.preventDefault();
};

const SizeClsMap = {
    large: '-lg',
    normal: '',
    small: '-sm',
    mini: '-xs',
    compact: '-compact',
};
class Pagination extends React.Component<PaginationProps, PaginationState> {
    static contextType = GlobalConfigContext;
    context!: React.ContextType<typeof GlobalConfigContext>;

    static propTypes = {
        total: PropTypes.number,
        pageSize: PropTypes.number,
        currentPage: PropTypes.number,
        defaultCurrentPage: PropTypes.number,
        showPager: PropTypes.bool,
        prevText: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
        nextText: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
        onChange: PropTypes.func,
        onShowSizeChange: PropTypes.func,
        showJumper: PropTypes.bool,
        // size: PropTypes.string,
        showTotal: PropTypes.func,
        pageSizeOptions: PropTypes.array,
        showCurrentPage: PropTypes.bool,
        defaultPageSize: PropTypes.number,
        hideOnSinglePage: PropTypes.bool,
        showLessItems: PropTypes.bool,
        disabled: PropTypes.bool,
        responsive: PropTypes.bool,
        showTitle: PropTypes.bool,
        showSizeChanger: PropTypes.bool,
    };

    static defaultProps = {
        total: 0,
        pageSize: undefined,
        defaultCurrentPage: 1,
        currentPage: undefined,
        showPager: true,
        // size: 'normal',
        prevText: '',
        nextText: '',
        onChange: () => {},
        onShowSizeChange: () => {},
        showTotal: null,
        showJumper: false,
        pageSizeOptions: [],
        showCurrentPage: false,
        defaultPageSize: 10,
        hideOnSinglePage: false,
        showLessItems: false,
        disabled: false,
        responsive: true,
        showTitle: false,
        showSizeChanger: true,
    };

    constructor(props: PaginationProps) {
        super(props);
        this.state = {
            current: this.props.currentPage || this.props.defaultCurrentPage || 1,
            pageSize: this.props.pageSize || this.props.defaultPageSize || 10,
            prevProps: {},
            screenWidth: typeof window === 'undefined' ? 0 : window.innerWidth,
        };
    }

    private handleResize = debounce(() => {
        this.setState({ screenWidth: window.innerWidth });
    }, 150);

    componentDidMount() {
        const { responsive } = this.props;
        if (responsive) {
            window.addEventListener('resize', this.handleResize);
        }
    }

    componentWillUnmount() {
        const { responsive } = this.props;
        if (responsive) {
            window.removeEventListener('resize', this.handleResize);
        }
    }

    // 根据屏幕宽度返回要显示的页码数
    getPageBufferSize = () => {
        const { screenWidth } = this.state;
        const { showLessItems } = this.props;

        if (screenWidth < 768) {
            return 1;
        }
        return showLessItems ? 1 : 2;
    };

    static getDerivedStateFromProps(nextProps: PaginationProps, prevState: PaginationState) {
        const { current, pageSize } = prevState;
        const prevProps = prevState.prevProps || {};
        const controlPageSize = prevProps.pageSize !== nextProps.pageSize ? nextProps.pageSize : pageSize;
        const controlCurrentPage = prevProps.currentPage !== nextProps.currentPage ? nextProps.currentPage : current;
        const state: any = {
            prevProps: nextProps,
        };
        if (nextProps.currentPage !== undefined) {
            state.current = controlCurrentPage;
        }
        if (nextProps.pageSize !== undefined) {
            state.pageSize = controlPageSize;
        }
        return state;
    }

    getJumpPrevPage = () => Math.max(1, this.state.current - 5);

    getJumpNextPage = () => Math.min(this.calculatePage(), this.state.current + 5);

    jumpPrev = () => {
        this.handlePageChange(this.getJumpPrevPage());
    };

    jumpNext = () => {
        this.handlePageChange(this.getJumpNextPage());
    };

    hasPrev = () => this.state.current > 1;

    hasNext = () => this.state.current < this.calculatePage();

    // 获取分页数目
    calculatePage = (p?: number) => {
        let pageSize = p;
        if (typeof pageSize === 'undefined') {
            pageSize = this.state.pageSize!;
        }
        return Math.ceil(this.props.total! / pageSize!);
    };

    // 上一页
    handleClickPrev = (e: React.SyntheticEvent<any>) => {
        if (this.props.disabled) return;
        e.stopPropagation();
        if (this.hasPrev()) {
            this.handlePageChange(this.state.current - 1);
        }
    };

    // 下一页
    handleClickNext = (e: React.SyntheticEvent<any>) => {
        if (this.props.disabled) return;
        e.stopPropagation();
        if (this.hasNext()) {
            this.handlePageChange(this.state.current + 1);
        }
    };

    handleChange = (current: number) => {
        const { onChange } = this.props;
        const { pageSize } = this.state;
        if (isFunction(onChange)) {
            onChange(current, pageSize);
        }
    };

    handlePageChange = (current: number) => {
        if (this.props.disabled) return;
        const currentPage = this.state.current;
        if (typeof this.props.currentPage !== 'number') {
            if (current === currentPage) {
                return;
            }
            this.setState(
                {
                    current,
                },
                () => {
                    this.handleChange(current);
                },
            );
            return;
        }
        if (currentPage !== current) {
            this.handleChange(current);
        }
    };

    // 页数选择器 change
    paginationSelectorChange = (size: any) => {
        if (this.props.disabled) return;
        let { current } = this.state;
        const newCurrent = this.calculatePage(size);
        const { onShowSizeChange } = this.props;
        current = current > newCurrent ? newCurrent : current;
        if (newCurrent === 0) current = this.state.current;
        if (this.props.pageSize === undefined && typeof size === 'number') {
            this.setState({ pageSize: size });
        }
        if (this.props.currentPage === undefined && typeof current === 'number') {
            this.setState({ current });
        }
        if (isFunction(onShowSizeChange)) {
            onShowSizeChange(current, size);
        }
        if ('onChange' in this.props && this.props.onChange) {
            this.props.onChange(current, size);
        }
    };

    // pageSizeOptions 做过滤
    getPageSizeOptions = (pageSizeOptions: any[]) => {
        const options = pageSizeOptions.filter(i => isNumber(Number(i)));
        if (options.some(i => i.toString() === this.state.pageSize.toString())) {
            // pagesize 在 getPageSizeOptions中，直接返回
            return options.map(i => Number(i));
        }
        // 不存在的话把pagesize塞进去并作一下排序
        // https://github.com/react-component/pagination/blob/master/src/Options.tsx#L93
        return options
            .concat([this.state.pageSize.toString()])
            .sort((a, b) => {
                const numberA = Number.isNaN(Number(a)) ? 0 : Number(a);
                const numberB = Number.isNaN(Number(b)) ? 0 : Number(b);
                return numberA - numberB;
            })
            .map(i => Number(i));
    };

    render() {
        let pagesList: any = [];
        const { current, pageSize } = this.state;
        const {
            total,
            showJumper,
            size = this.context.size || 'normal',
            className,
            style,
            showTotal,
            pageSizeOptions,
            showCurrentPage,
            hideOnSinglePage,
            showLessItems = false,
            disabled = false,
            responsive = true,
            showTitle = false,
            showSizeChanger = true,
        } = this.props;
        let pageBuffer = 2;
        if (responsive) {
            pageBuffer = this.getPageBufferSize();
        } else if (showLessItems) {
            pageBuffer = 1;
        }
        const allPages = this.calculatePage(pageSize);
        let left = allPages > 5 ? Math.max(1, current - pageBuffer) : 1;
        let right = allPages > 5 ? Math.min(current + pageBuffer, allPages) : allPages;
        const prevClass = this.props.prevText ? 'light' : 'arrow';
        // 左边跳转
        const jumpPrev = (
            <li
                className={`${this.context.direction === 'RTL' ? 'ellipsis-right' : 'ellipsis-left'}`}
                key="prev"
                onClick={this.jumpPrev}
            >
                <a onClick={preventDefault} />
            </li>
        );
        // 右边跳转
        const jumpNext = (
            <li
                className={`${this.context.direction === 'RTL' ? 'ellipsis-left' : 'ellipsis-right'}`}
                key="next"
                onClick={this.jumpNext}
            >
                <a onClick={preventDefault} />
            </li>
        );

        // 首页
        const firstPager = (
            <Page
                page={1}
                onChangePage={this.handlePageChange}
                key={g.guid('pagination')}
                showTitle={showTitle}
            />
        );
        // 页
        const lastPager = (
            <Page
                page={allPages}
                onChangePage={this.handlePageChange}
                key={g.guid('pagination')}
                showTitle={showTitle}
            />
        );
        // 左极限
        if (current + pageBuffer >= allPages && allPages - 2 * pageBuffer >= 1) {
            left = allPages - (pageBuffer === 1 ? 2 : 4);
        }
        // 右极限
        if (current - pageBuffer <= 1 && 1 + 2 * pageBuffer < allPages) {
            right = 1 + (pageBuffer === 1 ? 2 : 4);
        }

        for (let i = left; i <= right; i++) {
            const active = current === i;
            pagesList.push(
                <Page
                    page={i}
                    active={active}
                    key={g.guid('pagination')}
                    onChangePage={this.handlePageChange}
                    showTitle={showTitle}
                />,
            );
        }

        if (current - pageBuffer * 2 >= 1 && allPages > 5 && left > 2) {
            pagesList.unshift(jumpPrev);
        }

        if (allPages - current >= pageBuffer * 2 && allPages > 5 && allPages - right > 1) {
            pagesList.push(jumpNext);
        }

        if (left !== 1) {
            pagesList.unshift(firstPager);
        }

        if (right !== allPages && !this.props.hideMaxPage) {
            pagesList.push(lastPager);
        }

        if (total! <= pageSize) {
            pagesList = (
                <Page
                    page={1}
                    key={g.guid('pagination')}
                    active
                    onChangePage={this.handlePageChange}
                    showTitle={showTitle}
                />
            );
        }
        const clx = `${this.context.prefixCls}-pagination${SizeClsMap[size]}`;
        // 显示总数及当前展示顺序
        let totalText = null;
        if (showTotal) {
            totalText = (
                <li className={`${this.context.prefixCls}-total-text`}>
                    {showTotal(total!, [
                        total === 0 ? 0 : (current - 1) * pageSize + 1,
                        current * pageSize > total! ? total! : current * pageSize,
                    ])}
                </li>
            );
        }
        // 展示页数切换器
        let paginationSelector = null;
        if (
            (showSizeChanger !== false) &&
            pageSizeOptions instanceof Array &&
            pageSizeOptions.length > 0
        ) {
            const originOptions = this.getPageSizeOptions(pageSizeOptions || []);
            const options = originOptions?.map((value: number) => ({
                value,
                label: `${value} ${locale.lng('Pagination.itemsPerPage')}`,
            }));
            paginationSelector = (
                <span style={{ display: 'inline-block' }}>
                    <Selector
                        options={options}
                        value={pageSize}
                        onChange={this.paginationSelectorChange}
                        style={{ width: '110px' }}
                        size={size}
                        disabled={disabled}
                    />
                </span>
            );
        }
        // 极简模式下中间页码展示内容
        let simplePage = null;
        if (showCurrentPage) {
            simplePage = (
                <Page
                    active
                    key={g.guid('pagination')}
                    page={current}
                    showTitle={showTitle}
                />
            );
        }
        // 如果只有一页且设置了hideOnSinglePage，则不渲染分页器
        if (hideOnSinglePage && allPages <= 1) {
            return null;
        }
        return (
            <ul
                className={classNames(clx, className, {
                    [`${this.context.prefixCls}-pagination-disabled`]: disabled,
                })}
                style={style}
            >
                {totalText}
                {paginationSelector}
                <li
                    className={this.hasPrev() ? prevClass : 'disabled'}
                    onClick={this.handleClickPrev}
                >
                    {this.props.prevText ? (
                        <a
                            className="text"
                            aria-label="Previous"
                            onClick={preventDefault}
                        >
                            {this.props.prevText}
                        </a>
                    ) : (
                        <a aria-label="Previous">
                            <span aria-hidden="true">
                                {/* 正常情况为' < 'RTL情况下为' > ' */}
                                <i
                                    className={`roo-icon roo-icon-chevron-${
                                        this.context.direction === 'RTL' ? 'right-new' : 'left-new'
                                    }`}
                                />
                            </span>
                        </a>
                    )}
                </li>
                {this.props.showPager ? pagesList : simplePage}
                <li
                    className={this.hasNext() ? prevClass : 'disabled'}
                    onClick={this.handleClickNext}
                >
                    {this.props.nextText ? (
                        <a
                            className="text"
                            aria-label="Next"
                            onClick={preventDefault}
                        >
                            {this.props.nextText}
                        </a>
                    ) : (
                        <a
                            aria-label="Next"
                            onClick={preventDefault}
                        >
                            <span aria-hidden="true">
                                {/* 正常情况为' > 'RTL情况下为' < ' */}
                                <i
                                    className={`roo-icon roo-icon-chevron-${
                                        this.context.direction === 'RTL' ? 'left-new' : 'right-new'
                                    }`}
                                />
                            </span>
                        </a>
                    )}
                </li>
                {showJumper && (
                    <div className={`${this.context.prefixCls}-pagination-jump`}>
                        <Jump
                            size={size}
                            allPages={allPages}
                            onChange={this.handlePageChange}
                            disabled={disabled}
                        />
                    </div>
                )}
            </ul>
        );
    }
}

polyfill(Pagination);
export default Pagination;
