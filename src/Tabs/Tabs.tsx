import * as React from 'react';
import * as PropTypes from 'prop-types';
import classnames from 'classnames';
import { polyfill } from 'react-lifecycles-compat';
import warning from 'warning';
import debounce from 'lodash/debounce';
import { GlobalConfigContext } from '@roo/roo/ConfigProvider';
import { isFunction, isArray } from '@utiljs/is';
import compact from 'lodash/compact';
import TabPane from './TabPane';
import TabContent from './TabContent';
import ScrollTableBar from './ScrollTableBar';

import {
    TabsProps,
    TabsState,
    Direction,
    ScrollBarProps,
    TabPaneProps
} from './interface';
import { getInstanceName } from '../_utils/reactUtils';

const getItemsTabPane = (items: TabPaneProps[] | undefined) => {
    const mappedItems = items?.map(item => {
        if (!item) return null;
        const { children, ...res } = item;
        return (
            <TabPane
                {...res}
                key={item.name}
            >
                {children || null}
            </TabPane>
        );
    });
    return compact(mappedItems);
};

const getDefaultValue = (props: TabsProps) => {
    let value: any;
    let childs = props.children;
    if ((!props.children || (isArray(props.children) && props.children.length === 0)) && props.items && props.items.length) {
        childs = getItemsTabPane(props.items);
    }
    React.Children.forEach(childs, (child: any) => {
        if (child && !value && !child.props.disabled) {
            value = child.props.name;
        }
    });

    return value;
};

class Tabs extends React.Component<TabsProps, TabsState> {
    static contextType = GlobalConfigContext;
    context!: React.ContextType<typeof GlobalConfigContext>;

    static propTypes = {
        type: PropTypes.oneOf(['card', 'fill', 'line']),
        autoResize: PropTypes.bool,
        defaultValue: PropTypes.string,
        value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        children: PropTypes.node,
        onTabClick: PropTypes.func,
        className: PropTypes.string,
        style: PropTypes.object,
        reRender: PropTypes.bool,
        placement: PropTypes.oneOf<Direction>(['left', 'right', 'top', 'bottom']),
        autoBottomLineWidth: PropTypes.bool,
        renderTabBar: PropTypes.func,
        onTabScroll: PropTypes.func,
        onChange: PropTypes.func,
        items: PropTypes.array,
        animated: PropTypes.oneOfType([PropTypes.bool, PropTypes.object]),
    };

    static defaultProps = {
        type: 'card',
        autoResize: false,
        defaultValue: undefined,
        value: undefined,
        children: [],
        onTabClick: () => {},
        className: '',
        style: {},
        reRender: false,
        placement: 'top' as Direction,
        autoBottomLineWidth: false,
        items: undefined,
        renderTabBar: undefined,
        onTabScroll: undefined,
        onChange: undefined,
        animated: { inkBar: true, tabPane: false },
    };

    static Pane = TabPane;

    constructor(props: TabsProps) {
        super(props);

        let value;
        if (props.value !== undefined) {
            ({ value } = props);
        } else if (props.defaultValue !== undefined) {
            value = props.defaultValue;
        } else {
            value = getDefaultValue(props);
        }

        this.state = {
            value,
        };
    }

    static getDerivedStateFromProps(nextProps: TabsProps, state: TabsState) {
        if (nextProps.value !== undefined) {
            return {
                value: nextProps.value,
            };
        }
        if (state.value === undefined) {
            return nextProps.defaultValue !== undefined
                ? {
                      value: nextProps.defaultValue,
                  }
                : {
                      value: getDefaultValue(nextProps),
                  };
        }
        return null;
    }

    getTabIndex = (value: any, realChildren?: any): number => {
        let tabIndex = -1;
        React.Children.forEach(realChildren || this.getValidChildren, (child: any, index) => {
            if (child && child.props && child.props.name === value) {
                tabIndex = index;
            }
        });
        return tabIndex;
    };

    handleTabClick = (name: string | number) => {
        const { onTabClick, onChange } = this.props;
        this.setState({
            value: name,
        });
        /* eslint-disable */
        onTabClick && onTabClick(name);
        if (name !== this.state.value && isFunction(onChange)) onChange(name);
    };

    get getValidChildren() {
        const { children } = this.props;

        // @ts-expect-error
        const validChildren = React.Children.map(children, child => child).filter(child => {
            // displayName 可能会被某些插件改写，所以做了两个 name 的判断
            if (
                React.isValidElement(child) &&
                (getInstanceName(child) === 'RooTabPane' || getInstanceName(child) === 'TabPane')
            ) {
                return true;
            }
            warning(false, 'Roo Tabs: Tabs 内子元素只能为 TabPane');
            return false;
        });
        return validChildren;
    }

    componentDidMount(): void {
        const resize = debounce(() => {
            this.forceUpdate();
        }, 100);
        window.addEventListener('resize', resize);
    }

    render() {
        const { value } = this.state;
        const {
            type,
            autoResize,
            className,
            style,
            children,
            placement,
            autoBottomLineWidth,
            renderTabBar,
            items,
            size = this.context.size || 'normal',
            animated = { inkBar: true, tabPane: false },
            ...restProps
        } = this.props;

        let index = this.getTabIndex(value);
        const validChildren = this.getValidChildren;
        const itemsExist = items && isArray(items);
        const isNeedReturnNull = validChildren && validChildren.length === 0 && !itemsExist;
        if (isNeedReturnNull) {
            return null;
        }

        const tabClx = classnames(
            `${this.context.prefixCls}-tabs`,
            `${this.context.prefixCls}-tabs-${type}`,
            {
                [`${this.context.prefixCls}-tabs-flexable`]: autoResize,
                [`${this.context.prefixCls}-tabs-vertical`]: placement === 'left' || placement === 'right',
                [`${this.context.prefixCls}-tabs-vertical-right`]: placement === 'right',
                [`${this.context.prefixCls}-tabs-bottom`]: placement === 'bottom',
            },
            className,
        );

        let realChildren: any = validChildren;
        if (validChildren.length === 0 && items!.length !== 0) {
            realChildren = getItemsTabPane(items);
            index = this.getTabIndex(value, realChildren);
        }
        const props: ScrollBarProps = {
            ...restProps,
            size,
            children: realChildren,
            autoResize: autoResize,
            tabIndex: index,
            autoBottomLineWidth: autoBottomLineWidth,
            value: value,
            type: type,
            placement: placement,
            onTabClick: this.handleTabClick,
            animated,
        };

        return (
            <div
                className={tabClx}
                style={style}
            >
                {renderTabBar ? (
                    renderTabBar(props, ScrollTableBar)
                ) : (
                    <ScrollTableBar
                        {...restProps}
                        size={size}
                        children={realChildren}
                        autoResize={autoResize}
                        tabIndex={index}
                        autoBottomLineWidth={autoBottomLineWidth}
                        value={value}
                        type={type}
                        placement={placement}
                        onTabClick={this.handleTabClick}
                        animated={animated}
                    />
                )}
                <TabContent
                    {...restProps}
                    children={realChildren}
                    value={value}
                    animated={animated}
                />
            </div>
        );
    }
}

polyfill(Tabs);
export default Tabs;
