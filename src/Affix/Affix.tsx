import * as React from 'react';
import * as PropTypes from 'prop-types';
import { type } from '@utiljs/type';
import { isFunction } from '@utiljs/is';
import { createRef } from '@roo/create-react-ref';

import { AffixProps, AffixState } from './interface';
import { findDOMNode } from '../_utils/Dom';

let initContainerClientRect = { top: 0, left: 0, bottom: 0 };

class Affix extends React.Component<AffixProps, AffixState> {
    static propTypes = {
        container: PropTypes.oneOfType([PropTypes.object, PropTypes.func]), // eslint-disable-line
        offsetTop: PropTypes.number,
        offsetBottom: PropTypes.number,
        onAffix: PropTypes.func,
        zIndex: PropTypes.number,
        className: PropTypes.string,
        style: PropTypes.object,
    }

    static defaultProps = {
        className: '',
        style: {},
        zIndex: 1000,
        offsetTop: null,
        offsetBottom: null,
        onAffix: () => { }
    }

    private affixRef: React.RefObject<HTMLInputElement> = createRef();

    private affixChildrenRef: React.RefObject<HTMLInputElement> = createRef();

    constructor(props: AffixProps) {
        super(props);
        this.state = {
            startAffix: false,
            style: {},
            containerStyle: {},
        };
    }

    getContainer = () => {
        const { container } = this.props;
        const contain = (isFunction(container) ? container() : container);
        // eslint-disable-next-line
        return findDOMNode(contain as HTMLElement);
    };

    getContain = () => {
        const { container } = this.props;
        const contain = container ? this.getContainer() : window;

        return contain as HTMLElement || window;
    }

    on = (node: HTMLElement | Window, eventName: string, callback: () => void) => {
        if (node.addEventListener) {
            node.addEventListener(eventName, callback, false);
        }
    }

    off = (node: HTMLElement | Window, eventName: string, callback: () => void) => {
        if (node.removeEventListener) {
            node.removeEventListener(eventName, callback, false);
        }
    }

    getScrollTop = (node: HTMLElement | Window, isVertical: boolean) => {
        const windowProp = isVertical ? 'pageYOffset' : 'pageXOffset';
        const elementProp = isVertical ? 'scrollTop' : 'scrollLeft';

        return (type(node) === 'window' || type(node) === 'global' ? (node as Window)[windowProp] : (node as HTMLElement)[elementProp]) || 0;
    }

    getClientRect = (node: HTMLElement | Window) => {
        let rect = { top: 0, left: 0, bottom: 0 };

        if (type(node) !== 'window' && type(node) !== 'global') rect = (node as HTMLElement).getBoundingClientRect();

        return rect;
    }

    getNodeOffset = (affixNode: HTMLElement, affixContainer: HTMLElement | Window) => {
        const affixRect = affixNode.getBoundingClientRect(); // affix children 相对浏览器窗口的位置
        const containerRect = this.getClientRect(affixContainer); // affix 外部容器容器 相对浏览器窗口的位置
        const containerScrollTop = this.getScrollTop(affixContainer, true);
        const containerScrollLeft = this.getScrollTop(affixContainer, false);

        return {
            top: affixRect.top - containerRect.top + containerScrollTop, // eslint-disable-line
            left: affixRect.left - containerRect.left + containerScrollLeft, // eslint-disable-line
            width: affixRect.width,
            height: affixRect.height,
        };
    }

    getNodeHeight = (node: HTMLElement | Window) => {
        // if (!node) {
        //     return 0;
        // }

        if (type(node) === 'window' || type(node) === 'global') {
            return window.innerHeight;
        }

        return (node as HTMLElement).clientHeight;
    }

    resizePosition = () => {
        const contain = this.getContain();
        const { offsetTop, offsetBottom, onAffix } = this.props;

        const containerScrollTop = this.getScrollTop(contain, true); // 容器在垂直位置上的滚动 offset
        const affixOffset = this.affixRef.current && this.getNodeOffset(this.affixRef.current, contain); // 目标节点当前相对于容器的 offset
        const containerHeight = this.getNodeHeight(contain); // 容器的高度
        const affixHeight = this.affixRef.current?.offsetHeight || 0;
        const containerClientRect = this.getClientRect(contain);

        const style: React.CSSProperties = {
            width: affixOffset?.width,
        };

        const containerStyle = {
            width: affixOffset?.width,
            height: this.affixChildrenRef.current?.offsetHeight,
        };

        let startAffix = false;
        if (type(offsetTop) === 'number' && containerScrollTop > (affixOffset?.top || 0) - offsetTop!) {
            //当传入的为offsetTop时，并且距离顶部的位置大于offsetTop时
            style.position = 'fixed';
            style.top = (offsetTop || 0) + (containerClientRect.top > 0 ? containerClientRect.top : initContainerClientRect.top); // eslint-disable-line
            startAffix = true;
            if (isFunction(onAffix)) onAffix(true);
        } else if (type(offsetBottom) === 'number' && containerScrollTop > (affixOffset?.top || 0) + affixHeight + offsetBottom! - containerHeight) { // eslint-disable-line
            //当传入的为offsetBottom时，并且距离底部的位置小于offsetBottom时
            style.position = 'fixed';
            style.bottom = offsetBottom;
            startAffix = true;

            if (isFunction(onAffix)) onAffix(true);
        } else {
            startAffix = false;
            if (isFunction(onAffix)) onAffix(false);
        }

        this.setState({
            style,
            containerStyle,
            startAffix,
        });
    }

    componentDidMount() {
        const contain = this.getContain();
        initContainerClientRect = this.getClientRect(contain);

        this.on(contain, 'scroll', this.resizePosition);
        window.addEventListener('resize', this.resizePosition, false);

        this.resizePosition();
    }

    componentWillUnmount() {
        const contain = this.getContain();

        this.off(contain, 'scroll', this.resizePosition);
        window.removeEventListener('resize', this.resizePosition);
    }

    render(): React.ReactNode {
        const {
            children,
            zIndex,
            style: customStyle,
            className
        } = this.props;
        const { style, containerStyle, startAffix } = this.state;

        return (
            <div
                ref={this.affixRef}
                className={className}
                style={customStyle}
            >
                {
                    startAffix && (
                        <div
                            style={containerStyle}
                        />
                    )
                }
                <div
                    ref={this.affixChildrenRef}
                    style={{
                        ...style,
                        zIndex
                    }}
                >
                    {children}
                </div>
            </div>
        );
    }
}

export default Affix;
