import * as React from 'react';
import ReactDOM from 'react-dom';
import * as PropTypes from 'prop-types';
import Portal from '@roo/roo/core/Portal';
import { isFunction, isString, isNumber } from '@utiljs/is';
import { createRef } from '@roo/create-react-ref';
import { contains } from '@utiljs/dom';
import omit from 'lodash/omit';
import classNames from 'classnames';

import { GlobalConfigContext } from '@roo/roo/ConfigProvider';
import { disableBodyScroll, enableBodyScroll } from '../_utils/Dom';
import { DrawerProps } from './interface';
import DrawerContext from './context';

// 105 是为了减去阴影的影响
const transforms: { [key: string]: string } = {
    top: 'translateY(-105%)',
    right: 'translateX(105%)',
    bottom: 'translateY(105%)',
    left: 'translateX(-105%)',
};

// 用于追踪可见抽屉层级
const visibleDrawers: number[] = [];
let topLevel: number | null = null;

interface DrawerState {
    pushed: boolean;
}

class Drawer extends React.Component<DrawerProps, DrawerState> {
    static defaultProps = {
        width: '300px',
        height: '256px',
        placement: 'left',
        backdrop: true,
        backdropClosable: true,
        duration: 300,
        scrollable: true,
        closable: false,
        backdropStyle: undefined,
        backdropClassName: undefined,
        headerStyle: undefined,
        footerStyle: undefined,
        bodyStyle: undefined,
        zIndex: 1000,
        title: null,
        extra: null,
        footer: null,
        closeIcon: null,
        popupContainer: document.body,
        size: undefined,
        keyboard: true,
        push: false,
        forceRender: false,
        destroyOnClose: false,
        autoFocus: true,
    };

    static propTypes = {
        width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        placement: PropTypes.oneOf(['left', 'right', 'top', 'bottom']),
        backdrop: PropTypes.bool,
        backdropClosable: PropTypes.bool,
        closable: PropTypes.bool,
        closeIcon: PropTypes.node,
        scrollable: PropTypes.bool,
        duration: PropTypes.number,
        zIndex: PropTypes.number,
        backdropStyle: PropTypes.object,
        backdropClassName: PropTypes.string,
        headerStyle: PropTypes.object,
        footerStyle: PropTypes.object,
        bodyStyle: PropTypes.object,
        title: PropTypes.node,
        extra: PropTypes.node,
        footer: PropTypes.node,
        popupContainer: PropTypes.oneOfType([PropTypes.instanceOf(Element), PropTypes.func]),
        size: PropTypes.oneOf(['normal', 'large', 'compact']),
        keyboard: PropTypes.bool,
        push: PropTypes.oneOfType([
            PropTypes.bool,
            PropTypes.shape({
                distance: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
            }),
        ]),
        forceRender: PropTypes.bool,
        destroyOnClose: PropTypes.bool,
        autoFocus: PropTypes.bool,
    };

    static contextType = GlobalConfigContext;
    context!: React.ContextType<typeof GlobalConfigContext>;

    contentRef: React.RefObject<HTMLDivElement> = createRef();
    portalRef: React.RefObject<HTMLDivElement> = createRef();

    private drawerContext!: React.ContextType<typeof DrawerContext>;

    // 为每个Drawer实例分配唯一level
    static levelCounter = 1;
    private level: number;

    constructor(props: DrawerProps) {
        super(props);
        this.state = {
            pushed: false,
        };

        this.level = Drawer.levelCounter;
        Drawer.levelCounter += 1;
    }

    componentDidMount() {
        const { visible, scrollable } = this.props;

        // 如果当前Drawer是可见的，将其level加入可见列表并更新topLevel
        if (visible) {
            visibleDrawers.push(this.level);
            topLevel = Math.max(...visibleDrawers);
        }

        if (visible && !scrollable) disableBodyScroll();

        if (visible && this.drawerContext && this.drawerContext.push) {
            this.drawerContext.push();
        }
    }

    componentDidUpdate(prevProps: DrawerProps) {
        const { visible, scrollable } = this.props;

        if (visible !== prevProps.visible) {
            // 可见性变化时更新全局可见列表和topLevel
            if (visible) {
                visibleDrawers.push(this.level);
            } else {
                const idx = visibleDrawers.indexOf(this.level);
                if (idx > -1) {
                    visibleDrawers.splice(idx, 1);
                }
            }
            topLevel = visibleDrawers.length ? Math.max(...visibleDrawers) : null;

            if (visible && this.drawerContext && this.drawerContext.push) {
                this.drawerContext.push();
            } else if (!visible && this.drawerContext && this.drawerContext.pull) {
                this.drawerContext.pull();
            }
        }

        if (visible !== prevProps.visible && visible) {
            this.handleFocus();
        }

        if (visible !== prevProps.visible && visible && !scrollable) {
            disableBodyScroll();
        }

        if (visible !== prevProps.visible && !visible) {
            enableBodyScroll();
        }
    }

    componentWillUnmount() {
        // 卸载时，如果仍然可见，需要清理全局数据
        if (this.props.visible) {
            const idx = visibleDrawers.indexOf(this.level);
            if (idx > -1) {
                visibleDrawers.splice(idx, 1);
            }
            topLevel = visibleDrawers.length ? Math.max(...visibleDrawers) : null;
            enableBodyScroll();
        }

        if (this.drawerContext && this.drawerContext.pull) {
            this.drawerContext.pull();
        }
    }

    push = () => {
        this.setState({ pushed: true });
    };

    pull = () => {
        this.setState({ pushed: false });
    };

    handleFocus = () => {
        const { visible, autoFocus } = this.props;
        if (visible && autoFocus && this.portalRef && this.portalRef.current) {
            this.portalRef.current.focus();
        }
    };

    private handleClickOutside = (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
        const { visible, onClickOutside, onClose, backdropClosable } = this.props;
        if (!visible) return;

        const { current: domNode } = this.contentRef;
        const { current: portalNode } = this.portalRef;
        // React-portal agents event，so DOM should be in portal-dom
        if (
            (!domNode || !contains(domNode as HTMLElement, event.target as HTMLElement)) &&
            contains(portalNode as HTMLElement, event.target as HTMLElement)
        ) {
            if (!backdropClosable) return;
            if (isFunction(onClickOutside)) {
                onClickOutside();
            }
            if (isFunction(onClose)) {
                onClose(event, 'backdrop');
            }
        }
    };

    private handleESC = (event: React.KeyboardEvent) => {
        const { visible, keyboard, onClose, onESC } = this.props;
        // 不是可见的、或keyboard=false、或不是最高层就不处理ESC
        if (!visible || !keyboard || (topLevel !== null && this.level !== topLevel)) return;
        if (event.keyCode === 27) {
            event.stopPropagation();

            if (isFunction(onESC)) {
                onESC();
            }
            if (isFunction(onClose)) {
                onClose(event, 'esc');
            }
        }
    };

    private handleTransitionEnd = (event: React.TransitionEvent) => {
        if (event.target === this.contentRef.current && event.propertyName.match(/transform$/)) {
            const { onOpened, onClosed, visible } = this.props;
            if (isFunction(onOpened) && visible) {
                onOpened();
            }
            if (isFunction(onClosed) && !visible) {
                onClosed();
            }
        }
    };

    private handleIconClose = (event: React.MouseEvent<HTMLButtonElement>) => {
        const { visible, onClose } = this.props;
        if (!visible) return;
        if (isFunction(onClose)) {
            onClose(event, 'closeicon');
        }
    };

    private closeIconNode(prefixCls: string) {
        const { closable, closeIcon } = this.props;
        return (
            closable && (
                <button
                    type="button"
                    className={`${prefixCls}-drawer-close`}
                    data-dismiss={`${prefixCls}-drawer`}
                    onClick={this.handleIconClose}
                >
                    {closeIcon || (
                        <span
                            aria-hidden="true"
                            className="roo-icon roo-icon-close-new"
                        />
                    )}
                </button>
            )
        );
    }

    private renderHeader(prefixCls: string): React.ReactNode | null {
        const { closable, title, extra, headerStyle } = this.props;
        if (!title && !closable) return null;
        return (
            <div
                className={classNames(`${prefixCls}-drawer-header`, {
                    [`${prefixCls}-drawer-header-close-only`]: closable && !title && !extra,
                })}
                style={headerStyle}
            >
                <div className={`${prefixCls}-drawer-header-title`}>
                    {this.closeIconNode(prefixCls)}
                    {title && <div className={`${prefixCls}-drawer-title`}>{title}</div>}
                </div>
                {extra && <div className={`${prefixCls}-drawer-extra`}>{extra}</div>}
            </div>
        );
    }

    private renderBody(prefixCls: string) {
        const { children, bodyStyle, loading, visible, forceRender, destroyOnClose } = this.props;

        if (!forceRender && !visible && destroyOnClose) {
            return null;
        }

        return (
            <div
                className={`${prefixCls}-drawer-body`}
                style={bodyStyle}
            >
                {loading ? (
                    <div className={`${prefixCls}-drawer-loading`}>
                        <span className={`${prefixCls}-drawer-loading-icon roo-icon roo-icon-loading`} />
                    </div>
                ) : (
                    children
                )}
            </div>
        );
    }

    private renderFooter(prefixCls: string) {
        const { footer, footerStyle } = this.props;
        if (!footer) return null;
        return (
            <div
                className={`${prefixCls}-drawer-footer`}
                style={footerStyle}
            >
                {footer}
            </div>
        );
    }

    getContainer = (container?: HTMLElement | (() => HTMLElement | null)): any => {
        const contain: HTMLElement | undefined | null = isFunction(container) ? container() : container;
        return contain;
    };

    getWidth = () => {
        const { width, popupContainer, size, placement } = this.props;
        const globalConfig = this.context;
        const finalSize = size || globalConfig?.size;
        // 如果设置了 size，且没有手动设置 width（width 为默认值），则使用预设尺寸
        if (finalSize && (placement === 'left' || placement === 'right') && width === '300px') {
            return finalSize === 'normal' || finalSize === 'compact' ? 300 : 600;
        }
        const container = this.getContainer(popupContainer);
        const viewportWidth = container?.getBoundingClientRect()?.width || window.innerWidth;
        const _width = isString(width) && width?.endsWith('px') ? parseInt(width, 10) : width;
        return isNumber(_width) ? Math.min(viewportWidth, _width) : _width;
    };

    getHeight = () => {
        const { height, size, placement } = this.props;
        const globalConfig = this.context;
        const finalSize = size || globalConfig?.size;
        if (finalSize && (placement === 'top' || placement === 'bottom') && height === '256px') {
            return finalSize === 'normal' || finalSize === 'compact' ? 256 : 512;
        }
        return height;
    };

    render() {
        return (
            <DrawerContext.Consumer>
                {drawerContext => {
                    this.drawerContext = drawerContext;
                    const prefixCls = this.context?.prefixCls ?? 'roo';

                    const {
                        zIndex,
                        popupContainer,
                        placement,
                        children,
                        backdrop,
                        visible,
                        height,
                        style,
                        duration,
                        className,
                        backdropClassName,
                        headerStyle,
                        footerStyle,
                        bodyStyle,
                        backdropClosable,
                        closable,
                        closeIcon,
                        push,
                        title,
                        backdropStyle: backdropPropsStyle,
                        forceRender,
                        destroyOnClose,
                        autoFocus,
                        ...restProps
                    } = this.props;

                    const getDefaultPushDistance = () => {
                        if (!push) return 0;
                        return typeof push === 'boolean' ? 180 : push.distance || 180;
                    };

                    const pushDistance = getDefaultPushDistance();

                    let backdropStyle: React.CSSProperties = {};
                    let portalStyle: React.CSSProperties = {
                        zIndex,
                    };
                    let drawerStyle: React.CSSProperties = {};

                    if (!visible) {
                        backdropStyle = {
                            width: 0,
                            opacity: 0,
                        };
                        portalStyle = {
                            width: 0,
                            zIndex,
                        };
                        drawerStyle = {
                            transform: transforms[placement || 'left'],
                        };
                    } else {
                        drawerStyle = {
                            transform: 'none',
                        };
                    }

                    if (this.state.pushed && pushDistance) {
                        const placements = placement || 'left';
                        if (placements === 'left') {
                            drawerStyle.transform = `translateX(${pushDistance}px)`;
                        } else if (placements === 'right') {
                            drawerStyle.transform = `translateX(-${pushDistance}px)`;
                        } else if (placements === 'top') {
                            drawerStyle.transform = `translateY(${pushDistance}px)`;
                        } else if (placements === 'bottom') {
                            drawerStyle.transform = `translateY(-${pushDistance}px)`;
                        }
                    }

                    const contentStyle: React.CSSProperties = {
                        width: placement === 'left' || placement === 'right' ? this.getWidth() : '100%',
                        height: placement === 'top' || placement === 'bottom' ? this.getHeight() : '100%',
                        transition: `transform ${duration}ms cubic-bezier(0.7,0,0.3,1) 0ms`,
                        [placement || 'left']: 0,
                        zIndex: 1000,
                    };

                    const size = this.props.size || this.context.size || 'normal';
                    const clx = classNames(`${prefixCls}-drawer`, className, `${prefixCls}-drawer-${size}`);
                    const restDomProps = omit(restProps, [
                        'title',
                        'onClickOutside',
                        'onESC',
                        'onClose',
                        'onClosed',
                        'onOpened',
                        'scrollable',
                        'keyboard',
                        'loading',
                        'push',
                        'size',
                        'forceRender',
                        'destroyOnClose',
                    ]);

                    return (
                        <DrawerContext.Provider
                            value={{
                                push: this.push,
                                pull: this.pull,
                                pushDistance,
                            }}
                        >
                            <Portal container={popupContainer}>
                                <div
                                    tabIndex={autoFocus ? -1 : undefined}
                                    onKeyDown={this.handleESC}
                                    ref={this.portalRef}
                                    className={`${prefixCls}-drawer-portal`}
                                    style={{
                                        ...portalStyle,
                                    }}
                                >
                                    {backdrop && (
                                        <div
                                            className={`${prefixCls}-backdrop`}
                                            onClick={this.handleClickOutside}
                                            style={{
                                                ...backdropPropsStyle,
                                                ...backdropStyle,
                                            }}
                                        />
                                    )}
                                    <div
                                        className={clx}
                                        onTransitionEnd={this.handleTransitionEnd}
                                        ref={this.contentRef}
                                        style={{
                                            ...style,
                                            ...contentStyle,
                                            ...drawerStyle,
                                        }}
                                        {...restDomProps}
                                    >
                                        {this.renderHeader(prefixCls)}
                                        {this.renderBody(prefixCls)}
                                        {this.renderFooter(prefixCls)}
                                    </div>
                                </div>
                            </Portal>
                        </DrawerContext.Provider>
                    );
                }}
            </DrawerContext.Consumer>
        );
    }
}

export default Drawer;

// TIPS:
// tabIndex 照理应该在 content 上，但是会出现动画抖动的问题。
