import * as React from 'react';
import * as PropTypes from 'prop-types';
import Trigger from '@rc-component/trigger';
import dayjs from 'dayjs';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import isoWeek from 'dayjs/plugin/isoWeek';
import 'dayjs/locale/zh-cn';
import { polyfill } from 'react-lifecycles-compat';
import { createRef } from '@roo/create-react-ref';
import locale from '@roo/roo/locale';
import { isFunction, isArray, isObject, isUndefined } from '@utiljs/is';
import { Manager, Reference, PopperPortal } from '@roo/roo/core/Popper';
import classNames from 'classnames';
import warning from 'warning';
import { GlobalConfigContext } from '@roo/roo/ConfigProvider';
import Icon from '../Icon';
import IconClear from './IconClear';
import { PANEL_LIST, END_PANEL_LIST } from './const';
import { getTimeValue, togglePanel, dateFormatter, initRangePickerFormat, getFormat, change12HoursTime } from './utils';
import { getPlacements } from './placements';
import Input from '../Input';
import Button from '../Button';
import DatePanel from './DatePanel';
import MonthPanel from './MonthPanel';
import YearPanel from './YearPanel';
import TimePicker from './TimePicker';
import { RangePickerProps, PanelMode } from './interface';
import { somePlacements, placementEnum, placementTypes } from '../_utils/types';
import DayjsLocalProvider from './DayjsLocalProvider';
import withDisabled from '../_utils/hoc/withDisabled';

dayjs.extend(weekOfYear);
dayjs.extend(isoWeek);
// dayjs.locale('zh-cn');
// interface RangePickerState {
//     startDate: {}; // 第一个渲染器对应的当前渲染时间
//     selectDate: {}; // 第一个渲染器选中时间
//     endDate: {}; // 第二个渲染器对应的当前渲染时间
//     selectEndDate: {};
// }

const initEditableOptions = {
    inputValueTexts: ['', ''],
    inputStatus: [false, false],
    inputValid: [false, false],
};

interface ExtraProps {
    position: 'pre' | 'aft';
}

enum RangeIndex {
    START_INDEX = 0,
    END_INDEX = 1,
}

class RangePicker extends React.Component<RangePickerProps & ExtraProps, any> {
    static contextType = GlobalConfigContext;
    context!: React.ContextType<typeof GlobalConfigContext>;

    static defaultProps = {
        defaultValue: undefined,
        className: '',
        inputClassName: '',
        value: undefined,
        format: 'YYYY-MM-DD',
        valueOfType: 'timestamp',
        placement: undefined,
        flip: true,
        popupContainer: document.body || document.documentElement,
        showTime: false,
        clearable: false,
        placeholder: [],
        // size: 'normal' as Size,
        bordered: true,
        disabled: false,
        open: undefined,
        autoFocus: false,
        timeSteps: undefined,
        popupClassName: null,
        inputReadOnly: false,
        use12Hours: false,
        ranges: {},
        presets: {},
        type: 'date',
        disabledTime: () => false,
        disabledDate: () => false,
        onBlur: () => {},
        onFocus: () => {},
        onChange: () => {},
        onCalendarChange: () => {},
        valueRender: undefined,
        isCloseMotion: false,
        disablePortal: false,
        zIndex: undefined,
        variant: undefined,
    };

    static propTypes = {
        defaultValue: PropTypes.array,
        className: PropTypes.string,
        inputClassName: PropTypes.string,
        valueOfType: PropTypes.string,
        value: PropTypes.array,
        format: PropTypes.string,
        showTime: PropTypes.oneOfType([PropTypes.bool, PropTypes.object]),
        clearable: PropTypes.bool,
        placeholder: PropTypes.array,
        // size: PropTypes.oneOf(sizes),
        disabled: PropTypes.oneOfType([PropTypes.bool, PropTypes.array]),
        bordered: PropTypes.bool,
        ranges: PropTypes.object,
        presets: PropTypes.object,
        flip: PropTypes.bool,
        placement: PropTypes.oneOf(somePlacements),
        popupContainer: PropTypes.oneOfType([PropTypes.instanceOf(HTMLElement), PropTypes.func]),
        disabledTime: PropTypes.func,
        disabledDate: PropTypes.func,
        open: PropTypes.bool,
        autoFocus: PropTypes.bool,
        onChange: PropTypes.func,
        onBlur: PropTypes.func,
        onFocus: PropTypes.func,
        onCalendarChange: PropTypes.func,
        timeSteps: PropTypes.object,
        popupClassName: PropTypes.string,
        inputReadOnly: PropTypes.bool,
        type: PropTypes.oneOf(['week', 'date']),
        use12Hours: PropTypes.bool,
        valueRender: PropTypes.func,
        isCloseMotion: PropTypes.bool,
        disablePortal: PropTypes.bool,
        zIndex: PropTypes.number,
        variant: PropTypes.oneOf(['line', 'plaintext']),
    };

    fold: boolean = false;

    private rootNode: any = createRef();

    private inputNode: any = null;

    private startInputRef = createRef<HTMLInputElement>();

    private endInputRef = createRef<HTMLInputElement>();

    constructor(props: RangePickerProps & ExtraProps) {
        super(props);
        const { value, defaultValue, valueOfType, use12Hours } = this.props;

        if (value !== undefined && value !== null && !Array.isArray(value)) {
            warning(false, 'RangePicker: value 类型应该是数组', value);
        }
        if (defaultValue !== undefined && defaultValue !== null && !Array.isArray(defaultValue)) {
            warning(false, 'RangePicker: defaultValue 类型应该是数组', defaultValue);
        }
        let newUse12HoursType: string[] = [];

        // eslint-disable-next-line
        let valueArray = Array.isArray(value) ? value : (Array.isArray(defaultValue) ? defaultValue : ['', '']);
        if (use12Hours && valueOfType === 'string') {
            valueArray = valueArray.map((valueItem, index) => {
                const { value: itemValue, use12HoursType } = change12HoursTime(valueItem as string);
                newUse12HoursType[index] = use12HoursType;
                return itemValue;
            });
        }
        if (use12Hours && (!value || !value.length)) {
            const period = dayjs().hour() < 12 ? 'AM' : 'PM';
            newUse12HoursType = [period, period];
        }
        const startValue = dateFormatter(valueArray[0], valueOfType);
        const endValue = dateFormatter(valueArray[1], valueOfType);
        const formattedDay = this.formatDefaultDay();
        let firstDate = startValue || formattedDay[0];
        let secondDate = endValue || formattedDay[1].add(1, 'month');
        const selectDate: any = startValue || endValue || formattedDay[0];
        const selectEndDate = endValue || startValue || formattedDay[1];
        // 同月判断
        if (startValue && endValue) {
            if (startValue.year() === endValue.year() && startValue.month() === endValue.month()) {
                secondDate = startValue.add(1, 'month');
            }
        } else {
            if (startValue) {
                secondDate = startValue.add(1, 'month');
            } else if (endValue) {
                firstDate = endValue.subtract(1, 'month');
            }
        }

        this.state = {
            startValue,
            // eslint-disable-next-line
            startInitValue: dateFormatter(valueArray[0], valueOfType, true),
            endValue,
            // eslint-disable-next-line
            endInitValue: dateFormatter(valueArray[1], valueOfType, true),
            startDate: firstDate,
            selectDate,
            endDate: secondDate,
            selectEndDate,
            dateShow: true,
            monthShow: false,
            yearShow: false,
            // eslint-disable-next-line
            cacheValue: value,
            hoverDate: null,
            enddateShow: true,
            endmonthShow: false,
            endyearShow: false,
            closeShow: false,
            format: 'YYYY-MM-DD',
            showPopover: false,
            /**使用12小时制时对hour的辅助判断 */
            use12HoursType: newUse12HoursType,
            timeSelectActiveIndex: 0,
            /**是否是array类型disabled,且只有一个为true */
            isDisabledOneSide: false,
            disabledIndex: null,

            //以下属性为可输入时需要
            /**输入的字符串 */
            inputValueTexts: ['', ''],
            /** 是否为输入状态 */
            inputStatus: [false, false],
            /** 输入值是否匹配 */
            inputValid: [false, false],
            /** 当前聚焦下标 */
            focusIndex: 0,
            /**暂存的输入之前的日期,方便失焦日期回填 */
            temporaryTimeStore: {},
        };
    }

    componentDidMount() {
        const { disabled, valueOfType, value, defaultValue, format, showTime, type, use12Hours } = this.props;
        const defaultValueArray = Array.isArray(defaultValue) ? defaultValue : ['', ''];
        const valueArray = Array.isArray(value) ? value : defaultValueArray;
        const startValue = dateFormatter(valueArray[0], valueOfType);
        const endValue = dateFormatter(valueArray[1], valueOfType);
        const isArrayDisabled = isArray(disabled);
        const newFormat = initRangePickerFormat({
            type,
            format,
            showTime: showTime as boolean,
            use12Hours,
        });
        let newState: any = {
            format: newFormat,
        };
        //判断disabled是否是array形式
        if (isArrayDisabled) {
            const allDisabled = disabled.every(item => !!item);
            if (!allDisabled) {
                const disabledIndex = disabled.findIndex(item => !!item);
                if (disabledIndex === 0) {
                    newState = {
                        ...newState,
                        selectDate: startValue || dayjs(),
                        startValue: dateFormatter(startValue || dayjs(), valueOfType),
                        selectEndDate: endValue || null,
                        endValue: dateFormatter(endValue || null, valueOfType),
                        isDisabledOneSide: true,
                        disabledIndex,
                    };
                } else if (disabledIndex === 1) {
                    newState = {
                        ...newState,
                        selectEndDate: endValue || dayjs(),
                        endValue: dateFormatter(endValue || dayjs(), valueOfType),
                        selectDate: startValue || null,
                        startValue: dateFormatter(startValue || null, valueOfType),
                        isDisabledOneSide: true,
                        disabledIndex,
                    };
                }
            }
        }
        this.setState(newState);
    }

    /**
     * TODO: getDerivedStateFromProps 只用于受控模式下的状态更新，其他情况不处理。
     * 1. 受控模式： 防止 selectMonthOrYear 引起更新
     * 2. 受控定义： 只要是数组，不管是空的还是非空，都判定为受控模式。
     * @param nextProps
     * @param state
     */
    static getDerivedStateFromProps(nextProps: RangePickerProps, state: any) {
        const newState: any = {};
        if (!isUndefined(nextProps.open)) {
            newState.showPopover = nextProps.open;
        }

        if (!Array.isArray(nextProps.value)) {
            if (Array.isArray(state.cacheValue)) {
                // 数组到非数组，判定为由受控变为非受控，重设为当前时间。
                warning(
                    false,
                    'Warning: RangePicker 组件不应该从受控状态变为非受控状态。 More info: https://fb.me/react-controlled-components',
                );
                return {
                    ...newState,
                    ...{
                        startValue: '',
                        endValue: '',
                        startDate: dayjs(),
                        endDate: dayjs().add(1, 'month'),
                        hoverDate: dayjs(),
                        selectDate: dayjs(),
                        selectEndDate: dayjs(),
                        value: nextProps.value,
                        cacheValue: nextProps.value,
                    },
                };
            } else {
                // 非受控状态，不处理更新
                return newState;
            }
        }
        const { value, showTime, type, format, use12Hours, valueOfType, disabled } = nextProps;
        let isDisabledOneSide = state.isDisabledOneSide;
        let disabledIndex = state.disabledIndex;
        if (isArray(disabled) && disabled.some(item => !!item)) {
            disabledIndex = disabled.findIndex(item => !!item);
            isDisabledOneSide = true;
        }
        newState.disabledIndex = disabledIndex;
        newState.isDisabledOneSide = isDisabledOneSide;
        let newValue: any = [...value];
        const newUse12HoursType = state.use12HoursType;

        // 如果 format 发生变化，更新 format
        const newFormat = initRangePickerFormat({
            type,
            format,
            showTime: showTime as boolean,
            use12Hours,
        });
        if (newFormat !== state.format) {
            newState.format = newFormat;
        }

        if (value !== undefined && value !== null && !Array.isArray(value)) {
            warning(false, 'RangePicker: value 类型应该是数组', value);
        }
        if (use12Hours && valueOfType === 'string') {
            newValue = value.map((valueItem, index) => {
                const { value: itemValue, use12HoursType } = change12HoursTime(valueItem as string);
                newUse12HoursType[index] = use12HoursType;
                return itemValue;
            });
        }
        const value0: any = dateFormatter((newValue || [])[0], nextProps.valueOfType);
        const value1: any = dateFormatter((newValue || [])[1], nextProps.valueOfType);

        let endDate = value1;

        if (!value0 && state.startValue && !value1 && state.endValue) {
            let defaultValue: any[] = [];
            if (typeof showTime === 'object') {
                defaultValue = showTime.defaultValue || [];
            }
            const startTimeDefaultValue = defaultValue[0] ? dayjs(defaultValue[0]) : dayjs();
            const endTimeDefaultValue = defaultValue[1] ? dayjs(defaultValue[1]) : dayjs();
            return {
                ...newState,
                ...{
                    startValue: '',
                    endValue: '',
                    startDate: startTimeDefaultValue,
                    endDate: endTimeDefaultValue.add(1, 'month'),
                    hoverDate: startTimeDefaultValue,
                    selectDate: startTimeDefaultValue,
                    selectEndDate: endTimeDefaultValue,
                    use12HoursType: newUse12HoursType,
                },
            };
        }

        if (!value0 && state.startValue) {
            const isNeedClear = !(isDisabledOneSide && disabledIndex === RangeIndex.START_INDEX);
            if (isNeedClear) {
                return {
                    ...newState,
                    ...{
                        startValue: '',
                        startDate: dayjs(),
                        selectDate: dayjs(),
                    },
                };
            }
        }

        if (!value1 && state.endValue) {
            const isNeedClear = !(isDisabledOneSide && disabledIndex === RangeIndex.END_INDEX);
            if (isNeedClear) {
                return {
                    ...newState,
                    ...{
                        endValue: '',
                        startValue: value0,
                        startDate: value0,
                        endDate: dayjs().add(1, 'month'),
                        selectEndDate: dayjs(),
                    },
                };
            }
        }

        // 同年同月，月份加一，避免 picker 渲染相同的结果
        if (value0 && value1 && value0.year() === value1.year() && value0.month() === value1.month()) {
            endDate = value1.add(1, 'month');
        }
        const checkValueChange = (newPropsValue: dayjs.Dayjs, oldValue: dayjs.Dayjs) => {
            if (newPropsValue && !oldValue) {
                return true;
            }
            return newPropsValue?.valueOf() !== oldValue.valueOf();
        };
        const isStartValueChange = checkValueChange(value0, state.startValue);
        const isEndValueChange = checkValueChange(value1, state.endValue);

        if (isStartValueChange || isEndValueChange) {
            let startDate = value0;
            //如果都更新了,将页面展示到结束日期上
            if (isStartValueChange && isEndValueChange) {
                endDate = value1;
                startDate = value1.subtract(1, 'month');

                //如果是disabled array类型,展示到非disabled日期
                if (isDisabledOneSide) {
                    endDate = disabledIndex === RangeIndex.START_INDEX ? value1 : value0.add(1, 'month');
                    startDate = disabledIndex === RangeIndex.START_INDEX ? value1.subtract(1, 'month') : value0;
                }
            }

            //如果只更新了一侧的值, 将页面展示到更新的日期
            if (isStartValueChange) {
                endDate = (startDate || dayjs()).add(1, 'month');
            }
            if (isEndValueChange) {
                startDate = (endDate || dayjs()).subtract(1, 'month');
            }
            return {
                ...newState,
                ...{
                    startValue: value0,
                    endValue: value1,
                    startDate,
                    selectDate: value0, // datePanel 开始时间
                    hoverDate: value1,
                    endDate,
                    selectEndDate: value1, // datePanel 结束时间
                    cacheValue: nextProps.value,
                },
            };
        }
        return newState;
    }

    componentDidUpdate() {
        const { showPopover, selectEndDate, startValue, endValue } = this.state;
        let endDate = endValue;
        if (!showPopover && !selectEndDate && startValue && endValue) {
            if (
                startValue &&
                endValue &&
                startValue.year() === endValue.year() &&
                startValue.month() === endValue.month()
            ) {
                endDate = endValue.add(1, 'month');
            }
            this.setState({
                startDate: startValue,
                selectDate: startValue,
                hoverDate: endValue,
                endDate,
                selectEndDate: endValue,
            });
        }
    }

    getYearWeek(date?: dayjs.Dayjs | null): string {
        if (!date) {
            return '';
        }
        const year = date.isoWeekYear(); // 使用 ISO 周年
        const week = date.isoWeek(); // 使用 ISO 周数

        return `${year}-${week} ${locale.lng('DatePicker.week')}`;
    }

    formatDefaultDay = () => {
        const { showTime } = this.props;
        let defaultValue: any[] = [];
        if (typeof showTime === 'object') {
            defaultValue = showTime.defaultValue || [];
        }
        const startTimeDefaultValue = defaultValue[0] ? dayjs(defaultValue[0]) : dayjs();
        const endTimeDefaultValue = defaultValue[1] ? dayjs(defaultValue[1]) : dayjs();
        const currentDate = [startTimeDefaultValue, endTimeDefaultValue];

        return currentDate;
    };

    /**
     * 受控模式判断
     * @param value any
     */
    isControlled = (value: any): boolean => Array.isArray(value);

    handlePropsChange = <T extends any[]>(time: T, timeString: [string, string] | []) => {
        const { onChange, use12Hours, valueOfType } = this.props;
        const { selectDate, selectEndDate, format } = this.state;
        if (isFunction(onChange)) {
            if (use12Hours && valueOfType === 'string') {
                const { dateFormat } = getFormat(format);
                onChange(time, timeString, [
                    selectDate?.format(`${dateFormat} HH:mm:ss`),
                    selectEndDate?.format(`${dateFormat} HH:mm:ss`),
                ]);
            } else {
                onChange(time, timeString);
            }
        } else {
            warning(false, 'RangePicker: onChange 类型应该是函数');
        }
    };

    handleOpenAfterSelectDate = () => {
        const isOpen = !!this.props.showTime || false;
        if (this.props.onOpenChange) {
            this.props.onOpenChange(isOpen);
        }
        if (!isOpen) {
            this.isCloseShow();
        }
        this.setState({
            showPopover: isOpen,
        });
    };

    getStringValue = (startDate?: dayjs.Dayjs, endDate?: dayjs.Dayjs): [string, string] => {
        const { type } = this.props;
        const { startValue, endValue, format } = this.state;
        const newType = `${type}Range`;
        const start = startDate || startValue;
        const end = endDate || endValue;
        let firstInputValue = (start && start.format(format)) || '';
        let secondInputValue = (end && end.format(format)) || '';
        if (newType === 'weekRange') {
            firstInputValue = this.getYearWeek(start);
            secondInputValue = this.getYearWeek(end);
        }
        return [firstInputValue, secondInputValue];
    };

    handleDateSelect = (timestamp: number) => {
        const { selectEndDate, selectDate, endValue, disabledIndex, isDisabledOneSide, format } = this.state;
        const { valueOfType, value, defaultValue, onCalendarChange, use12Hours } = this.props;
        // eslint-disable-next-line no-nested-ternary
        const valueArray = Array.isArray(value) ? value : Array.isArray(defaultValue) ? defaultValue : ['', ''];
        const { timeFormat } = getFormat(format);
        let nextSelectDate = null;
        let nextSelectEndDate = null;
        let endDate: any;
        //判断是否是数组形式的disable,如果有,将所选值添加到非disable的地方
        if (isDisabledOneSide) {
            if (disabledIndex === 0) {
                endDate = dayjs(timestamp);
                nextSelectEndDate = dayjs(timestamp);
                nextSelectDate = selectDate;
            } else {
                endDate = selectEndDate;
                nextSelectEndDate = selectEndDate;
                nextSelectDate = dayjs(timestamp);
            }
        } else {
            // 需要默认的 selectEndDate， 所以没有选择结束日期时，结束日期与开始日期是同一个值。
            if (!selectEndDate) {
                let endTimeValue: any = endValue || this.formatDefaultDay()[1];
                if (valueArray[1] && timeFormat) {
                    // 获取props中value的结束时间，将选择的时间的时分秒替换为结束时间的时分秒，解决在反显时同一个pick中结束时间的时分秒与开始时分秒一致问题
                    let dateFormatterParamsValue = valueArray[1];
                    if (valueOfType === 'string' && use12Hours) {
                        dateFormatterParamsValue = change12HoursTime(valueArray[1] as string).value;
                    }
                    endTimeValue = dateFormatter(dateFormatterParamsValue, valueOfType);
                }
                nextSelectEndDate = dayjs(timestamp)
                    .set('hour', endTimeValue.hour())
                    .set('minute', endTimeValue.minute())
                    .set('second', endTimeValue.second());
                nextSelectDate = selectDate;
                endDate = nextSelectEndDate;
            } else {
                nextSelectDate = dayjs(timestamp);
            }
        }

        // 开始和结束时间比较，对调。
        if (nextSelectDate && nextSelectEndDate && nextSelectDate.valueOf() > nextSelectEndDate.valueOf()) {
            [nextSelectDate, nextSelectEndDate] = [nextSelectEndDate, nextSelectDate];
        }
        // 获取当前的时分秒
        const currentSelectDate = selectDate
            ? nextSelectDate
                  .set('hour', selectDate.hour())
                  .set('minute', selectDate.minute())
                  .set('second', selectDate.second())
            : nextSelectDate;
        const currentSelectEndDate =
            (nextSelectEndDate &&
                nextSelectEndDate
                    .set('hour', endDate.hour())
                    .set('minute', endDate.minute())
                    .set('second', endDate.second())) ||
            null;

        const sd = getTimeValue(valueOfType, currentSelectDate, format);
        const ed = getTimeValue(valueOfType, currentSelectEndDate, format);
        this.setState(
            {
                startDate: nextSelectDate,
                selectDate: currentSelectDate,
                selectEndDate: currentSelectEndDate,
                ...initEditableOptions,
            },
            () => {
                const { state } = this;
                if (state.selectEndDate) {
                    this.handleOpenAfterSelectDate();
                }
                if (ed) {
                    const dataString = this.getStringValue(currentSelectDate, currentSelectEndDate);
                    if (!this.isControlled(this.props.value)) {
                        this.setState({
                            startValue: currentSelectDate,
                            endValue: currentSelectEndDate,
                        });
                    }
                    this.handlePropsChange([sd, ed], dataString);
                }
            },
        );

        if (onCalendarChange) {
            onCalendarChange([sd, ed], dayjs(timestamp));
        }
    };

    handleEndDateSelect = (timestamp: number) => {
        const { selectEndDate, selectDate, endValue, isDisabledOneSide, disabledIndex, format } = this.state;
        const { valueOfType, value, defaultValue, onCalendarChange, use12Hours } = this.props;
        let nextSelectDate = dayjs();
        let nextSelectEndDate = null;
        // eslint-disable-next-line no-nested-ternary
        const valueArray = Array.isArray(value) ? value : Array.isArray(defaultValue) ? defaultValue : ['', ''];
        const { timeFormat } = getFormat(format);
        //判断是否是数组形式的disable,如果有,将所选值添加到非disable的地方
        if (isDisabledOneSide) {
            if (disabledIndex === 0) {
                nextSelectEndDate = dayjs(timestamp);
                nextSelectDate = selectDate;
            } else {
                nextSelectEndDate = selectEndDate;
                nextSelectDate = dayjs(timestamp);
            }
        } else {
            if (!selectEndDate) {
                let endTimeValue: any = endValue || this.formatDefaultDay()[1];
                if (valueArray[1] && timeFormat) {
                    let dateFormatterParamsValue = valueArray[1];
                    if (valueOfType === 'string' && use12Hours) {
                        dateFormatterParamsValue = change12HoursTime(valueArray[1] as string).value;
                    }
                    // 获取props中value的结束时间，将选择的时间的时分秒替换为结束时间的时分秒，解决在反显时同一个pick中结束时间的时分秒与开始时分秒一致问题
                    endTimeValue = dateFormatter(dateFormatterParamsValue, valueOfType);
                }
                nextSelectEndDate = dayjs(timestamp)
                    .set('hour', endTimeValue.hour())
                    .set('minute', endTimeValue.minute())
                    .set('second', endTimeValue.second());
                nextSelectDate = selectDate;
            } else {
                nextSelectDate = dayjs(timestamp);
            }
        }
        if (nextSelectDate && nextSelectEndDate && nextSelectDate.valueOf() > nextSelectEndDate.valueOf()) {
            [nextSelectDate, nextSelectEndDate] = [nextSelectEndDate, nextSelectDate];
        }

        const currentSelectDate =
            nextSelectEndDate &&
            nextSelectEndDate
                .set('hour', nextSelectEndDate.hour())
                .set('minute', nextSelectEndDate.minute())
                .set('second', nextSelectEndDate.second());
        const sd = getTimeValue(valueOfType, nextSelectDate, format);
        const ed = getTimeValue(valueOfType, currentSelectDate, format);

        this.setState(
            {
                startDate: nextSelectDate,
                selectDate: nextSelectDate
                    .set('hour', selectDate.hour())
                    .set('minute', selectDate.minute())
                    .set('second', selectDate.second()),
                endDate: nextSelectDate.set('month', nextSelectDate.month() + 1),
                selectEndDate: currentSelectDate,
                ...initEditableOptions,
            },
            () => {
                const { state } = this;
                if (state.selectEndDate) {
                    this.handleOpenAfterSelectDate();
                }
                if (ed) {
                    const dataString = this.getStringValue(nextSelectDate, currentSelectDate);
                    if (!this.isControlled(this.props.value)) {
                        this.setState({
                            startValue: nextSelectDate
                                .set('hour', selectDate.hour())
                                .set('minute', selectDate.minute())
                                .set('second', selectDate.second()),
                            endValue: currentSelectDate,
                        });
                    }
                    this.handlePropsChange([sd, ed], dataString);
                }
            },
        );
        if (onCalendarChange) {
            onCalendarChange([sd, ed], dayjs(timestamp));
        }
    };

    selectMonthOrYear = (params: { timestamp: number; panelType: string; e?: React.SyntheticEvent }) => {
        const { timestamp, panelType: type } = params;
        const tl = togglePanel(PANEL_LIST, `${type}Show`);
        this.setState({
            startDate: dayjs(timestamp),
            endDate: dayjs(timestamp).add(1, 'month'),
            ...tl,
        });

        if (this.props.onCalendarChange) {
            const { valueOfType } = this.props;
            const { format } = this.state;
            const sd = getTimeValue(valueOfType, this.state.selectDate, format);
            const ed = getTimeValue(valueOfType, this.state.selectEndDate, format);
            this.props.onCalendarChange([sd, ed], dayjs(timestamp));
        }
    };

    selectEndMonthOrYear = (params: { timestamp: number; panelType: string; e?: React.SyntheticEvent }) => {
        const { timestamp, panelType } = params;
        const tl = togglePanel(END_PANEL_LIST, `end${panelType}Show`);
        this.setState({
            endDate: dayjs(timestamp),
            startDate: dayjs(timestamp).subtract(1, 'month'),
            ...tl,
        });
    };

    getDifferentTypeValue = (value: any) => {
        const { valueOfType, use12Hours } = this.props;
        let newValue = value;
        if (valueOfType === 'second') {
            newValue = value * 1000;
        }
        if (valueOfType === 'string' && use12Hours) {
            newValue = change12HoursTime(value).value;
        }
        return newValue;
    };

    adjustTimeFor12HourFormat({ date, use12HoursType, type, stateUse12HoursType }: any) {
        const { use12Hours } = this.props;
        const { timeSelectActiveIndex } = this.state;
        if (use12Hours) {
            const tempDataHour = date.hour();
            const isAM = use12HoursType === 'AM';
            const isPM = use12HoursType === 'PM';
            const isStateAM = stateUse12HoursType[timeSelectActiveIndex] === 'AM';
            const isStatePM = stateUse12HoursType[timeSelectActiveIndex] === 'PM';
            if (type === '12Hours') {
                if (isAM && tempDataHour >= 12) {
                    date = date.subtract(12, 'hour');
                } else if (isPM && tempDataHour < 12) {
                    date = date.add(12, 'hour');
                }
            } else {
                if (isStateAM && tempDataHour >= 12) {
                    date = date.subtract(12, 'hour');
                } else if (isStatePM && tempDataHour < 12) {
                    date = date.add(12, 'hour');
                }
            }
        }
        return date;
    }

    selectTime = (time: number, type: dayjs.UnitType & '12Hours', _: any, use12HoursType: 'AM' | 'PM') => {
        const {
            selectDate: stateSelectDate,
            selectEndDate,
            use12HoursType: stateUse12HoursType,
            timeSelectActiveIndex,
            format,
            inputValid,
            focusIndex,
            inputValueTexts,
        } = this.state;
        const { valueOfType, editable } = this.props;
        const selectDate = this.adjustTimeFor12HourFormat({
            date: stateSelectDate.set(type, time),
            use12HoursType,
            type,
            stateUse12HoursType,
        });
        const sd = getTimeValue(valueOfType, selectDate, format);
        // selectEndDate为空时，默认结束时间为selectDate
        const ed = getTimeValue(valueOfType, selectEndDate || selectDate, format);
        const dataString = this.getStringValue(selectDate, selectEndDate || selectDate);
        const state: any = {
            selectDate,
        };
        if (use12HoursType) {
            const newUse12HoursType = [...stateUse12HoursType];
            newUse12HoursType[timeSelectActiveIndex] = use12HoursType;
            state.use12HoursType = newUse12HoursType;
        }
        //如果是editable,将值填入inputValid为ture的地方
        if (editable && inputValid[focusIndex]) {
            const newInputValueTexts = [...inputValueTexts];
            const currentTime = focusIndex === RangeIndex.START_INDEX ? selectDate : selectEndDate;
            newInputValueTexts[focusIndex] = isFunction(currentTime.format) ? currentTime.format(format) : '';
            state.inputValueTexts = newInputValueTexts;
        }
        this.setState(state, () => {
            if (this.isControlled(this.props.value)) {
                this.handlePropsChange([sd, ed], dataString);
            } else {
                this.setState(
                    {
                        startValue: dayjs(this.getDifferentTypeValue(sd)),
                        endValue: dayjs(this.getDifferentTypeValue(ed)),
                    },
                    () => {
                        this.handlePropsChange([sd, ed], dataString);
                    },
                );
            }
        });
    };

    selectEndTime = (time: number, type: dayjs.UnitType & '12Hours', _: any, use12HoursType: 'AM' | 'PM') => {
        const {
            selectEndDate: stateSelectEndDate,
            use12HoursType: stateUse12HoursType,
            selectDate,
            timeSelectActiveIndex,
            format,
            focusIndex,
            inputValid,
            inputValueTexts,
        } = this.state;
        const { valueOfType, editable } = this.props;
        const selectEndDate = this.adjustTimeFor12HourFormat({
            date: (stateSelectEndDate || selectDate).set(type, time), // 当用户先选时间后选日期时，stateSelectEndDate为空，兜底逻辑
            use12HoursType,
            type,
            stateUse12HoursType,
        });
        const sd = getTimeValue(valueOfType, selectDate, format);
        const ed = getTimeValue(valueOfType, selectEndDate, format);
        const dataString = this.getStringValue(selectDate, selectEndDate);
        const state: any = {
            selectEndDate,
        };
        if (use12HoursType) {
            const newUse12HoursType = [...stateUse12HoursType];
            newUse12HoursType[timeSelectActiveIndex] = use12HoursType;
            state.use12HoursType = newUse12HoursType;
        }
        //如果是editable,将值填入inputValid为true的地方
        if (editable && inputValid[focusIndex]) {
            const newInputValueTexts = [...inputValueTexts];
            const currentTime = focusIndex === RangeIndex.START_INDEX ? selectDate : selectEndDate;
            newInputValueTexts[focusIndex] = isFunction(currentTime.format) ? currentTime.format(format) : '';
            state.inputValueTexts = newInputValueTexts;
        }
        this.setState(state, () => {
            if (this.isControlled(this.props.value)) {
                this.handlePropsChange([sd, ed], dataString);
            } else {
                this.setState(
                    {
                        startValue: dayjs(this.getDifferentTypeValue(sd)),
                        endValue: dayjs(this.getDifferentTypeValue(ed)),
                    },
                    () => {
                        this.handlePropsChange([sd, ed], dataString);
                    },
                );
            }
        });
    };

    handleChangeDate = (type: string, unitType: dayjs.UnitType, count?: number) => {
        const { startDate, endDate } = this.state;
        count = count || 1;

        const newStartDate: dayjs.Dayjs =
            type === 'add' ? startDate.add(count, unitType) : startDate.subtract(count, unitType);
        const newEndDate: dayjs.Dayjs =
            type === 'add' ? endDate.add(count, unitType) : endDate.subtract(count, unitType);

        this.setState({
            startDate: newStartDate,
            endDate: newEndDate,
        });
    };

    handleChangeEndDate = (type: string, unitType: dayjs.UnitType, count?: number) => {
        const { startDate, endDate } = this.state;
        count = count || 1;

        const newStartDate: dayjs.Dayjs =
            type === 'add' ? startDate.add(count, unitType) : startDate.subtract(count, unitType);
        const newEndDate: dayjs.Dayjs =
            type === 'add' ? endDate.add(count, unitType) : endDate.subtract(count, unitType);

        this.setState({
            // 更新组件状态
            startDate: newStartDate,
            endDate: newEndDate,
        });
    };

    showPanel = (type: string) => {
        const tl = togglePanel(PANEL_LIST, `${type}Show`);
        this.setState({
            ...tl,
        });
    };

    showEndPanel = (type: string) => {
        const tl = togglePanel(END_PANEL_LIST, `end${type}Show`);
        this.setState({
            ...tl,
        });
    };

    clearTime = (evt: Event, leftInputDisabled: any, rightInputDisabled: any) => {
        evt.stopPropagation();
        //需要判断disabld为array的情况
        const { valueOfType } = this.props;
        const { selectDate, selectEndDate, startDate, endDate, endValue, startValue, format } = this.state;

        let dataString: any = [];
        let newValue: any = [];
        const sd = getTimeValue(valueOfType, selectDate, format);
        const ed = getTimeValue(valueOfType, selectEndDate, format);
        if (leftInputDisabled) {
            dataString = [this.getStringValue(selectDate)[0] ?? '', null];
            newValue = [sd, null];
        }
        if (rightInputDisabled) {
            dataString = [null, this.getStringValue(undefined, selectEndDate)[1] ?? ''];
            newValue = [null, ed];
        }
        const defaultDate = this.formatDefaultDay();
        const firstDate = defaultDate[0];
        const secondDate = defaultDate[1];

        let states: any = {
            startValue: '',
            endValue: '',
            selectDate: firstDate,
            selectEndDate: secondDate,
            hoverDate: '',
            startDate: firstDate,
            endDate: secondDate.add(1, 'month'),
        };
        if (leftInputDisabled) {
            states = {
                ...states,
                selectDate,
                startDate,
                startValue,
            };
        }
        if (rightInputDisabled) {
            states = {
                ...states,
                selectEndDate,
                endDate,
                endValue,
            };
        }

        this.setState(states, () => {
            this.handlePropsChange(newValue, dataString);
        });
    };

    inputFocus = (e: React.FocusEvent<HTMLInputElement, Element>, focusIndex: number) => {
        const { onFocus, editable, type, onOpenChange } = this.props;
        const { startValue, endValue, inputStatus, format } = this.state;
        if (isFunction(onFocus)) {
            onFocus(e);
        }
        if (isFunction(onOpenChange)) {
            onOpenChange(true);
        }

        this.setState(
            {
                closeShow: true,
                focusIndex,
            },
            () => {
                //可输入时输入框有已选日期且没有被输入过时手动比较当前输入框时间的合法性
                const hasInput = inputStatus.some((item: boolean) => item);
                if (editable && !hasInput) {
                    const currentIndexValue = focusIndex === RangeIndex.START_INDEX ? startValue : endValue;
                    if (currentIndexValue) {
                        const currentFormatValue = isFunction(currentIndexValue.format)
                            ? currentIndexValue.format(format)
                            : '';
                        const inputValue = type === 'week' ? this.getYearWeek(currentIndexValue) : currentFormatValue;
                        this.onInputChange(inputValue);
                    }
                }
            },
        );
    };

    inputBlur = (e: React.FocusEvent<HTMLInputElement, Element>) => {
        const { onBlur } = this.props;
        if (isFunction(onBlur)) {
            onBlur(e);
        }
    };

    isCloseShow = () => {
        this.setState({
            closeShow: false,
        });
    };

    handleHoverDate = (timestamp: number, evt: React.SyntheticEvent) => {
        const { selectDate, selectEndDate, isDisabledOneSide } = this.state;
        if (selectDate && selectEndDate && !isDisabledOneSide) {
            return;
        }
        evt.stopPropagation();
        this.setState({
            hoverDate: dayjs(timestamp),
        });
    };

    triggerPreset = (timeRange: number[]) => {
        const { valueOfType } = this.props;
        const { format } = this.state;
        let nextSelectDate = dayjs(timeRange[0] || '');
        let nextSelectEndDate = dayjs(timeRange[1] || '');

        //  开始和结束时间比较，对调。
        if (nextSelectDate && nextSelectEndDate && nextSelectDate.valueOf() > nextSelectEndDate.valueOf()) {
            [nextSelectDate, nextSelectEndDate] = [nextSelectEndDate, nextSelectDate];
        }

        this.setState({
            startDate: nextSelectDate,
            selectDate: nextSelectDate,
            endDate: nextSelectDate.add(1, 'month'),
            selectEndDate: nextSelectEndDate,
        });

        const sd = getTimeValue(valueOfType, nextSelectDate, format);
        const ed = getTimeValue(valueOfType, nextSelectEndDate, format);
        const dataString = this.getStringValue(nextSelectDate, nextSelectEndDate);

        if (this.isControlled(this.props.value)) {
            this.handlePropsChange([sd, ed], dataString);
        } else {
            this.setState(
                {
                    startValue: nextSelectDate,
                    endValue:
                        (nextSelectEndDate &&
                            nextSelectEndDate
                                .set('hour', nextSelectEndDate.hour())
                                .set('minute', nextSelectEndDate.minute())
                                .set('second', nextSelectEndDate.second())) ||
                        '',
                },
                () => {
                    this.handlePropsChange([sd, ed], dataString);
                },
            );
        }
    };

    renderPreset = () => {
        const { ranges = {}, presets } = this.props;
        let presetArr: any[] = [];
        // ranges后续废弃，统一使用presets
        const presetsDate = Object.keys(ranges)?.length ? ranges : presets;
        if (isObject(presetsDate)) {
            presetArr = Object.keys(presetsDate).map((key: string) => {
                const timeRange = (presetsDate as any)[key];
                if (isArray(timeRange)) {
                    return (
                        <Button
                            key={key}
                            className={`${this.context.prefixCls}-datepicker-preset-btn`}
                            size="mini"
                            onClick={() => {
                                this.triggerPreset(timeRange);
                            }}
                        >
                            {key}
                        </Button>
                    );
                }
                return null;
            });
        }

        return presetArr;
    };

    getExtraFooter(mode: PanelMode, renderExtraFooter?: (mode: PanelMode) => React.ReactNode) {
        if (!renderExtraFooter) {
            return null;
        }

        return <div className={`${this.context.prefixCls}-datepicker-footer-extra`}>{renderExtraFooter(mode)}</div>;
    }

    handleRooPickerCellRangeHover = (pointerDate: any) => {
        const { isDisabledOneSide, selectDate, selectEndDate } = this.state;
        if (
            isDisabledOneSide &&
            [selectDate, selectEndDate].every((item: any) => !!item) &&
            pointerDate.isAfter(selectDate, 'day') &&
            pointerDate.isBefore(selectEndDate, 'day')
        ) {
            return true;
        }
        return false;
    };

    getPlacement = () => {
        const { placement } = this.props;
        if (placement) {
            return placement;
        } else {
            return this.context.direction === 'RTL' ? 'bottomRight' : 'bottomLeft';
        }
    };

    getPopperPlacement = () => {
        const { placement } = this.props;
        if (placement) {
            return placementEnum[placement as placementTypes];
        } else {
            return this.context.direction === 'RTL' ? placementEnum.bottomRight : placementEnum.bottomLeft;
        }
    };

    parseWeekString(weekString: string): dayjs.Dayjs | null {
        const localWeek = locale.lng('DatePicker.week');
        const match = new RegExp(`^(\\d{4})-(\\d{1,2})\\s?${localWeek}$`).exec(weekString);
        if (match) {
            const year = parseInt(match[1], 10);
            const week = parseInt(match[2], 10);
            // 使用 ISO 周标准
            // console.log(dayjs().year(year), year, week, dayjs().year(year).set('date', 1).set('month', 0));
            // 从指定年份的1月4日开始计算（ISO 8601标准规定每年第一周包含1月4日）
            const jan4th = dayjs().year(year).month(0).date(4);
            // 获取包含1月4日的那一周的第一天
            const firstWeek = jan4th.startOf('isoWeek');
            // 计算目标周的第一天
            return firstWeek.add(week - 1, 'weeks');
            // return dayjs().year(year).startOf('year').isoWeek(week).startOf('isoWeek');
        }
        return null;
    }

    onInputChange = (value: string) => {
        const { showTime, timeSteps, disabledDate, disabledTime, position, type } = this.props;
        const {
            format,
            inputStatus,
            inputValid,
            focusIndex,
            inputValueTexts,
            startDate,
            selectDate,
            selectEndDate,
            endDate,
            isDisabledOneSide,
            disabledIndex,
        } = this.state;

        const newInputValueTexts = [...inputValueTexts];
        const currentInputStatus = inputStatus[focusIndex];
        const currentInputValid = inputValid[focusIndex];
        newInputValueTexts[focusIndex] = value;
        const newState: any = {
            inputValueTexts: newInputValueTexts,
            inputStatus: [...inputStatus],
            inputValid: [...inputValid],
            //清空hoverDate,防止输入时active样式出错
            hoverDate: '',
        };
        //如果都没有进行输入,保存当前数据,清空用
        if (inputStatus.every((item: boolean) => !item)) {
            newState.temporaryTimeStore = {
                startDate,
                selectDate,
                selectEndDate,
                endDate,
            };
        }
        if (!currentInputStatus) {
            newState.inputStatus[focusIndex] = true;
        }
        if (currentInputValid) {
            newState.inputValid[focusIndex] = false;
        }
        this.setState(newState);

        let inputMatchedValue = dayjs(value, format, true);
        const newStates: any = { inputValid: [...newState.inputValid] };
        let isValid = inputMatchedValue.isValid();
        if (type === 'week') {
            const isValidWeek = this.parseWeekString(value);
            if (isValidWeek) {
                isValid = true;
                inputMatchedValue = isValidWeek;
            }
        }
        if (!isValid) return false;
        let isDisabled = false;
        const hour = inputMatchedValue.hour();
        const minute = inputMatchedValue.minute();
        const second = inputMatchedValue.second();
        if (disabledDate && isFunction(disabledDate)) {
            // 检查输入值是否在禁用时间范围内
            isDisabled = disabledDate(inputMatchedValue);
            if (isDisabled) return false;
        }
        if (showTime) {
            //如果showTime并且传入了timeSteps,需要判断步长
            if (timeSteps) {
                const { hourStep, minuteStep, secondStep } = timeSteps;
                if (hourStep && hour % hourStep !== 0) {
                    isDisabled = true;
                    return false;
                }
                if (minuteStep && minute % minuteStep !== 0) {
                    isDisabled = true;
                    return false;
                }
                if (secondStep && second % secondStep !== 0) {
                    isDisabled = true;
                    return false;
                }
            }
            if (disabledTime && isFunction(disabledTime)) {
                // 检查输入值是否在禁用时间范围内
                const isHourDisabled = disabledTime(hour, 'hour', position);
                const isMinuteDisabled = disabledTime(minute, 'minute', position);
                const isSecondDisabled = disabledTime(second, 'second', position);
                isDisabled = isHourDisabled || isMinuteDisabled || isSecondDisabled;
                if (isDisabled) return false;
            }
        }
        if (isDisabledOneSide) {
            const arrayDisabledDate = (date: any) => {
                if (disabledIndex === RangeIndex.START_INDEX) {
                    return date.isBefore(selectDate, 'day');
                } else if (disabledIndex === RangeIndex.END_INDEX) {
                    return date.isAfter(selectEndDate, 'day');
                }
                return false;
            };
            isDisabled = arrayDisabledDate(inputMatchedValue);
        }

        if (isValid && !isDisabled) {
            if (focusIndex === RangeIndex.START_INDEX) {
                newStates.startDate = inputMatchedValue;
                newStates.selectDate = inputMatchedValue;
                //保证页面上不会出现两个相同的日期展示
                newStates.endDate = inputMatchedValue.add(1, 'month');
            } else {
                newStates.endDate = inputMatchedValue;
                newStates.selectEndDate = inputMatchedValue;
                //保证页面上不会出现两个相同的日期展示
                newStates.startDate = inputMatchedValue.subtract(1, 'month');
            }
            newStates.inputValid[focusIndex] = true;
            this.setState(newStates);
        }
        return true;
    };

    handleEditableConfirm = () => {
        const { selectEndDate, selectDate, format, focusIndex } = this.state;
        const { valueOfType, onOpenChange, value } = this.props;
        let nextSelectDate = selectDate;
        let nextSelectEndDate = selectEndDate;
        if (nextSelectDate && nextSelectEndDate && nextSelectDate.valueOf() > nextSelectEndDate.valueOf()) {
            [nextSelectDate, nextSelectEndDate] = [nextSelectEndDate, nextSelectDate];
        }

        const currentSelectDate =
            nextSelectEndDate &&
            nextSelectEndDate
                .set('hour', nextSelectEndDate.hour())
                .set('minute', nextSelectEndDate.minute())
                .set('second', nextSelectEndDate.second());
        const sd = getTimeValue(valueOfType, nextSelectDate, format);
        const ed = getTimeValue(valueOfType, currentSelectDate, format);

        if (ed) {
            this.setState({
                showPopover: false,
            });
            this.isCloseShow();
            if (isFunction(onOpenChange)) {
                onOpenChange(false);
            }
            const dataString = this.getStringValue(nextSelectDate, currentSelectDate);
            if (!this.isControlled(value)) {
                this.setState({
                    startValue: nextSelectDate
                        .set('hour', selectDate.hour())
                        .set('minute', selectDate.minute())
                        .set('second', selectDate.second()),
                    endValue: currentSelectDate,
                });
            }
            this.handlePropsChange([sd, ed], dataString);
            //清空输入状态
            this.setState({
                ...initEditableOptions,
            });

            //处理失焦
            if (focusIndex === RangeIndex.START_INDEX && this.startInputRef?.current) {
                this.startInputRef.current.blur();
            }
            if (focusIndex === RangeIndex.END_INDEX && this.endInputRef?.current) {
                this.endInputRef.current.blur();
            }
        }
    };

    getInputKeyDownJumpValue = (targetIndex: number) => {
        const { type } = this.props;
        const { inputStatus, startValue, endValue, inputValueTexts, format } = this.state;
        const targetValue = targetIndex === RangeIndex.START_INDEX ? startValue : endValue;
        const targetInputValue = inputValueTexts[targetIndex];
        const targetInputStatus = inputStatus[targetIndex];
        let jumpValue = targetValue ? targetValue.format(format) : '';
        if (type === 'week' && targetValue) {
            jumpValue = this.getYearWeek(targetValue);
        }
        if (targetInputStatus) {
            jumpValue = targetInputValue;
        }
        return jumpValue;
    };

    onInputKeyDown = (e: React.KeyboardEvent, manualCall?: boolean) => {
        const { editable } = this.props;
        if (!(editable && e.key === 'Enter') && !manualCall) {
            return;
        }
        const { inputValid, focusIndex, isDisabledOneSide } = this.state;
        const inValidIndex = inputValid.findIndex((item: boolean) => !item);
        //判断是否都通过校验或isDisabledOneSide，符合直接触发onChange
        if (inValidIndex === -1 || (isDisabledOneSide && inputValid[focusIndex])) {
            this.handleEditableConfirm();
            return;
        }
        //全部都不符合时不切换焦点
        const isAllInValid = inputValid.every((item: boolean) => !item);
        if (isAllInValid) {
            return;
        }
        let newInputFocus = focusIndex;
        let inputValue = '';
        //聚焦状态切换到没通过校验的input,且手动调用onInputChange
        if (inValidIndex === RangeIndex.START_INDEX && this.startInputRef?.current) {
            this.startInputRef.current.focus();
            newInputFocus = 0;
            inputValue = this.getInputKeyDownJumpValue(0);
        }
        if (inValidIndex === RangeIndex.END_INDEX && this.endInputRef?.current) {
            this.endInputRef.current.focus();
            newInputFocus = 1;
            inputValue = this.getInputKeyDownJumpValue(1);
        }
        //不为手动触发时再进行set
        if (!manualCall) {
            this.setState(
                {
                    focusIndex: newInputFocus,
                },
                () => {
                    if (inputValue) {
                        this.onInputChange(inputValue);
                    }
                },
            );
        }
    };

    handleClickOutside = (e: any) => {
        // 点击的是输入框区域，则不做任何处理
        if (this.inputNode?.contains(e?.target)) {
            return
        }

        const { inputStatus, temporaryTimeStore } = this.state;
        const { onOpenChange } = this.props;
        let newState = { showPopover: false };
        //是输入状态的将状态清空,日期设置为输入之前日期
        if (inputStatus.some((item: boolean) => Boolean(item))) {
            newState = {
                ...newState,
                ...temporaryTimeStore,
                ...initEditableOptions,
            };
        }
        if (isFunction(onOpenChange)) {
            onOpenChange(false);
        }
        this.isCloseShow();
        this.setState(newState);
    };

    handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { editable } = this.props;
        const { showPopover } = this.state;
        if (editable) {
            if (!showPopover) {
                this.setState({
                    showPopover: true,
                });
            }
            this.onInputChange(e.target.value);
        }
    };

    render() {
        const {
            startDate,
            selectDate,
            endDate,
            selectEndDate,
            dateShow,
            monthShow,
            yearShow,
            enddateShow,
            endmonthShow,
            endyearShow,
            showPopover,
            hoverDate,
            closeShow,
            format,
            inputValueTexts,
            inputStatus,
        } = this.state;

        const {
            showTime,
            placeholder,
            disabledDate,
            disabled,
            popupContainer,
            className,
            clearable,
            dateCellRender,
            inputClassName,
            size = this.context.size || 'normal',
            flip,
            autoFocus,
            style,
            popupStyle,
            disabledTime,
            renderExtraFooter,
            panelRender,
            prevIcon,
            nextIcon,
            superPrevIcon,
            superNextIcon,
            separator,
            bordered,
            status,
            timeSteps,
            popupClassName,
            inputReadOnly,
            type,
            use12Hours,
            valueRender,
            suffixIcon,
            isCloseMotion,
            zIndex,
            disablePortal,
            variant,
        } = this.props;

        const newType = `${type}Range`;

        const extraFooter = this.getExtraFooter('range', renderExtraFooter);
        const { dateFormat, timeFormat } = getFormat(format);
        const isShowTimeObject = isObject(timeSteps);
        let timeSelectProps = {};
        if (isShowTimeObject) {
            timeSelectProps = timeSteps;
        }
        const isArrayDisabled = isArray(disabled);
        let leftInputDisabled = disabled;
        let rightInputDisabled = disabled;
        let popupVisibleDisabled = disabled;
        let allDisabled = false;
        let arrayDisabledDate;
        //在这里判断disabled是否为数组形式,且通过arrayDisabledDate禁用选项,不在DatePanel中判断是因为异步问题导致props不更新,错过渲染
        if (isArrayDisabled) {
            allDisabled = disabled.every(item => !!item);
            leftInputDisabled = disabled[0] ? disabled[0] : false;
            rightInputDisabled = disabled[1] ? disabled[1] : false;
            popupVisibleDisabled = disabled.every(item => !!item);
            const disabledIndex = (disabled as boolean[]).findIndex(item => !!item);
            if (!allDisabled && disabledIndex !== null) {
                arrayDisabledDate = (date: any) => {
                    if (disabledIndex === 0) {
                        return date.isBefore(selectDate, 'day');
                    } else if (disabledIndex === 1) {
                        return date.isAfter(selectEndDate, 'day');
                    }
                    return false;
                };
            }
        }

        const getValue = (dayjsDate: dayjs.Dayjs, currentFormat: string | undefined, defaultFormat: string) =>
            dayjsDate ? dayjsDate.format(currentFormat || defaultFormat) : undefined;
        const firstSelectValue = getValue(selectDate, dateFormat, 'YYYY-MM-DD');
        const firstSelectTimeValue = getValue(selectDate, timeFormat, 'HH:mm:ss');

        const secondSelectValue = getValue(selectEndDate, dateFormat, 'YYYY-MM-DD');
        const secondSelectTimeValue = getValue(selectEndDate, timeFormat, 'HH:mm:ss');

        let panelBody: React.ReactNode = (
            <>
                <div
                    className={`${this.context.prefixCls}-datepicker-data-panel datepicker-days`}
                    key={`start${selectDate ? selectDate.format('YYYYMMDD') : 0}${
                        selectEndDate ? selectEndDate.format('YYYYMMDD') : 0
                    }${hoverDate && hoverDate.millisecond()}`}
                >
                    {showTime && type !== 'week' ? (
                        <div className={`${this.context.prefixCls}-datepicker-inputs`}>
                            <Input
                                // key={`${startDate.date()}${startDate.month()}${startDate.year()}`}
                                type="text"
                                className={`${this.context.prefixCls}-input ${this.context.prefixCls}-input-inline ${this.context.prefixCls}-input-xs`}
                                value={firstSelectValue}
                                disabled={leftInputDisabled as boolean}
                                readOnly
                            />
                            <Trigger
                                builtinPlacements={getPlacements(flip)}
                                popupPlacement="bottomLeft"
                                action={['click']}
                                popupStyle={{ whiteSpace: 'nowrap' }}
                                getPopupContainer={() => this.rootNode.current}
                                popup={
                                    <TimePicker
                                        disabledTime={disabledTime}
                                        value={selectDate}
                                        format={timeFormat || 'HH:mm:ss'}
                                        defaultValue={selectDate}
                                        onChange={this.selectTime}
                                        position="pre"
                                        use12Hours={use12Hours}
                                        halfDayHelperTime={selectDate}
                                        showTime={showTime}
                                        {...timeSelectProps}
                                    />
                                }
                            >
                                <Input
                                    // key={`${selectDate?.format('HH:mm:ss')}`}
                                    type="text"
                                    className={`${this.context.prefixCls}-input ${this.context.prefixCls}-input-inline ${this.context.prefixCls}-input-xs`}
                                    value={firstSelectTimeValue}
                                    disabled={leftInputDisabled as boolean}
                                    onFocus={() => {
                                        this.setState({
                                            timeSelectActiveIndex: RangeIndex.START_INDEX,
                                        });
                                    }}
                                />
                            </Trigger>
                        </div>
                    ) : null}
                    <YearPanel
                        show={yearShow}
                        startDate={startDate}
                        selectDate={selectDate}
                        onSelectYear={this.selectMonthOrYear}
                        handleChangeDate={this.handleChangeDate}
                        disabledDate={disabledDate}
                        dateCellRender={dateCellRender}
                        prevIcon={prevIcon}
                        nextIcon={nextIcon}
                    />
                    <MonthPanel
                        show={monthShow}
                        startDate={startDate}
                        selectDate={selectDate}
                        onShowPanel={this.showPanel}
                        onSelectMonth={this.selectMonthOrYear}
                        handleChangeDate={this.handleChangeDate}
                        disabledDate={disabledDate}
                        dateCellRender={dateCellRender}
                        prevIcon={prevIcon}
                        nextIcon={nextIcon}
                    />
                    <DatePanel
                        type={newType}
                        show={dateShow}
                        startDate={startDate}
                        hoverDate={hoverDate}
                        selectDate={[selectDate, selectEndDate]}
                        onShowPanel={this.showPanel}
                        onSelectDate={this.handleDateSelect}
                        handleChangeDate={this.handleChangeDate}
                        onHover={this.handleHoverDate}
                        disabledDate={disabledDate}
                        dateCellRender={dateCellRender}
                        prevIcon={prevIcon}
                        nextIcon={nextIcon}
                        superPrevIcon={superPrevIcon}
                        superNextIcon={superNextIcon}
                        arrayDisabledDate={arrayDisabledDate}
                    />
                </div>
                <div
                    className={`${this.context.prefixCls}-datepicker-data-panel datepicker-days`}
                    key={`end${selectDate ? selectDate.format('YYYYMMDD') : 0}${
                        selectEndDate ? selectEndDate.format('YYYYMMDD') : 0
                    }${hoverDate && hoverDate.millisecond()}`}
                >
                    {showTime && type !== 'week' ? (
                        <div className={`${this.context.prefixCls}-datepicker-inputs`}>
                            <Input
                                // key={`${startDate.date()}${startDate.month()}${startDate.year()}`}
                                type="text"
                                className={`${this.context.prefixCls}-input ${this.context.prefixCls}-input-inline ${this.context.prefixCls}-input-xs`}
                                value={secondSelectValue}
                                disabled={rightInputDisabled as boolean}
                                readOnly
                            />
                            <Trigger
                                builtinPlacements={getPlacements(flip)}
                                popupPlacement="bottomLeft"
                                action={['click']}
                                popupStyle={{ whiteSpace: 'nowrap' }}
                                getPopupContainer={() => this.rootNode.current}
                                popup={
                                    <TimePicker
                                        disabledTime={disabledTime}
                                        value={selectEndDate || endDate}
                                        format={timeFormat || 'HH:mm:ss'}
                                        defaultValue={selectEndDate || endDate}
                                        onChange={this.selectEndTime}
                                        position="aft"
                                        use12Hours={use12Hours}
                                        halfDayHelperTime={selectEndDate || endDate}
                                        showTime={showTime}
                                        {...timeSelectProps}
                                    />
                                }
                            >
                                <Input
                                    // key={`${selectEndDate && selectEndDate.format('HH:mm:ss')}` || ''}
                                    type="text"
                                    className={`${this.context.prefixCls}-input ${this.context.prefixCls}-input-inline ${this.context.prefixCls}-input-xs`}
                                    value={secondSelectTimeValue}
                                    disabled={rightInputDisabled as boolean}
                                    onFocus={() => {
                                        this.setState({
                                            timeSelectActiveIndex: RangeIndex.END_INDEX,
                                        });
                                    }}
                                />
                            </Trigger>
                        </div>
                    ) : null}
                    <YearPanel
                        show={endyearShow}
                        startDate={endDate}
                        selectDate={selectEndDate || selectDate}
                        onSelectYear={this.selectEndMonthOrYear}
                        handleChangeDate={this.handleChangeEndDate}
                        disabledDate={disabledDate}
                        dateCellRender={dateCellRender}
                        prevIcon={prevIcon}
                        nextIcon={nextIcon}
                    />
                    <MonthPanel
                        show={endmonthShow}
                        startDate={endDate}
                        selectDate={selectEndDate || selectDate}
                        onShowPanel={this.showEndPanel}
                        onSelectMonth={this.selectEndMonthOrYear}
                        handleChangeDate={this.handleChangeEndDate}
                        disabledDate={disabledDate}
                        dateCellRender={dateCellRender}
                        prevIcon={prevIcon}
                        nextIcon={nextIcon}
                    />
                    <DatePanel
                        type={newType}
                        show={enddateShow}
                        startDate={endDate}
                        hoverDate={hoverDate}
                        selectDate={[selectDate, selectEndDate]}
                        onShowPanel={this.showEndPanel}
                        onSelectDate={this.handleEndDateSelect}
                        handleChangeDate={this.handleChangeEndDate}
                        onHover={this.handleHoverDate}
                        disabledDate={disabledDate}
                        dateCellRender={dateCellRender}
                        prevIcon={prevIcon}
                        nextIcon={nextIcon}
                        superPrevIcon={superPrevIcon}
                        superNextIcon={superNextIcon}
                        arrayDisabledDate={arrayDisabledDate}
                    />
                </div>
                <div className="footer-wrapper">
                    {extraFooter ? (
                        <div className={`${this.context.prefixCls}-datepicker-footer`}>{extraFooter}</div>
                    ) : null}
                    {this.renderPreset()}
                </div>
            </>
        );

        if (isFunction(panelRender)) {
            panelBody = panelRender(panelBody);
        }

        const groupCls = classNames(`${this.context.prefixCls}-input-group`, {
            'has-icon': true,
            [`${this.context.prefixCls}-input-group-${size}`]: size && size !== 'normal',
            [`${this.context.prefixCls}-input-group-disabled`]: leftInputDisabled && rightInputDisabled,
            [`${this.context.prefixCls}-input-group-outline`]: bordered,
            [`${this.context.prefixCls}-input-group-no-outline`]: !bordered,
            [`${this.context.prefixCls}-input-group-outline-${status}`]: status,
            [`${this.context.prefixCls}-input-group-outline-${variant}`]: variant,
        });
        let [firstInputValue, secondInputValue] = this.getStringValue();
        if (isFunction(valueRender)) {
            const [valueOne, valueTwo] = valueRender([startDate, endDate]);
            firstInputValue = valueOne;
            secondInputValue = valueTwo;
        }
        if (inputStatus[0]) {
            firstInputValue = inputValueTexts[0];
        }
        if (inputStatus[1]) {
            secondInputValue = inputValueTexts[1];
        }

        return (
            <DayjsLocalProvider>
                <div>
                    <Manager>
                        <Reference>
                            {/* 使用回调的方式才能取到ref， 否则this.inputNode.current始终为null */}
                            {({ ref }: any) => (
                                <div
                                    className={groupCls}
                                    style={style}
                                    ref={node => {
                                        ref(node);
                                        this.inputNode = node;
                                    }}
                                    id="range"
                                    onClick={() => {
                                        if (!popupVisibleDisabled) {
                                            this.setState({
                                                showPopover: true,
                                            });
                                        }
                                    }}
                                >
                                    <Input
                                        inputRef={this.startInputRef}
                                        size={size}
                                        className={classNames(`${this.context.prefixCls}-input`, inputClassName)}
                                        disabled={leftInputDisabled as boolean}
                                        autoFocus={autoFocus}
                                        placeholder={
                                            (isArray(placeholder) && placeholder[RangeIndex.START_INDEX]) ||
                                            (locale.lng('DatePicker.selectStartPlaceholder') as string) ||
                                            ''
                                        }
                                        onFocus={e => this.inputFocus(e, RangeIndex.START_INDEX)}
                                        onBlur={this.inputBlur}
                                        value={firstInputValue}
                                        readOnly={inputReadOnly}
                                        onChange={this.handleInputChange}
                                        onKeyDown={this.onInputKeyDown}
                                        styleType={variant}
                                    />
                                    {separator || <span className="input-separator">-</span>}
                                    <Input
                                        inputRef={this.endInputRef}
                                        size={size}
                                        className={classNames(`${this.context.prefixCls}-input`, inputClassName)}
                                        onFocus={e => this.inputFocus(e, RangeIndex.END_INDEX)}
                                        onBlur={this.inputBlur}
                                        disabled={rightInputDisabled as boolean}
                                        placeholder={
                                            (isArray(placeholder) && placeholder[RangeIndex.END_INDEX]) ||
                                            (locale.lng('DatePicker.selectEndPlaceholder') as string) ||
                                            ''
                                        }
                                        value={secondInputValue}
                                        readOnly={inputReadOnly}
                                        onChange={this.handleInputChange}
                                        onKeyDown={this.onInputKeyDown}
                                        styleType={variant}
                                    />
                                    {clearable && (!leftInputDisabled || !rightInputDisabled) ? (
                                        <IconClear
                                            onClick={(e: any) => this.clearTime(e, leftInputDisabled, rightInputDisabled)}
                                            className={`addon-icon ${this.context.prefixCls}-date-time-picker-icon`}
                                            closeShow={closeShow}
                                            suffixIcon={suffixIcon}
                                        />
                                    ) : (
                                        <Icon
                                            className={`addon-icon ${this.context.prefixCls}-date-time-picker-icon`}
                                            name={suffixIcon || 'calendar-new'}
                                        />
                                    )}
                                </div>
                            )}
                        </Reference>
                        <PopperPortal
                            zIndex={zIndex}
                            disablePortal={disablePortal}
                            visible={showPopover}
                            modifiers={[
                                {
                                    name: 'flip',
                                    enabled: flip,
                                },
                            ]}
                            placement={this.getPopperPlacement()}
                            container={popupContainer}
                            onClickOutside={this.handleClickOutside}
                            motionName={
                                isCloseMotion
                                    ? `${this.context.prefixCls}-close-motion`
                                    : `${this.context.prefixCls}-popup-fast-motion`
                            }
                            isCloseMotion={isCloseMotion}
                            className={popupClassName}
                        >
                            <div
                                className={`${this.context.prefixCls}-datepicker ${this.context.prefixCls}-datepicker-ranger ${className}`}
                                style={popupStyle}
                                ref={this.rootNode}
                                onClick={e => {
                                    e.stopPropagation();
                                }}
                            >
                                {panelBody}
                            </div>
                        </PopperPortal>
                    </Manager>
                </div>
            </DayjsLocalProvider>
        );
    }
}

polyfill(RangePicker);

export default withDisabled<typeof RangePicker>(RangePicker);
