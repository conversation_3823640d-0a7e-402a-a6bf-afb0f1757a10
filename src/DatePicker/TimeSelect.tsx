import * as React from 'react';
import * as PropTypes from 'prop-types';
import Trigger from '@rc-component/trigger';
import dayjs from 'dayjs';
import { polyfill } from 'react-lifecycles-compat';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { createRef } from '@roo/create-react-ref';
import locale from '@roo/roo/locale';
import { isFunction, isArray, isBoolean, isUndefined, isString } from '@utiljs/is';
import classNames from 'classnames';
import cloneDeep from 'lodash/cloneDeep';
import { mouseEnter, mouseLeave, getValueOfTypeValue, timeTypesFormatter } from './utils';
import TimePicker from './TimePicker';
import { getPlacements } from './placements';
import Icon from '../Icon';
import Input from '../Input';
import { TimeSelectProps, DateValueType, dateValueTypes } from './interface';
import { Size, sizes, somePlacements } from '../_utils/types';
import { GlobalConfigContext } from '../ConfigProvider';
import { $gray400 } from '../_utils/ThemeColor';
import DayjsLocalProvider from './DayjsLocalProvider';
import withDisabled from '../_utils/hoc/withDisabled';

const initInputOptions = {
    isInputStatus: false,
    isInputValid: false,
    inputValueTexts: ['', ''],
};
dayjs.extend(customParseFormat);
interface TimeSelectState {
    value: string | string[];
    closeShow: boolean;
    open?: boolean;
    focusIndex: number;
    rangePlacement: string;
    inputValueTexts: string[];
    isInputStatus: boolean;
    isInputValid: boolean;
    position: 'pre' | 'aft';
}

interface ExtraProps {
    position?: 'pre' | 'aft';
}

class TimeSelect extends React.Component<TimeSelectProps & ExtraProps, TimeSelectState> {
    static contextType = GlobalConfigContext;
    context!: React.ContextType<typeof GlobalConfigContext>;

    static defaultProps = {
        placeholder: '',
        className: '',
        inputClassName: '',
        disabled: false,
        clearable: false,
        editable: true,
        format: 'HH:mm:ss',
        flip: true,
        bordered: true,
        open: undefined,
        // size: 'normal' as Size,
        placement: undefined,
        range: false,
        autoFocus: false,
        preset: {},
        hourStep: 1,
        minuteStep: 1,
        secondStep: 1,
        use12Hours: false,
        popupContainer: document.body || document.documentElement,
        valueOfType: 'string' as DateValueType,
        onBlur: () => {},
        onFocus: () => {},
        onChange: () => {},
        disabledTime: () => false,
        popupClassName: undefined,
        inputReadOnly: false,
    };

    static propTypes = {
        // size: PropTypes.oneOf(sizes),
        className: PropTypes.string,
        inputClassName: PropTypes.string,
        clearable: PropTypes.bool,
        placeholder: PropTypes.oneOfType([PropTypes.string, PropTypes.array]),
        format: PropTypes.string,
        disabled: PropTypes.bool,
        flip: PropTypes.oneOfType([PropTypes.bool]),
        placement: PropTypes.oneOf(somePlacements),
        range: PropTypes.bool,
        popupContainer: PropTypes.oneOfType([PropTypes.instanceOf(Element), PropTypes.func]),
        open: PropTypes.bool,
        autoFocus: PropTypes.bool,
        bordered: PropTypes.bool,
        editable: PropTypes.bool,
        use12Hours: PropTypes.bool,
        preset: PropTypes.object,
        hourStep: PropTypes.number,
        minuteStep: PropTypes.number,
        secondStep: PropTypes.number,
        onChange: PropTypes.func,
        onBlur: PropTypes.func,
        onFocus: PropTypes.func,
        disabledTime: PropTypes.func,
        valueOfType: PropTypes.oneOf(dateValueTypes),
        popupClassName: PropTypes.string,
        inputReadOnly: PropTypes.bool,
    };

    rootNode = createRef<HTMLElement>();

    firstInputRef = createRef<HTMLInputElement>();

    secondInputRef = createRef<HTMLInputElement>();

    inputNode: any = createRef();

    mouseEnter: () => void = mouseEnter.bind(this);

    mouseLeave: () => void = mouseLeave.bind(this);

    constructor(props: TimeSelectProps) {
        super(props);
        const { defaultValue, value, valueOfType, range, format, open } = props;
        //在入口处将其他类型时间转为string类型
        const newValue = getValueOfTypeValue({
            value: value || defaultValue,
            valueOfType: valueOfType!,
            format: format!,
            range: range!,
        });
        this.state = {
            value: newValue || (range && !value && !defaultValue ? ['', ''] : ''),
            closeShow: false,
            open,
            focusIndex: 0,
            rangePlacement: 'bottomLeft',
            inputValueTexts: ['', ''],
            isInputStatus: false, //是否为输入状态
            isInputValid: false,
            position: 'pre',
        };
    }

    static getDerivedStateFromProps(nextProps: TimeSelectProps) {
        const newState: any = {};
        const { open, value, valueOfType, range, format } = nextProps;
        if (!isUndefined(open)) {
            newState.open = open;
        }
        if (!isUndefined(value)) {
            newState.value = getValueOfTypeValue({
                value,
                valueOfType: valueOfType!,
                format: format!,
                range: range!,
            });
        }
        return newState;
    }

    clearTime = () => {
        const { range } = this.props;
        this.setState({
            value: range ? ['', ''] : '',
        });
        if (this.props.onChange) {
            this.props.onChange(range ? ['', ''] : '');
        }
    };

    inputFocusPre = () => {
        if (this.props.onFocus) {
            this.props.onFocus();
        }
        this.setState({
            focusIndex: 0,
            rangePlacement: this.context.direction === 'RTL' ? 'bottomRight' : 'bottomLeft',
            closeShow: true,
            open: false,
            position: 'pre',
        });
    };

    inputFocusAfter = () => {
        if (this.props.onFocus) {
            this.props.onFocus();
        }
        this.setState({
            focusIndex: 1,
            rangePlacement: this.context.direction === 'RTL' ? 'bottomLeft' : 'bottomRight',
            closeShow: true,
            open: false,
            position: 'aft',
        });
    };

    inputBlurPre = () => {
        if (this.props.onBlur) {
            this.props.onBlur();
        }
    };

    inputBlurAfter = () => {
        if (this.props.onBlur) {
            this.props.onBlur();
        }
    };

    isCloseShow = () => {
        this.setState({
            closeShow: false,
        });
    };

    getValueItem = (value: string | string[]): string => {
        const { focusIndex } = this.state;
        if (isArray(value)) {
            return value[focusIndex] || '';
        }
        return (value as string) || '';
    };

    popupVisibleChange = (isShow: boolean) => {
        const { open, onOpenChange } = this.props;

        const { isInputStatus } = this.state;

        if (onOpenChange) {
            onOpenChange(isShow);
        }
        if (!isShow) {
            this.isCloseShow();
        }
        if (isBoolean(open)) return;
        let states: any = { open: isShow };
        //如果当前关闭弹窗且处于输入状态,则清空输入状态
        if (isInputStatus && !isShow) {
            states = {
                ...states,
                ...initInputOptions,
            };
        }
        this.setState(states);
    };

    changeTimePeriod = (date: dayjs.Dayjs, type: any, period: 'AM' | 'PM', time?: number | string) => {
        const { format } = this.props;
        if (type !== '12Hours') {
            date = date.set(type, Number(time));
        }
        const hour = date.hour();
        const newHour = period === 'AM' ? hour % 12 : (hour % 12) + 12;
        date = date.hour(newHour);
        // 阿拉伯语的特殊适配
        const specialFormatAr = (val: string) => {
            const dayjsLocal = locale.lng('DatePicker.dayjsLocal');
            if (dayjsLocal === 'ar' && isString(val)) {
                return val.replace('AM', 'ص').replace('PM', 'م');
            }
            return val;
        };
        return specialFormatAr(date.format(format));
    };

    //dayjs不能直接解析中文的12小时制时间如:'11:11:11 凌晨',需要转为英文12小时制时间如:'11:11:11 AM'
    parseTimeStr = (timeStr: any) => {
        const newTimeStr = this.getValueItem(timeStr || '');
        if (!newTimeStr) {
            return { period: '', str: '' };
        }
        const amPeriods = ['凌晨', '早上', '上午', 'ص'];
        const pmPeriods = ['下午', '晚上', 'م'];
        const hour = newTimeStr.split(':')[0];
        let period: any;
        //12点以前的中午转为AM,12点以后的中午转为PM
        if (
            amPeriods.some(p => newTimeStr.includes(p)) ||
            (newTimeStr.includes('中午') && hour < '12') ||
            newTimeStr.includes('AM')
        ) {
            period = 'AM';
        } else if (
            pmPeriods.some(p => newTimeStr.includes(p)) ||
            (newTimeStr.includes('中午') && hour >= '12') ||
            newTimeStr.includes('PM')
        ) {
            period = 'PM';
        }
        const transformTimeStr = () => {
            let changedStr = newTimeStr.replace(/凌晨|早上|上午|中午|下午|晚上/, period === 'AM' ? '上午' : '下午');
            // 阿拉伯语的特殊适配
            changedStr = changedStr.replace(/ص|م/, period);
            return changedStr;
        };
        return {
            period,
            str: transformTimeStr(),
        };
    };

    handleTimeSelect = (time: number | string, type?: dayjs.UnitType | any, e?: any, twelveHoursType?: 'AM' | 'PM') => {
        const { value: currentValue, focusIndex, isInputStatus, isInputValid, inputValueTexts } = this.state;
        const { onChange, format, range, use12Hours, valueOfType } = this.props;
        //如果处于输入状态,且匹配,则使用已输入的值
        const isEditableValid = Boolean(isInputValid && inputValueTexts);
        const item = this.getValueItem(isEditableValid ? inputValueTexts[focusIndex] : currentValue);
        const newTimeStr = this.parseTimeStr(item);
        const used12HoursTime =
            use12Hours && newTimeStr.str
                ? dayjs(newTimeStr.str, format)
                : this.setValue(item.split(':')).set(type!, time as number);
        const newTwelveHoursType = twelveHoursType || newTimeStr.period || 'AM';
        const newItem = use12Hours
            ? this.changeTimePeriod(used12HoursTime, type, newTwelveHoursType, time)
            : this.setValue(item.split(':'))
                  .set(type!, time as number)
                  .format(format);

        let newStates: any = {
            value: range ? [] : '',
        };
        if (!type) {
            // 点击预设按钮
            newStates.value = time as string;
            newStates.open = false;
        } else {
            if (range) {
                let rangeValues = cloneDeep(currentValue);
                //判断是否手动鼠标混合选择,如果有则将另一个值填进去
                if (isInputStatus && isInputValid) {
                    rangeValues = [...inputValueTexts];
                }
                if (isArray(rangeValues)) {
                    newStates.value =
                        focusIndex === 0
                            ? [newItem, rangeValues[focusIndex + 1]]
                            : [rangeValues[focusIndex - 1], newItem];
                }
            } else {
                newStates.value = newItem;
                if (isInputStatus) {
                    newStates = {
                        ...newStates,
                        ...initInputOptions,
                    };
                }
            }
        }
        newStates = {
            ...newStates,
            ...initInputOptions,
        };
        this.setState(newStates, () => {
            if (isFunction(onChange)) {
                //使用12小时制多加一个参数
                if (use12Hours) {
                    let normalValue;
                    if (range) {
                        normalValue = newStates.value.map((val: string) => {
                            if (!val) {
                                val = '';
                            } else {
                                const str = this.parseTimeStr(val);
                                val = dayjs(str.str, format).format('HH:mm:ss');
                            }
                            return val;
                        });
                    } else {
                        normalValue = newStates.value
                            ? dayjs(this.parseTimeStr(newStates.value).str, format).format('HH:mm:ss')
                            : '';
                    }
                    const use12HoursRealValue =
                        valueOfType === 'string'
                            ? newStates.value
                            : timeTypesFormatter({
                                  value: normalValue,
                                  range,
                                  valueOfType: valueOfType!,
                                  format: 'HH:mm:ss',
                              });
                    onChange(use12HoursRealValue, normalValue);
                } else {
                    onChange(
                        timeTypesFormatter({
                            value: newStates.value,
                            range,
                            valueOfType: valueOfType!,
                            format: format!,
                        }),
                    );
                }
            }
        });
    };

    findFormatIndex = (formatList: string[], format: string, defaultIndex: number): number => {
        const index = formatList.indexOf(format);
        return index !== -1 ? index : defaultIndex;
    };

    setValue = (arrTime: string[]) => {
        const { format, hourStep, minuteStep, secondStep } = this.props;
        const formatArr = format!.split(':');
        const formatList = formatArr.map(item => item.toLocaleLowerCase());
        let time = dayjs();
        if (arrTime.some(item => item === '')) {
            if (hourStep !== 1 || minuteStep !== 1 || secondStep !== 1) {
                return time.set('hour', 0).set('minute', 0).set('second', 0);
            }
            return time;
        }
        if (formatList.includes('hh')) {
            time = time.set('hour', parseInt(arrTime[this.findFormatIndex(formatList, 'hh', 0)], 10));
        }
        if (formatList.includes('mm')) {
            time = time.set('minute', parseInt(arrTime[this.findFormatIndex(formatList, 'mm', 1)], 10));
        }
        if (formatList.includes('ss')) {
            time = time.set('second', parseInt(arrTime[this.findFormatIndex(formatList, 'ss', 2)], 10));
        }
        return time;
    };

    renderClear = () => {
        const { clearable, disabled, range, suffix, suffixIcon, size = this.context.size || 'normal' } = this.props;
        const { closeShow } = this.state;
        const margin = this.context.direction === 'RTL' ? { marginLeft: -28 } : { marginRight: -28 };
        const style = { fontSize: ['compact', 'mini', 'small'].includes(size) ? 14 : 16, color: $gray400, ...margin };
        return clearable && !disabled
            ? suffix || (
                  <Icon
                      style={range ? style : {}}
                      name={closeShow ? 'times-circle-new' : suffixIcon || 'time-new'}
                      onClick={e => {
                          e.stopPropagation();
                          this.clearTime();
                      }}
                      onMouseEnter={this.mouseEnter}
                      onMouseLeave={this.mouseLeave}
                  />
              )
            : suffix || (
                  <Icon
                      name={suffixIcon || 'time-new'}
                      style={range ? style : {}}
                  />
              );
    };

    handleInputClick = () => {
        const { disabled } = this.props;
        const { open } = this.state;
        if (!disabled && !open) {
            this.setState({
                open: true,
            });
        }
    };

    onInputChange = (value: string) => {
        const { isInputStatus, isInputValid, focusIndex, inputValueTexts, value: currentValue, position } = this.state;
        const { disabledTime, format, hourStep, minuteStep, secondStep } = this.props;
        let newInputValueTexts = [...inputValueTexts];
        const newState: any = {};
        if (!isInputStatus) {
            newState.isInputStatus = true;
            newInputValueTexts = [...currentValue];
        }
        if (isInputValid) {
            newState.isInputValid = false;
        }
        newInputValueTexts[focusIndex] = value;
        newState.inputValueTexts = newInputValueTexts;
        this.setState(newState);
        const inputMatchedFormattedValue = dayjs(value, format, true);
        const isValid = inputMatchedFormattedValue.isValid();
        const hour = inputMatchedFormattedValue.hour();
        const minute = inputMatchedFormattedValue.minute();
        const second = inputMatchedFormattedValue.second();

        // 提取小时、分钟和秒
        let isDisabled = false;
        //如果有disabledTime则需要判断所选时间在不在禁选时间内,如果在的话则不进行赋值操作
        if (disabledTime && isFunction(disabledTime)) {
            // 检查输入值是否在禁用时间范围内
            const isHourDisabled = disabledTime(hour, 'hour', position);
            const isMinuteDisabled = disabledTime(minute, 'minute', position);
            const isSecondDisabled = disabledTime(second, 'second', position);
            isDisabled = isHourDisabled || isMinuteDisabled || isSecondDisabled;
        }
        //如果有step,判断所选值在不在step之间
        if (hourStep) {
            if (hour % hourStep !== 0) {
                isDisabled = true;
            }
        }
        if (minuteStep) {
            if (minute % minuteStep !== 0) {
                isDisabled = true;
            }
        }
        if (secondStep) {
            if (second % secondStep !== 0) {
                isDisabled = true;
            }
        }
        if (isValid && !isDisabled) {
            const newStates: any = {
                inputChangeValue: inputMatchedFormattedValue,
                isInputValid: true,
            };
            this.setState(newStates);
        }
    };

    onInputKeyDown = (e: any) => {
        const { open, isInputStatus, inputValueTexts, isInputValid, focusIndex } = this.state;
        const { onChange, range, editable } = this.props;
        //仅按下回车键才进行输入值匹配
        if ((e.key === 'Enter' || e.keyCode === 13) && editable) {
            if (range) {
                const isFinish = inputValueTexts.every(item => item);
                const unInputIndex = inputValueTexts.findIndex(item => !item);
                // 判断当前value是否全部符合
                if (isFinish && isInputValid) {
                    this.setState(
                        {
                            open: !open,
                            value: inputValueTexts,
                            ...initInputOptions,
                            inputValueTexts,
                        },
                        () => {
                            if (isFunction(onChange)) {
                                onChange(inputValueTexts);
                                if (focusIndex === 0 && this.firstInputRef.current) {
                                    this.firstInputRef.current.blur();
                                } else if (focusIndex === 1 && this.secondInputRef.current) {
                                    this.secondInputRef.current.blur();
                                }
                            }
                        },
                    );
                } else if (isInputValid && unInputIndex !== -1) {
                    //将focus状态改变为未输入值的input标签
                    if (unInputIndex === 0 && this.firstInputRef.current) {
                        this.firstInputRef.current.focus();
                    }
                    if (unInputIndex === 1 && this.secondInputRef.current) {
                        this.secondInputRef.current.focus();
                    }
                }
            } else {
                this.setState({
                    open: !open,
                });
                //如果当前是要关闭弹窗且处于手动录入模式且值匹配,就触发change
                if (open && isInputStatus) {
                    const newStates: any = { ...initInputOptions };
                    if (isInputValid) {
                        newStates.value = inputValueTexts[0];
                        newStates.inputValueTexts = inputValueTexts;
                    }
                    this.setState(newStates, () => {
                        if (isFunction(onChange) && isInputValid) {
                            onChange(inputValueTexts[0]);
                            if (this.firstInputRef.current) {
                                this.firstInputRef.current.blur();
                            }
                        }
                    });
                }
            }
        }
    };

    getPlacement = () => {
        const { placement } = this.props;
        if (placement) {
            return placement;
        } else {
            return this.context.direction === 'RTL' ? 'bottomRight' : 'bottomLeft';
        }
    };

    render() {
        const { value, open, rangePlacement, inputValueTexts, isInputStatus, isInputValid, focusIndex, position } =
            this.state;

        const {
            placeholder,
            defaultValue,
            popupContainer,
            className,
            disabled,
            disabledTime,
            format,
            inputClassName,
            size = this.context.size || 'normal',
            flip,
            autoFocus,
            range,
            style,
            popupStyle,
            panelRender,
            preset,
            separator,
            hourStep,
            minuteStep,
            secondStep,
            bordered,
            status,
            editable,
            use12Hours,
            valueOfType,
            variant,
            popupClassName,
            inputReadOnly,
            renderExtraFooter,
        } = this.props;
        const arrTime = this.getValueItem(value).split(':');
        const newTimeStr = this.parseTimeStr(value);
        //halfDayHelperTime 在use12Hours时对AM,PM进行高亮判断的辅助
        const halfDayHelperTime = use12Hours && arrTime && arrTime[0] ? dayjs(newTimeStr.str, format) : undefined;
        const defaultStringValue = getValueOfTypeValue({
            value: defaultValue,
            valueOfType: valueOfType!,
            format: format!,
            range: range!,
        });
        const defaultArrTime = this.getValueItem(defaultStringValue as string | string[]).split(':');
        const inputValueArrTime = isInputStatus ? this.getValueItem(inputValueTexts).split(':') : [];
        let timePickerValue;
        if (arrTime && arrTime[0]) {
            if (use12Hours) {
                timePickerValue = dayjs(newTimeStr.str, format);
            } else {
                timePickerValue = this.setValue(arrTime);
            }
        }
        let panelBody: React.ReactNode = (
            <TimePicker
                timeRef={this.rootNode}
                format={format!}
                className={className}
                preset={preset}
                style={popupStyle}
                hourStep={hourStep}
                minuteStep={minuteStep}
                secondStep={secondStep}
                value={timePickerValue}
                defaultValue={defaultArrTime && defaultArrTime[0] ? this.setValue(defaultArrTime!) : undefined}
                onChange={this.handleTimeSelect}
                disabledTime={disabledTime}
                inputValueArrTime={this.setValue(inputValueArrTime)}
                isInputValid={isInputValid}
                isInputStatus={isInputStatus}
                use12Hours={use12Hours}
                halfDayHelperTime={halfDayHelperTime}
                open={open}
                focusIndex={focusIndex}
                renderExtraFooter={renderExtraFooter}
                position={position}
            />
        );

        if (isFunction(panelRender)) {
            panelBody = panelRender(panelBody);
        }

        const groupCls = range
            ? classNames(`${this.context.prefixCls}-input-group has-icon`, {
                  [`${this.context.prefixCls}-input-group-disabled`]: disabled,
                  [`${this.context.prefixCls}-input-group-outline`]: bordered,
                  [`${this.context.prefixCls}-input-group-no-outline`]: !bordered,
                  [`${this.context.prefixCls}-input-group-outline-${status}`]: status,
                  [`${this.context.prefixCls}-input-group-outline-${variant}`]: variant,
              })
            : `${this.context.prefixCls}-input-wrap`;
        let preInputValue = isArray(value) ? value[0] : value;
        let nextInputValue = isArray(value) ? value[1] : '';
        if (isInputStatus) {
            preInputValue = inputValueTexts[0];
            nextInputValue = inputValueTexts[1];
        }
        return (
            <DayjsLocalProvider>
                <Trigger
                    builtinPlacements={getPlacements(flip)}
                    popupPlacement={range ? rangePlacement : this.getPlacement()}
                    popupVisible={disabled ? false : open}
                    onPopupVisibleChange={this.popupVisibleChange}
                    getPopupContainer={isFunction(popupContainer) ? popupContainer : () => popupContainer}
                    popupStyle={{ whiteSpace: 'nowrap' }}
                    action={['click']}
                    popup={panelBody}
                    popupClassName={popupClassName}
                >
                    <div
                        className={groupCls}
                        style={style}
                        // ref={this.inputNode}
                    >
                        <Input
                            // eslint-disable-next-line no-nested-ternary
                            placeholder={
                                range
                                    ? (isArray(placeholder) && placeholder[0]) ||
                                    (locale.lng('DatePicker.selectStartPlaceholder') as string)
                                    : (placeholder as string) || (locale.lng('DatePicker.placeholder') as string)
                            }
                            inputRef={this.firstInputRef}
                            onFocus={this.inputFocusPre}
                            onBlur={this.inputBlurPre}
                            className={inputClassName}
                            disabled={disabled}
                            autoFocus={autoFocus}
                            size={size}
                            bordered={!range ? bordered : true}
                            inputStatus={!range ? status : undefined}
                            value={preInputValue}
                            suffix={!range && this.renderClear()}
                            onChange={e => {
                                if (editable) {
                                    if (!open) {
                                        this.setState({
                                            open: true,
                                        });
                                    }
                                    this.onInputChange(e.target.value);
                                }
                            }}
                            onClick={e => {
                                this.handleInputClick();
                                e.stopPropagation();
                            }}
                            onKeyDown={this.onInputKeyDown}
                            styleType={variant}
                            readOnly={inputReadOnly}
                        />
                        {range && (
                            <>
                                {separator || <span className="input-separator">-</span>}
                                <Input
                                    placeholder={
                                        (isArray(placeholder) && placeholder[1]) ||
                                        (locale.lng('DatePicker.selectEndPlaceholder') as string)
                                    }
                                    inputRef={this.secondInputRef}
                                    onFocus={this.inputFocusAfter}
                                    onBlur={this.inputBlurAfter}
                                    className={inputClassName}
                                    disabled={disabled}
                                    styleType={variant}
                                    size={size}
                                    value={nextInputValue}
                                    onChange={e => {
                                        if (editable) {
                                            if (!open) {
                                                this.setState({
                                                    open: true,
                                                });
                                            }
                                            this.onInputChange(e.target.value);
                                        }
                                    }}
                                    onKeyDown={this.onInputKeyDown}
                                    onClick={e => {
                                        this.handleInputClick();
                                        e.stopPropagation();
                                    }}
                                    readOnly={inputReadOnly}
                                />
                            </>
                        )}
                        {range && this.renderClear()}
                    </div>
                </Trigger>
            </DayjsLocalProvider>
        );
    }
}

polyfill(TimeSelect);
export default withDisabled<typeof TimeSelect>(TimeSelect);
