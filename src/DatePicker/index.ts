/*
 * DatePicker
 */
import RooDatePicker from './DatePicker';
import RooRangePicker from './RangePicker';
import RooTimePicker from './TimeSelect';
import withApiCompatibility from '../_utils/hoc/withApiCompatibility';

const WrappedDatePicker = withApiCompatibility('DatePicker', RooDatePicker) as typeof RooDatePicker;
const WrappedRangePicker = withApiCompatibility('RangePicker', RooRangePicker) as typeof RooRangePicker;
const WrappedTimePicker = withApiCompatibility('TimePicker', RooTimePicker) as typeof RooTimePicker;

// 将包装后的组件作为静态属性挂载到主组件上
// @ts-ignore
WrappedDatePicker.RangePicker = WrappedRangePicker;
WrappedDatePicker.TimePicker = WrappedTimePicker;

// 导出包装后的组件，确保所有组件都支持API兼容
export { WrappedRangePicker as RangePicker, WrappedTimePicker as TimePicker };
export * from './interface';
export default WrappedDatePicker;
