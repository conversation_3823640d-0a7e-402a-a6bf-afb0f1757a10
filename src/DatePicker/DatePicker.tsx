/* eslint-disable @typescript-eslint/member-ordering */
import * as React from 'react';
import * as PropTypes from 'prop-types';
import Trigger from '@rc-component/trigger';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import isoWeek from 'dayjs/plugin/isoWeek';
import { polyfill } from 'react-lifecycles-compat';
import { createRef } from '@roo/create-react-ref';
import locale from '@roo/roo/locale';
import warning from 'warning';
import classNames from 'classnames';
import { isFunction, isArray, isObject } from '@utiljs/is';
import isBoolean from 'lodash/isBoolean';
import isEqual from 'lodash/isEqual';
import isUndefined from 'lodash/isUndefined';
import cloneDeep from 'lodash/cloneDeep';
import IconClear from './IconClear';
import { PANEL_LIST } from './const';
import DayjsLocalProvider from './DayjsLocalProvider';
import {
    getTimeValue,
    togglePanel,
    dateFormatter,
    quarterOrHalfToDate,
    timestampToQuarterOrHalf,
    initFormat,
    getFormat,
    initUse12HourValue,
} from './utils';
import { getPlacements } from './placements';
import Icon from '../Icon';
import Input from '../Input';
import DatePanel from './DatePanel';
import MonthPanel from './MonthPanel';
import QuarterPanel from './QuarterPanel';
import HalfYearPanel from './HalfYearPanel';
import YearPanel from './YearPanel';
import TimePicker from './TimePicker';
import {
    datePickerModes,
    DatePickerMode,
    DatePickerProps,
    RangePickerProps,
    TimeSelectProps,
    DateValueType,
    dateValueTypes,
} from './interface';
import { somePlacements, Size, sizes } from '../_utils/types';
import { GlobalConfigContext } from '../ConfigProvider';
import { $gray400 } from '../_utils/ThemeColor';
import withDisabled from '../_utils/hoc/withDisabled';

import 'dayjs/locale/ar';

const initInputOptions = {
    inputValueTexts: ['', ''],
    isInputStatus: false,
    isInputValid: false,
    inputCallback: () => {},
    inputCallbackDate: [],
    inputCallbackParams: {},
    inputWeekStartDate: undefined,
    inputWeekEndDate: undefined,
};
interface ExtraProps {
    position?: 'pre' | 'aft';
}

const sizeMap = {
    large: 'lg',
    normal: 'md',
    small: 'sm',
    mini: 'xs',
    compact: 'compact',
};

//依赖customParseFormat插件使用严格解析模式
dayjs.extend(customParseFormat);
dayjs.extend(quarterOfYear);
dayjs.extend(weekOfYear);
dayjs.extend(isoWeek);

class DatePicker extends React.Component<DatePickerProps & ExtraProps, any> {
    static contextType = GlobalConfigContext;
    context!: React.ContextType<typeof GlobalConfigContext>;

    static defaultProps = {
        defaultValue: undefined,
        className: '',
        inputClassName: '',
        timeClassName: '',
        // value: '',
        type: 'date' as DatePickerMode,
        bordered: true,
        showTime: false,
        clearable: false,
        placeholder: '',
        // size: 'normal' as Size,
        disabled: false,
        placement: undefined,
        flip: true,
        format: 'YYYY-MM-DD',
        valueOfType: 'timestamp' as DateValueType,
        popupContainer: document.body || document.documentElement,
        open: undefined,
        autoFocus: false,
        range: false,
        editable: false,
        timeSteps: undefined,
        inputReadOnly: false,
        use12Hours: false,
        disabledDate: () => false,
        disabledTime: () => false,
        onBlur: () => {},
        onFocus: () => {},
        onChange: () => {},
        valueRender: undefined,
        cellRender: undefined,
        headerCellRender: undefined,
        headerMonthCellRender: undefined,
        monthSortCellRender: undefined,
        variant: undefined,
        status: undefined,
        presets: {},
        multiple: false,
    };

    static propTypes = {
        defaultValue: PropTypes.oneOfType([PropTypes.object, PropTypes.string, PropTypes.array]),
        className: PropTypes.string,
        inputClassName: PropTypes.string,
        timeClassName: PropTypes.string,
        // eslint-disable-next-line
        value: PropTypes.oneOfType([PropTypes.object, PropTypes.string, PropTypes.number, PropTypes.array]),
        type: PropTypes.oneOf(datePickerModes),
        bordered: PropTypes.bool,
        showTime: PropTypes.oneOfType([PropTypes.bool, PropTypes.object]),
        clearable: PropTypes.bool,
        placeholder: PropTypes.string,
        // size: PropTypes.oneOf(sizes),
        valueOfType: PropTypes.oneOf(dateValueTypes),
        flip: PropTypes.oneOfType([PropTypes.bool]),
        placement: PropTypes.oneOf(somePlacements),
        format: PropTypes.string,
        disabled: PropTypes.bool,
        popupContainer: PropTypes.oneOfType([PropTypes.instanceOf(HTMLElement), PropTypes.func]),
        open: PropTypes.bool,
        autoFocus: PropTypes.bool,
        range: PropTypes.bool,
        editable: PropTypes.bool,
        disabledDate: PropTypes.func,
        disabledTime: PropTypes.func,
        onChange: PropTypes.func,
        onBlur: PropTypes.func,
        onFocus: PropTypes.func,
        valueRender: PropTypes.func,
        timeSteps: PropTypes.object,
        inputReadOnly: PropTypes.bool,
        use12Hours: PropTypes.bool,
        cellRender: PropTypes.func,
        headerCellRender: PropTypes.func,
        headerMonthCellRender: PropTypes.func,
        monthSortCellRender: PropTypes.func,
        variant: PropTypes.oneOf(['line', 'plaintext']),
        status: PropTypes.oneOf(['normal', 'error', 'success']),
        presets: PropTypes.object,
        multiple: PropTypes.bool,
    };

    static RangePicker: React.ComponentClass<RangePickerProps>;

    static TimePicker: React.ComponentClass<TimeSelectProps>;

    constructor(props: DatePickerProps) {

        super(props);
        const {
            value: _value,
            defaultValue: _defaultValue,
            valueOfType,
            open,
            type,
            range,
            use12Hours,
            multiple,
        } = this.props;
        let value =
            type === 'quarter' || type === 'half' ? quarterOrHalfToDate(_value as string | string[], type) : _value;
        let defaultValue =
            type === 'quarter' || type === 'half'
                ? quarterOrHalfToDate(_defaultValue as string | string[], type)
                : _defaultValue || '';
        let newUse12HoursType = '';
        let selectEndDate: string | dayjs.Dayjs = '';
        let selectDate: any = dayjs();
        let startDate: any = dayjs();
        let oneValue: any = dayjs();
        let multipleValues: Array<'' | dayjs.Dayjs> = [];
        if (multiple) {
            const newValue = value || defaultValue || [];
            const multipleValue = isArray(newValue) ? newValue : [newValue];
            multipleValues = multipleValue?.map(item => dateFormatter(item, valueOfType));
            startDate = multipleValues[0] || startDate;
            selectDate = multipleValues[0] || selectDate;
        } else {
            if (use12Hours && valueOfType === 'string') {
                const { newValue, use12HoursType } = initUse12HourValue({
                    value,
                    range,
                });
                newUse12HoursType = use12HoursType;
                value = newValue;

                if (!value && defaultValue) {
                    const { newValue: newDefaultValue, use12HoursType: defaultValueUse12HoursType } =
                        initUse12HourValue({ value: defaultValue, range });
                    newUse12HoursType = defaultValueUse12HoursType;
                    defaultValue = newDefaultValue;
                }
            }

            const formatterValue =
                range && isArray(value)
                    ? [dateFormatter(value[0] || '', valueOfType), dateFormatter(value[1] || '', valueOfType)]
                    : dateFormatter(value, valueOfType);
            const formatterDefaultValue =
                range && isArray(defaultValue)
                    ? [
                        dateFormatter(defaultValue[0] || '', valueOfType),
                        dateFormatter(defaultValue[1] || '', valueOfType),
                    ]
                    : dateFormatter(defaultValue, valueOfType);

            oneValue = this.isControlled() ? formatterValue : formatterDefaultValue;
            const formattedDay = this.formatDefaultDay();
            startDate = range && isArray(oneValue) ? oneValue[0] || formattedDay : oneValue || formattedDay;
            selectDate =
                range && isArray(oneValue) ? oneValue || [formattedDay, formattedDay] : oneValue || formattedDay;

            //use12Hours
            if (use12Hours && !oneValue) {
                newUse12HoursType = dayjs().hour() < 12 ? 'AM' : 'PM';
            }

            if (type === 'week' && oneValue && !isArray(oneValue)) {
                const [d1, d2] = this.getWeekData(oneValue);

                selectDate = d1;
                selectEndDate = d2;
            }
        }
        this.state = {
            startDate,
            // NOTE: 默认选中时间是 2000-1-1: 00:00:00, 保证渲染时间内无选中的即可。
            selectDate,
            selectEndDate,
            value: oneValue,
            // eslint-disable-next-line
            initialValue: this.props.value, // 缓存初始 value
            dateShow: true,
            monthShow: false,
            yearShow: false,
            /**聚焦失焦关闭按钮展示 */
            closeShow: false,
            /**多选时hover到图标上关闭按钮展示 */
            _closeShow: false,
            type: '',
            open,
            firstSelect: true,
            format: 'YYYY-MM-DD',
            /**使用12小时制时对hour的辅助判断 */
            use12HoursType: newUse12HoursType,

            //以下属性为输入框输入时需要的
            /**输入的字符串 */
            inputValueTexts: ['', ''],
            /** 是否为输入状态 */
            isInputStatus: false,
            /** 输入值是否匹配 */
            isInputValid: false,
            /** 回车键调用的onChange回调函数 */
            inputCallback: () => {},
            /** 匹配的时间 */
            inputCallbackDate: [],
            /** 回车键调用的onChange回调函数的参数 */
            inputCallbackParams: {},
            /** 周选择需要start和end两个时间 */
            inputWeekStartDate: undefined,
            inputWeekEndDate: undefined,
            /** 当前聚焦下标 */
            focusIndex: 0,

            /** 多选的value,现有value不增加多选兼容, 改动过大,直接另起炉灶*/
            multipleValues,
            isCleared: false,
        };
    }

    componentDidMount(): void {
        const { type, format, showTime, use12Hours } = this.props;
        const newFormat = initFormat({
            type,
            format,
            showTime,
            use12Hours: Boolean(use12Hours),
        });
        this.setState({
            format: newFormat,
        });
    }

    static getDerivedStateFromProps(nextProps: DatePickerProps, state: any) {
        const { type, valueOfType, value: _value, range, open, showTime, format, use12Hours, multiple } = nextProps;
        const { value: stateValue } = state;
        const newState: any = {};
        let value =
            type === 'quarter' || type === 'half' ? quarterOrHalfToDate(_value as string | string[], type) : _value;
        if (!isUndefined(open)) {
            newState.open = open;
        }
        // 如果 format 发生变化，更新 format
        const newFormat = initFormat({
            type,
            format,
            showTime,
            use12Hours: Boolean(use12Hours),
        });
        if (newFormat !== state.format) {
            newState.format = newFormat;
        }

        if (!value && 'value' in nextProps) {
            if (stateValue) {
                return {
                    ...newState,
                    ...{
                        value: range ? ['', ''] : '',
                        startDate: dayjs(),
                        selectDate: range ? [dayjs(), dayjs()] : dayjs(),
                        type,
                    },
                };
            } else {
                return {
                    ...newState,
                    ...{
                        value: range ? ['', ''] : '',
                        type,
                    },
                };
            }
        }

        if (type === 'week' && !isArray(value)) {
            const nowValue = dateFormatter(value, valueOfType) || dayjs();
            if (
                'value' in nextProps &&
                (nowValue.valueOf() !== state.value.valueOf() || nextProps.type !== state.type)
            ) {
                let realDate = nowValue;
                while (realDate.day() !== 1) {
                    realDate = realDate.subtract(1, 'day');
                }
                return {
                    ...newState,
                    ...{
                        value: nowValue,
                        selectDate: realDate,
                        startDate: realDate,
                        selectEndDate: realDate.add(6, 'day').set('hour', 0).set('minute', 0).set('second', 0),
                        type,
                    },
                };
            } else {
                return newState;
            }
        }
        let newUse12HoursType = 'AM';
        if (value && use12Hours && valueOfType === 'string') {
            const { newValue, use12HoursType } = initUse12HourValue({
                value,
                range,
            });
            newUse12HoursType = use12HoursType;
            value = newValue;
        }
        const nowValue =
            (range || multiple) && isArray(value)
                ? value.map(item => (item ? dateFormatter(item, valueOfType) : undefined))
                : dateFormatter(value, valueOfType) || dayjs();

        if (
            'value' in nextProps &&
            ((isArray(nowValue) && !isEqual(nowValue, state.value)) ||
                (!isArray(nowValue) && nowValue?.valueOf() !== state.value?.valueOf()) ||
                nextProps.type !== state.type)
        ) {
            let startDate = range && isArray(nowValue) ? nowValue[0] || dayjs() : nowValue || dayjs();
            //如果是多选且不为清除, startDate以点击的startDate为准
            if (multiple) {
                if (state.isCleared) {
                    startDate = dayjs();
                } else {
                    startDate = state.startDate;
                }
            }
            return {
                ...newState,
                ...{
                    value: nowValue,
                    multipleValues: isArray(nowValue) ? nowValue.filter(Boolean) : [nowValue],
                    startDate,
                    selectDate: multiple && !isArray(nowValue) ? [nowValue] : nowValue,
                    initialValue: nextProps.value,
                    use12HoursType: newUse12HoursType,
                    type,
                    isCleared: false,
                },
            };
        }
        return newState;
    }

    private rootNode: any = createRef();

    private inputNode: any = createRef();

    private firstInputRef = createRef<HTMLInputElement>();

    private secondInputRef = createRef<HTMLInputElement>();

    // 格式化默认时间
    formatDefaultDay = () => {
        const { showTime } = this.props;

        let defaultValue;
        if (typeof showTime === 'object') {
            defaultValue = showTime.defaultValue;
        }
        const currentDate = defaultValue ? dayjs(defaultValue as any) : dayjs();

        return currentDate;
    };

    /**
     * 通过 value 是否存在 props 决定受控状态。
     * defaultProps 不能设置 value 默认值！！！
     */
    isControlled = (): boolean => 'value' in this.props;

    inputFocus = (focusIndex: number) => {
        this.setState({
            focusIndex,
            closeShow: true,
        });
        if (this.props.onFocus) {
            this.props.onFocus();
        }
    };

    inputBlur = () => {
        if (this.props.onBlur) {
            this.props.onBlur();
        }
    };

    isCloseShow = () => {
        this.setState({
            closeShow: false,
        });
    };

    /**
     * 根据 ISO 8601 标准获取周的起始和结束日期
     * @param now dayjs 对象
     * @returns [周起始日期, 周结束日期]
     */
    getWeekData = (now: dayjs.Dayjs) => {
        const weekStart = now.startOf('isoWeek');
        const weekEnd = weekStart.endOf('isoWeek').set('hour', 0).set('minute', 0).set('second', 0);
        return [weekStart, weekEnd];
    };

    getMultipleValue = ({ timestamp, date }: { timestamp: number; date: dayjs.Dayjs }) => {
        const { multipleValues = [] } = this.state;
        const { valueOfType, format } = this.props;
        // 使用isSame判断是否是同一天，而不是精确的时间戳比较
        const targetExists = multipleValues?.some((item: dayjs.Dayjs) => item.isSame(date, 'day'));
        const realMultipleValues = targetExists
            ? multipleValues?.filter((item: dayjs.Dayjs) => !item.isSame(date, 'day'))
            : multipleValues?.concat(date);
        const newArr = realMultipleValues?.sort((a: dayjs.Dayjs, b: dayjs.Dayjs) => a.valueOf() - b.valueOf());
        const formatedValues: (string | number)[] = [];
        const dayjsValues: dayjs.Dayjs[] = [];
        newArr?.forEach((item: dayjs.Dayjs) => {
            formatedValues.push(getTimeValue(valueOfType, item, format));
            dayjsValues.push(item);
        });
        return {
            formatedValues,
            dayjsValues,
        };
    };

    /**
     * 天,周选择 day,week
     * @param {number} timestamp - 选中的时间戳
     * @param {boolean} isDelete - 是否删除标志，只有多选时需要
     */
    onDateSelected = (timestamp: number, isDelete?: boolean) => {
        const { valueOfType, onChange, showTime, type, use12Hours, multiple } = this.props;
        const { format, isInputStatus } = this.state;
        //如果是在输入中,清空输入状态
        if (isInputStatus) {
            this.setState({
                ...initInputOptions,
            });
        }
        const date = dayjs(timestamp);
        const sd: dayjs.Dayjs = date.set('hour', date.hour()).set('minute', date.minute()).set('second', date.second());
        let multipleValue: any = {
            formatedValues: [],
            dayjsValues: [],
        };
        if (multiple) {
            multipleValue = this.getMultipleValue({
                timestamp,
                date: sd,
            });
        }
        const handleChange = () => {
            if (type === 'week') {
                const [weekStartDate, weekEndDate] = this.getWeekData(sd);
                if (isFunction(onChange)) {
                    onChange!(getTimeValue(valueOfType, sd, format), [
                        getTimeValue(valueOfType, weekStartDate, format),
                        getTimeValue(valueOfType, weekEndDate, format),
                    ]);
                }
            } else {
                if (!isFunction(onChange)) return;
                if (!multiple && use12Hours && valueOfType === 'string') {
                    onChange!(getTimeValue(valueOfType, sd, format), sd.format('YYYY-MM-DD HH:mm:ss'));
                    return;
                }
                if (multiple) {
                    const newArrValue = multipleValue.formatedValues;
                    onChange(newArrValue);
                    return;
                }
                onChange!(getTimeValue(valueOfType, sd, format));
            }
        };

        if (this.isControlled()) {
            this.setState(
                {
                    open: showTime || (!isDelete && multiple),
                },
                () => {
                    handleChange();
                },
            );
        } else {
            const newState: any = {
                startDate: dayjs(timestamp),
                selectDate: sd,
                value: sd,
                open: showTime || (!isDelete && multiple),
            };
            if (type === 'week') {
                const [weekStartDate, weekEndDate] = this.getWeekData(sd);
                newState.selectDate = weekStartDate;
                newState.selectEndDate = weekEndDate;
            }
            if (multiple) {
                newState.multipleValues = multipleValue.dayjsValues;
            }
            this.setState(newState, handleChange);
        }

        if (this.props.onOpenChange) {
            this.props.onOpenChange(Boolean(showTime) || Boolean(multiple));
        }
        if (!showTime && !multiple) {
            this.isCloseShow();
        }
    };

    selectMonthOrYear = (params: { timestamp: number; panelType: string; e?: React.SyntheticEvent }) => {
        const { timestamp, panelType, e } = params;
        if (e) {
            e.stopPropagation();
        }
        const { type, onChange, valueOfType, range } = this.props;
        const { value, selectDate, firstSelect, isInputStatus, format } = this.state;
        //如果在输入中,清空输入状态
        if (isInputStatus) {
            this.setState({
                ...initInputOptions,
            });
        }
        const selectValue = getTimeValue(valueOfType, dayjs(timestamp), format);
        let newValue: (string | number)[] | number | string = selectValue;
        let newSelectDate: dayjs.Dayjs | dayjs.Dayjs[] = dayjs(timestamp);
        if (type === 'month' || type === 'year' || type === 'quarter' || type === 'half') {
            if (range) {
                newValue = firstSelect ? [selectValue, ''] : [getTimeValue(valueOfType, value[0], format), selectValue];
                if (newValue[0] && newValue[1] && dayjs(newValue[0]).valueOf() > dayjs(newValue[1]).valueOf()) {
                    newValue.reverse();
                }
                newSelectDate = firstSelect ? [dayjs(timestamp), ''] : [selectDate[0], dayjs(timestamp)];
            }
            const isOpen = range ? !!firstSelect : false;
            if ('value' in this.props) {
                this.setState(
                    {
                        open: isOpen,
                        firstSelect: range ? !firstSelect : false,
                    },
                    () => {
                        if (isFunction(onChange)) {
                            if (type === 'quarter' || type === 'half') {
                                const { _value = '', _range = [] } = timestampToQuarterOrHalf(newValue, type) || {};
                                onChange(_value, _range);
                            } else {
                                onChange(newValue);
                            }
                        }
                    },
                );
            } else {
                const tl = togglePanel(PANEL_LIST, `${panelType}Show`);
                const newStates = {
                    startDate: dayjs(timestamp),
                    value: newSelectDate,
                    selectDate: newSelectDate,
                    open: isOpen,
                    firstSelect: range ? !firstSelect : false,
                };
                if (
                    range &&
                    isArray(newSelectDate) &&
                    newSelectDate[0] &&
                    newSelectDate[1] &&
                    dayjs(newSelectDate[0]).valueOf() > dayjs(newSelectDate[1]).valueOf()
                ) {
                    const reverseSelectDate = newSelectDate.reverse();
                    newStates.value = reverseSelectDate;
                    newStates.selectDate = reverseSelectDate;
                }
                this.setState({
                    ...newStates,
                    ...tl,
                });
                if (isFunction(onChange)) {
                    if (type === 'quarter' || type === 'half') {
                        const { _value = '', _range = [] } = timestampToQuarterOrHalf(newValue, type) || {};
                        onChange(_value, _range);
                    } else {
                        onChange(newValue);
                    }
                }
            }
            if (this.props.onOpenChange) {
                this.props.onOpenChange(isOpen);
            }
            if (!isOpen) {
                this.isCloseShow();
            }
            return;
        }

        const tl = togglePanel(PANEL_LIST, `${panelType}Show`);
        this.setState({
            startDate: dayjs(timestamp),
            ...tl,
        });
    };

    getSelectedData = (time: number, type: dayjs.UnitType, twelveHoursType?: 'AM' | 'PM') => {
        const {
            selectDate: selectDateByClick,
            isInputStatus,
            isInputValid,
            inputCallbackDate,
            use12HoursType,
        } = this.state;
        const { use12Hours } = this.props;
        let selectDate = selectDateByClick;
        if (isInputStatus && isInputValid) {
            selectDate = inputCallbackDate[0];
        }

        let tempData = null;
        // 判断是否选择过日期, 没有选择过则使用当前时间
        if (selectDate.unix() === dayjs('2000-1-1 00:00:00').unix()) {
            tempData = dayjs().set('second', 0).set('hour', 0).set('minute', 0).set(type, time);
        } else {
            tempData = selectDate.set(type, time);
            if (use12Hours) {
                const tempDataHour = tempData.hour();
                if (twelveHoursType) {
                    if (twelveHoursType === 'AM' && tempDataHour >= 12) {
                        tempData = tempData.subtract(12, 'hour');
                    }
                    if (twelveHoursType === 'PM' && tempDataHour < 12) {
                        tempData = tempData.add(12, 'hour');
                    }
                } else {
                    if (use12HoursType === 'AM' && tempDataHour >= 12) {
                        tempData = tempData.subtract(12, 'hour');
                    }
                    if (use12HoursType === 'PM' && tempDataHour < 12) {
                        tempData = tempData.add(12, 'hour');
                    }
                }
            }
        }
        return tempData;
    };

    /**
     * 时分秒选择
     */
    selectTime = (time: number, type: dayjs.UnitType, _: any, use12HoursType?: 'AM' | 'PM') => {
        const { onChange, valueOfType, use12Hours } = this.props;
        const { isInputStatus, format } = this.state;
        const { dateFormat } = getFormat(format);
        const handleChange = () => {
            const tempData = this.getSelectedData(time, type, use12HoursType);
            const sd = getTimeValue(valueOfType, tempData, format);
            if (isFunction(onChange)) {
                if (use12Hours && valueOfType === 'string') {
                    //string类型12小时制多返回24小时制时间
                    onChange(sd, tempData.format(`${dateFormat} HH:mm:ss`));
                } else {
                    onChange(sd);
                }
            }
        };

        if (this.isControlled()) {
            handleChange();
        } else {
            const tempData = this.getSelectedData(time, type, use12HoursType);
            const sd = getTimeValue(valueOfType, tempData, format);
            if (isFunction(onChange)) {
                if (use12Hours && valueOfType === 'string') {
                    //string类型12小时制多返回24小时制时间
                    onChange(sd, tempData.format(`${dateFormat} HH:mm:ss`));
                } else {
                    onChange(sd);
                }
            }
            const newState: any = {
                selectDate: tempData,
                startDate: tempData,
                value: tempData,
            };
            if (use12HoursType) {
                newState.use12HoursType = use12HoursType;
            }
            this.setState(newState);
        }
        if (isInputStatus) {
            this.setState({
                ...initInputOptions,
            });
        }
    };

    /**
     * 箭头图标切换年月
     */
    handleChangeDate = (type: string, unitType: dayjs.UnitType, count?: number) => {
        const { startDate, isInputStatus, inputCallbackDate } = this.state;
        count = count || 1;
        const isEditableValid = isInputStatus && inputCallbackDate.length;
        let calcuDate: dayjs.Dayjs = dayjs();
        const usedDate = isEditableValid ? inputCallbackDate[0] : startDate;
        calcuDate = type === 'add' ? usedDate.add(count, unitType) : usedDate.subtract(count, unitType);
        this.setState({
            [isEditableValid ? 'inputCallbackDate' : 'startDate']: isEditableValid ? [calcuDate] : calcuDate,
        });
    };

    /**
     * TODO: 清除时间，返回 '' 是否合理？
     **/
    clearTime = () => {
        const { onChange, value, range, multiple } = this.props;

        const emptyValue = (range && isArray(value)) || multiple ? [] : '';
        const emptySelectDate = (range && isArray(value)) || multiple ? [dayjs(), dayjs()] : dayjs();
        if (value) {
            if (isFunction(onChange)) {
                onChange(emptyValue);
            }
            this.setState({
                firstSelect: true,
                isCleared: true,
            });
        } else {
            this.setState(
                {
                    value: emptyValue,
                    selectDate: emptySelectDate,
                    startDate: dayjs(),
                    selectEndDate: '',
                    firstSelect: true,
                    multipleValues: emptyValue,
                    isCleared: true,
                },
                () => {
                    if (isFunction(onChange)) {
                        onChange(emptyValue);
                    }
                },
            );
        }
    };

    popupVisibleChange = (isShow: boolean) => {
        const { open, onOpenChange } = this.props;
        const { isInputStatus } = this.state;
        if (onOpenChange) {
            onOpenChange(isShow);
        }
        if (!isShow) {
            this.isCloseShow();
        }
        if (isBoolean(open)) return;
        let states: any = { open: isShow };
        //如果当前关闭弹窗且处于输入状态,则清空输入状态
        if (isInputStatus && !isShow) {
            states = {
                ...states,
                ...initInputOptions,
            };
        }
        this.setState(states);
    };

    showPanel = (type: string) => {
        const tl = togglePanel(PANEL_LIST, `${type}Show`);
        this.setState({
            ...tl,
        });
    };

    renderClear = () => {
        const { clearable, disabled, range, suffix, suffixIcon, size = this.context.size || 'normal' } = this.props;
        const margin = this.context.direction === 'RTL' ? { marginLeft: -28 } : { marginRight: -28 };
        const style = { fontSize: ['compact', 'mini', 'small'].includes(size) ? 14 : 16, color: $gray400, ...margin };
        const { closeShow } = this.state;
        return clearable && !disabled ? (
            <IconClear
                suffix={suffix}
                suffixIcon={suffixIcon}
                style={range ? style : {}}
                onClick={this.clearTime}
                closeShow={closeShow}
            />
        ) : (
            suffix || (
                <Icon
                    name={suffixIcon || 'calendar-new'}
                    style={range ? style : {}}
                />
            )
        );
    };

    parseQuarterString(quarterString: string): dayjs.Dayjs | null {
        const match = /^(\d{4})-Q(\d)$/.exec(quarterString);
        if (match) {
            const year = parseInt(match[1], 10);
            const quarter = parseInt(match[2], 10);
            if (quarter >= 1 && quarter <= 4) {
                const month = (quarter - 1) * 3;
                return dayjs(`${year}-${month + 1}-01`);
            }
        }
        return null;
    }

    /**
     * 解析周字符串，使用 ISO 8601 标准
     * @param weekString 周字符串，格式如：2025-1 周
     * @returns dayjs 对象
     */
    parseWeekString(weekString: string): dayjs.Dayjs | null {
        const localWeek = locale.lng('DatePicker.week');
        const match = new RegExp(`^(\\d{4})-(\\d{1,2})\\s?${localWeek}$`).exec(weekString);
        if (match) {
            const year = parseInt(match[1], 10);
            const week = parseInt(match[2], 10);
            // 使用 ISO 周标准
            // console.log(dayjs().year(year), year, week, dayjs().year(year).set('date', 1).set('month', 0));
            // 从指定年份的1月4日开始计算（ISO 8601标准规定每年第一周包含1月4日）
            const jan4th = dayjs().year(year).month(0).date(4);
            // 获取包含1月4日的那一周的第一天
            const firstWeek = jan4th.startOf('isoWeek');
            // 计算目标周的第一天
            return firstWeek.add(week - 1, 'weeks');
            // return dayjs().year(year).startOf('year').isoWeek(week).startOf('isoWeek');
        }
        return null;
    }

    handleInputClick = () => {
        const { disabled, onOpenChange, range, type, value } = this.props;
        const { open } = this.state;
        if (range && (type === 'date' || type === 'week') && 'value' in this.props && isArray(value)) {
            warning(false, `DatePicker: range下，type=${type}的value不应该是数组`, value);
            return;
        }
        if (onOpenChange) {
            onOpenChange(!open);
        }
        if (!disabled && !open) {
            this.setState({
                open: true,
            });
        }
    };

    onInputChange = (value: string) => {
        const { type, showTime, timeSteps, disabledDate, disabledTime, position } = this.props;
        const {
            format,
            isInputStatus,
            isInputValid,
            focusIndex,
            inputValueTexts,
            inputCallbackDate,
            inputWeekStartDate,
            startDate,
            selectDate: stateSelectDate,
            selectEndDate: stateSelectEndDate,
        } = this.state;
        const newInputCallbackDate = cloneDeep(inputCallbackDate);
        const newInputValueTexts = cloneDeep(inputValueTexts);
        newInputValueTexts[focusIndex] = value;
        const newState: any = { inputValueTexts: newInputValueTexts };
        if (!isInputStatus) {
            newState.isInputStatus = true;
        }
        if (isInputValid) {
            newState.isInputValid = false;
        }
        this.setState(newState);

        let newFormat;
        const inputCallbackParams: any = {};
        //panelType对应枚举year-month,half-half,month-date,qu-qu
        let changeFunction: any = () => {};
        switch (type) {
            case 'date':
                if (showTime) {
                    newFormat = 'YYYY-MM-DD HH:mm:ss';
                } else {
                    newFormat = 'YYYY-MM-DD';
                }
                if (format) {
                    newFormat = format;
                }
                changeFunction = this.onDateSelected;
                break;
            case 'month':
                newFormat = 'YYYY-MM';
                if (format) {
                    newFormat = format;
                }
                changeFunction = this.selectMonthOrYear;
                inputCallbackParams.panelType = 'date';
                break;
            case 'half':
                newFormat = 'YYYY-[H]H';
                changeFunction = this.selectMonthOrYear;
                inputCallbackParams.panelType = 'half';
                break;
            case 'quarter':
                newFormat = 'YYYY-[Q]Q';
                changeFunction = this.selectMonthOrYear;
                inputCallbackParams.panelType = 'quarter';
                break;
            case 'week':
                newFormat = 'YYYY-wo';
                changeFunction = this.onDateSelected;
                break;
            case 'year':
                newFormat = 'YYYY';
                changeFunction = this.selectMonthOrYear;
                inputCallbackParams.panelType = 'month';
                break;
            default:
                break;
        }
        let inputMatchedFormattedValue = dayjs(value, newFormat, true);
        const newStates: any = {
            inputCallback: changeFunction,
        };
        let isValid = inputMatchedFormattedValue.isValid();
        //特殊情况处理,dayjs不能直接解析YYYY-[Q]Q和YYYY-wo,手动解析
        const isValidQuarter = this.parseQuarterString(value);
        if (type === 'quarter' && isValidQuarter) {
            inputMatchedFormattedValue = isValidQuarter;
            isValid = true;
        }
        const isValidWeek = this.parseWeekString(value);
        if (type === 'week') {
            if (isValidWeek) {
                inputMatchedFormattedValue = isValidWeek;
                isValid = true;
                const [selectDate, selectEndDate] = this.getWeekData(inputMatchedFormattedValue);
                newStates.inputCallbackDate = [selectDate];
                newStates.inputWeekEndDate = selectEndDate;
                newStates.inputWeekStartDate = inputMatchedFormattedValue;
            } else {
                //如果没有匹配到,并且无inputWeekStartDate,就将现有值带入,防止日期跳动
                if (!inputWeekStartDate) {
                    this.setState({
                        inputWeekStartDate: startDate,
                        inputCallbackDate: stateSelectDate,
                        inputWeekEndDate: stateSelectEndDate,
                    });
                }
            }
        }
        if (!isValid) return false;
        let isDisabled = false;
        const hour = inputMatchedFormattedValue.hour();
        const minute = inputMatchedFormattedValue.minute();
        const second = inputMatchedFormattedValue.second();
        if (disabledDate && isFunction(disabledDate)) {
            // 检查输入值是否在禁用时间范围内
            isDisabled = disabledDate(inputMatchedFormattedValue);
            if (isDisabled) return false;
        }
        if (showTime) {
            //如果showTime并且传入了timeSteps,需要判断步长
            if (timeSteps) {
                const { hourStep, minuteStep, secondStep } = timeSteps;
                if (hourStep) {
                    if (hour % hourStep !== 0) {
                        isDisabled = true;
                        return false;
                    }
                }

                if (minuteStep) {
                    if (minute % minuteStep !== 0) {
                        isDisabled = true;
                        return false;
                    }
                }
                if (secondStep) {
                    if (second % secondStep !== 0) {
                        isDisabled = true;
                        return false;
                    }
                }
            }
            if (disabledTime && isFunction(disabledTime)) {
                // 检查输入值是否在禁用时间范围内
                const isHourDisabled = disabledTime(hour, 'hour', position);
                const isMinuteDisabled = disabledTime(minute, 'minute', position);
                const isSecondDisabled = disabledTime(second, 'second', position);
                isDisabled = isHourDisabled || isMinuteDisabled || isSecondDisabled;
                if (isDisabled) return false;
            }
        }

        if (isValid && !isDisabled) {
            //特殊日期需处理一下
            switch (type) {
                case 'half':
                    //半年判断需转换为季度Dayjs
                    const year = inputMatchedFormattedValue.year();
                    const quarter = inputMatchedFormattedValue.format('H') === '1' ? 1 : 3;
                    inputMatchedFormattedValue = dayjs(`${year}-01-01`).quarter(quarter);
                    break;

                default:
                    break;
            }
            inputCallbackParams.timestamp = inputMatchedFormattedValue.valueOf();
            newStates.inputCallbackParams = inputCallbackParams;
            if (type !== 'week') {
                newInputCallbackDate[focusIndex] = inputMatchedFormattedValue;
                newStates.inputCallbackDate = newInputCallbackDate;
                newStates.inputWeekEndDate = inputMatchedFormattedValue;
            }
            newStates.isInputValid = true;
            this.setState(newStates);
        }
        return true;
    };

    onInputKeyDown = (e: any) => {
        const { open, isInputStatus, inputCallback, inputCallbackDate, inputCallbackParams, isInputValid } = this.state;
        const { type, range, editable } = this.props;
        //仅按下回车键才进行输入值匹配
        if (e.key === 'Enter' && editable) {
            let functionParams: any = null;
            switch (type) {
                case 'date':
                case 'week':
                    functionParams = inputCallbackDate;
                    break;
                case 'month':
                case 'half':
                case 'quarter':
                case 'year':
                    functionParams = inputCallbackParams;
                    break;
                default:
                    functionParams = {};
                    break;
            }
            //range可输入本期不上,与RangePicker可输入一起上
            if (range) {
                // const isFinish = inputValueTexts.every((item: string) => item);
                // const unInputIndex = inputValueTexts.findIndex((item: string) => !item);
                // // 判断当前value是否全部符合
                // if (isFinish && isInputValid) {
                //     this.setState({
                //         open: !open,
                //         value: inputValueTexts,
                //         ...initInputOptions
                //     }, () => {
                //         if (isFunction(inputCallback)) {
                //             inputCallback(functionParams);
                //         }
                //     });
                // } else if (unInputIndex !== -1) {
                //     //将foucus状态改变为未输入值的input标签
                //     if (unInputIndex === 0 && this.firstInputRef.current) {
                //         this.firstInputRef.current.focus();
                //     }
                //     if (unInputIndex === 1 && this.secondInputRef.current) {
                //         this.secondInputRef.current.focus();
                //     }
                // }
            } else {
                //如果当前是要关闭弹窗且处于手动录入模式,那就进行传值(手动调用不同类型的onChange)
                if (open && isInputStatus) {
                    if (isFunction(inputCallback)) {
                        if (isInputValid) {
                            inputCallback(functionParams);
                        }
                        if (this.firstInputRef.current) {
                            this.firstInputRef.current.blur();
                        }
                        this.setState({
                            ...initInputOptions,
                        });
                    }
                }
                this.setState({
                    open: !open,
                });
            }
        }
    };

    getPlacement = () => {
        const { placement } = this.props;
        if (placement) {
            return placement;
        } else {
            return this.context.direction === 'RTL' ? 'bottomRight' : 'bottomLeft';
        }
    };

    handleMultipleItemClick = (e: React.MouseEvent, item: dayjs.Dayjs) => {
        if (e) {
            e.preventDefault();
        }
        this.onDateSelected(item.valueOf(), true);
    };

    getMultipleInputClassName(): string {
        const { disabled, size = this.context.size || 'normal', variant, bordered } = this.props;

        const list = [
            `${this.context.prefixCls}-input`,
            bordered ? '' : `${this.context.prefixCls}-input-noborder`,
            `${this.context.prefixCls}-input-tag`,
        ];

        if (disabled) {
            list.push('disabled');
        }

        if (size) {
            list.push(`${this.context.prefixCls}-input-${sizeMap[size]}`);
        }

        if (variant) {
            list.push(`${this.context.prefixCls}-input-${variant}`);
        }

        return classNames(list);
    }

    _renderMultiple = () => {
        const { size = this.context.size || 'normal', disabled, clearable } = this.props;
        const { multipleValues, format } = this.state;
        return (
            <>
                {multipleValues?.map((item: dayjs.Dayjs) => (
                    <span
                        className={classNames(`${this.context.prefixCls}-tag ${this.context.prefixCls}-tag-gray`, {
                            [`${this.context.prefixCls}-tag-${sizeMap[size]}`]: size !== 'normal',
                        })}
                        key={item?.format(format)}
                    >
                        {item?.format(format)}
                        {!disabled && clearable ? (
                            <i
                                className="roo-icon roo-icon-close-new"
                                onClick={e => {
                                    e.stopPropagation();
                                    this.handleMultipleItemClick(e, item);
                                }}
                            />
                        ) : null}
                    </span>
                ))}
            </>
        );
    };

    renderSuffixIcon(): React.ReactNode {
        const { disabled, clearable, suffixIcon, suffix } = this.props;

        const { closeShow, _closeShow } = this.state;
        if (clearable && !disabled && (closeShow || _closeShow)) {
            return (
                <i
                    className={classNames('roo-icon', 'addon-icon', 'roo-icon-times-circle-new', 'has-click')}
                    onClick={this.clearTime}
                    onMouseLeave={() => {
                        this.setState({
                            _closeShow: false,
                        });
                    }}
                />
            );
        }
        if (suffix) {
            return (
                <i
                    className={classNames('roo-icon', 'addon-icon')}
                    onMouseEnter={() => {
                        this.setState({
                            _closeShow: true,
                        });
                    }}
                >
                    {suffix}
                </i>
            );
        }
        return (
            <i
                className={classNames('roo-icon', 'addon-icon', `roo-icon-${suffixIcon || 'calendar-new'}`)}
                onMouseEnter={() => {
                    this.setState({
                        _closeShow: true,
                    });
                }}
            />
        );
    }

    _renderInput = ({
        inputClassName,
        firstInputValue,
    }: {
        inputClassName: string | undefined;
        firstInputValue: string;
    }) => {
        const { multiple, placeholder, editable, disabled, autoFocus, inputReadOnly, variant } = this.props;
        const { open, multipleValues } = this.state;
        const cStyle = {
            flexBasis: 0,
            minWidth: '20px',
        };
        const needHidePlaceHolder = multiple && multipleValues.length !== 0;
        return (
            <div className={this.getMultipleInputClassName()}>
                <div className="tags-box">
                    {this._renderMultiple()}
                    <Input
                        placeholder={
                            needHidePlaceHolder
                                ? undefined
                                : placeholder || (locale.lng('DatePicker.placeholder') as string)
                        }
                        inputRef={this.firstInputRef}
                        onFocus={() => {
                            this.inputFocus(0);
                        }}
                        type="text"
                        style={cStyle}
                        onBlur={this.inputBlur}
                        onChange={e => {
                            if (editable) {
                                if (!open) {
                                    this.setState({
                                        open: true,
                                    });
                                }
                                this.onInputChange(e.target.value);
                            }
                        }}
                        onKeyDown={this.onInputKeyDown}
                        disabled={disabled}
                        autoFocus={autoFocus}
                        className={inputClassName}
                        value={firstInputValue}
                        styleType={variant}
                        onClick={e => {
                            this.handleInputClick();
                            e.stopPropagation();
                        }}
                        readOnly={inputReadOnly}
                    />
                    {this.renderSuffixIcon()}
                </div>
            </div>
        );
    };

    render() {
        const {
            value,
            startDate,
            selectDate,
            selectEndDate,
            dateShow,
            monthShow,
            yearShow,
            open,
            inputValueTexts,
            inputCallbackDate,
            isInputStatus,
            inputWeekEndDate,
            inputWeekStartDate,
            isInputValid,
            format,
            focusIndex,
            multipleValues,
        } = this.state;

        const {
            showTime,
            placeholder,
            disabledDate,
            type,
            popupContainer,
            className,
            disabled,
            dateCellRender,
            inputClassName,
            size = this.context.size || 'normal',
            disabledTime,
            placement,
            flip,
            autoFocus,
            timeClassName,
            range,
            style,
            popupStyle,
            renderExtraFooter,
            panelRender,
            prevIcon,
            nextIcon,
            superPrevIcon,
            superNextIcon,
            separator,
            bordered,
            status,
            timeSteps,
            editable,
            popupClassName,
            inputReadOnly,
            use12Hours,
            variant,
            presets,
            valueRender,
            cellRender,
            headerCellRender,
            headerMonthCellRender,
            monthSortCellRender,
            multiple,
        } = this.props;
        const isShowTimeObject = isObject(timeSteps);
        let timeSelectProps = {};
        if (isShowTimeObject) {
            timeSelectProps = timeSteps;
        }
        let bodyPanel: React.ReactNode | null = null;
        const { dateFormat, timeFormat } = getFormat(format);
        let showTimeFormat;
        if (isObject(showTime) && showTime.format) {
            showTimeFormat = showTime.format;
        }
        let datePanelSelectDate = type !== 'week' ? [selectDate] : [selectDate, selectEndDate];
        if (isInputStatus && type === 'week') {
            datePanelSelectDate = [inputCallbackDate[0] || selectDate, inputWeekEndDate];
        }
        if (multiple) {
            datePanelSelectDate = isArray(selectDate) ? selectDate : [selectDate];
        }
        let dateSelectValue = startDate.format(dateFormat);
        let timeSelectValue = selectDate && !multiple && !range ? selectDate.format(timeFormat || 'HH:mm:ss') : '';
        if (isInputStatus) {
            dateSelectValue = inputCallbackDate[0]?.format(dateFormat);
            timeSelectValue = inputCallbackDate[0]?.format(showTimeFormat || timeFormat || 'HH:mm:ss');
        }

        //halfDayHelperTime 在use12Hours时对AM,PM进行高亮判断的辅助
        const halfDayHelperTime = (use12Hours && selectDate) || undefined;
        const datePicker = (
            <div>
                {showTime ? (
                    <div className={`${this.context.prefixCls}-datepicker-inputs`}>
                        <input
                            key={startDate ? `${startDate.date()}${startDate.month()}${startDate.year()}` : 'inout'}
                            type="text"
                            className={`${this.context.prefixCls}-input ${this.context.prefixCls}-input-inline ${this.context.prefixCls}-input-xs`}
                            onChange={() => {}}
                            value={dateSelectValue}
                        />
                        <Trigger
                            builtinPlacements={getPlacements(flip || false)}
                            popupPlacement={this.getPlacement()}
                            action={['click']}
                            popupStyle={{ whiteSpace: 'nowrap' }}
                            getPopupContainer={isFunction(popupContainer) ? popupContainer : () => popupContainer}
                            popup={
                                <TimePicker
                                    className={timeClassName}
                                    disabledTime={disabledTime}
                                    key={`datePicker-showtime-${focusIndex}`}
                                    value={selectDate}
                                    format={showTimeFormat || timeFormat || 'HH:mm:ss'}
                                    onChange={this.selectTime}
                                    use12Hours={use12Hours}
                                    isInputValid={isInputValid}
                                    inputValueArrTime={inputCallbackDate[0]}
                                    showTime={showTime}
                                    halfDayHelperTime={halfDayHelperTime}
                                    {...timeSelectProps}
                                />
                            }
                        >
                            <Input
                                key={`${selectDate?.format('YYYYMMDD HH:mm:ss')}`}
                                className={`${this.context.prefixCls}-input ${this.context.prefixCls}-input-inline ${this.context.prefixCls}-input-xs`}
                                value={timeSelectValue}
                            />
                        </Trigger>
                    </div>
                ) : null}
                <YearPanel
                    show={yearShow}
                    startDate={startDate}
                    selectDate={selectDate}
                    onSelectYear={this.selectMonthOrYear}
                    handleChangeDate={this.handleChangeDate}
                    disabledDate={disabledDate}
                    renderExtraFooter={renderExtraFooter}
                    prevIcon={prevIcon}
                    nextIcon={nextIcon}
                    cellRender={cellRender}
                    headerCellRender={headerCellRender}
                    multipleValues={multipleValues}
                    multiple={multiple}
                />
                <MonthPanel
                    show={monthShow}
                    startDate={startDate}
                    selectDate={selectDate}
                    onShowPanel={this.showPanel}
                    onSelectMonth={this.selectMonthOrYear}
                    handleChangeDate={this.handleChangeDate}
                    disabledDate={disabledDate}
                    renderExtraFooter={renderExtraFooter}
                    prevIcon={prevIcon}
                    nextIcon={nextIcon}
                    cellRender={cellRender}
                    headerCellRender={headerCellRender}
                    monthSortCellRender={monthSortCellRender}
                    multipleValues={multipleValues}
                    multiple={multiple}
                    presets={presets || {}}
                />
                <DatePanel
                    show={dateShow}
                    startDate={isInputStatus && type === 'week' ? inputWeekStartDate : startDate}
                    selectDate={datePanelSelectDate}
                    onShowPanel={this.showPanel}
                    onSelectDate={this.onDateSelected}
                    handleChangeDate={this.handleChangeDate}
                    disabledDate={disabledDate}
                    type={type}
                    dateCellRender={dateCellRender}
                    renderExtraFooter={renderExtraFooter}
                    prevIcon={prevIcon}
                    nextIcon={nextIcon}
                    superPrevIcon={superPrevIcon}
                    superNextIcon={superNextIcon}
                    isInputStatus={isInputStatus}
                    inputWeekEndDate={inputWeekEndDate}
                    inputCallbackDate={inputCallbackDate}
                    cellRender={cellRender}
                    headerCellRender={headerCellRender}
                    headerMonthCellRender={headerMonthCellRender}
                    presets={presets || {}}
                    multipleValues={multipleValues}
                    multiple={multiple}
                />
            </div>
        );

        const monthPicker = (
            <div>
                <MonthPanel
                    key={Math.random()}
                    show
                    startDate={startDate}
                    selectDate={selectDate}
                    onShowPanel={this.showPanel}
                    onSelectMonth={this.selectMonthOrYear}
                    handleChangeDate={this.handleChangeDate}
                    disabledDate={disabledDate}
                    dateType={type}
                    renderExtraFooter={renderExtraFooter}
                    prevIcon={prevIcon}
                    nextIcon={nextIcon}
                    inputCallbackDate={inputCallbackDate}
                    isInputStatus={isInputStatus}
                    isInputValid={isInputValid}
                    cellRender={cellRender}
                    headerCellRender={headerCellRender}
                    monthSortCellRender={monthSortCellRender}
                    presets={presets || {}}
                />
            </div>
        );

        const quarterPicker = (
            <div>
                <QuarterPanel
                    key={Math.random()}
                    show
                    startDate={startDate}
                    selectDate={selectDate}
                    onShowPanel={this.showPanel}
                    onSelectMonth={this.selectMonthOrYear}
                    handleChangeDate={this.handleChangeDate}
                    disabledDate={disabledDate}
                    dateType={type}
                    renderExtraFooter={renderExtraFooter}
                    prevIcon={prevIcon}
                    nextIcon={nextIcon}
                    inputCallbackDate={inputCallbackDate}
                    isInputStatus={isInputStatus}
                    isInputValid={isInputValid}
                    cellRender={cellRender}
                    headerCellRender={headerCellRender}
                />
            </div>
        );

        const halfYearPicker = (
            <div>
                <HalfYearPanel
                    key={Math.random()}
                    show
                    startDate={startDate}
                    selectDate={selectDate}
                    onShowPanel={this.showPanel}
                    onSelectMonth={this.selectMonthOrYear}
                    handleChangeDate={this.handleChangeDate}
                    disabledDate={disabledDate}
                    dateType={type}
                    renderExtraFooter={renderExtraFooter}
                    prevIcon={prevIcon}
                    nextIcon={nextIcon}
                    inputCallbackDate={inputCallbackDate}
                    isInputStatus={isInputStatus}
                    isInputValid={isInputValid}
                    cellRender={cellRender}
                    headerCellRender={headerCellRender}
                />
            </div>
        );

        const yearPicker = (
            <div>
                <YearPanel
                    key={Math.random()}
                    show
                    startDate={startDate}
                    selectDate={selectDate}
                    onSelectYear={this.selectMonthOrYear}
                    handleChangeDate={this.handleChangeDate}
                    disabledDate={disabledDate}
                    dateType={type}
                    renderExtraFooter={renderExtraFooter}
                    prevIcon={prevIcon}
                    nextIcon={nextIcon}
                    inputCallbackDate={inputCallbackDate}
                    isInputStatus={isInputStatus}
                    isInputValid={isInputValid}
                    cellRender={cellRender}
                    headerCellRender={headerCellRender}
                />
            </div>
        );

        switch (type) {
            case 'month':
                bodyPanel = monthPicker;
                break;
            case 'quarter':
                bodyPanel = quarterPicker;
                break;
            case 'half':
                bodyPanel = halfYearPicker;
                break;
            case 'year':
                bodyPanel = yearPicker;
                break;
            default:
                bodyPanel = datePicker;
                break;
        }

        if (isFunction(panelRender)) {
            bodyPanel = panelRender(bodyPanel);
        }
        let inputValue: string | string[] = '';
        if (type === 'week' && !isArray(value)) {
            // 使用 ISO 周标准显示年份和周数
            inputValue = value ? `${value.isoWeekYear()}-${value.isoWeek()} ${locale.lng('DatePicker.week')}` : '';
        } else if (type === 'quarter') {
            if (range && isArray(value)) {
                const st = value[0] ? `${value[0]!.year()}-Q${value[0]!.quarter()}` : '';
                const ed = value[1] ? `${value[1]!.year()}-Q${value[1]!.quarter()}` : '';
                inputValue = [st, ed];
            } else {
                inputValue = value ? `${value!.year()}-Q${value!.quarter()}` : '';
            }
        } else if (type === 'half') {
            if (range && isArray(value)) {
                const st = value[0] ? `${value[0]!.year()}-H${value[0]!.quarter() < 3 ? 1 : 2}` : '';
                const ed = value[1] ? `${value[1]!.year()}-H${value[1]!.quarter() < 3 ? 1 : 2}` : '';
                inputValue = [st, ed];
            } else {
                inputValue = value ? `${value!.year()}-H${value!.quarter() < 3 ? 1 : 2}` : '';
            }
        } else if (!multiple) {
            if (range && isArray(value)) {
                const st = value[0] ? value[0].format(format) : '';
                const ed = value[1] ? value[1].format(format) : '';
                inputValue = [st, ed];
            } else {
                inputValue = value ? value.format(format) : '';
            }
        }
        if (range && ['date', 'week'].includes(type)) {
            console.error('DatePicker 范围选择器不支持type为date week，需使用DatePicker.RangePicker');
        }

        let firstInputValue = range && isArray(inputValue) ? inputValue[0] : (inputValue as string);
        let secondInputValue = range && isArray(inputValue) ? inputValue[1] : (inputValue as string);
        if (isFunction(valueRender)) {
            const [valueOne, valueTwo] = valueRender(value);
            firstInputValue = valueOne;
            secondInputValue = valueTwo;
        }
        if (isInputStatus) {
            firstInputValue = inputValueTexts[0];
            if (range) {
                secondInputValue = inputValueTexts[1];
            }
        }
        const baseCls = `${this.context.prefixCls}-input-group`;
        let groupCls =
            multiple || range
                ? classNames(`${baseCls} has-icon`, {
                      [`${baseCls}-disabled`]: disabled,
                      [`${baseCls}-outline`]: bordered,
                      [`${baseCls}-no-outline`]: !bordered,
                      [`${baseCls}-${size}`]: size && size !== 'normal',
                      [`${baseCls}-outline-${status}`]: status,
                      [`${baseCls}-outline-${variant}`]: variant,
                      [`${this.context.prefixCls}-input-tag`]: multiple,
                  })
                : `${this.context.prefixCls}-input-wrap`;
        if (multiple) {
            groupCls = classNames(
                `${this.context.prefixCls}-input-group`,
                'has-icon',
                { [`${this.context.prefixCls}-input-group-${size}`]: size && size !== 'normal' },
                { 'has-error': status === 'error' },
                { 'has-success-color': status === 'success' },
            );
        }
        return (
            <Trigger
                builtinPlacements={getPlacements(flip)}
                popupPlacement={this.getPlacement()}
                onPopupVisibleChange={this.popupVisibleChange}
                popupVisible={disabled ? false : !!open}
                getPopupContainer={isFunction(popupContainer) ? popupContainer : () => popupContainer}
                action={['click']}
                popupClassName={popupClassName}
                popup={
                    <div
                        className={`${this.context.prefixCls}-datepicker ${className}`}
                        style={popupStyle}
                        ref={this.rootNode}
                    >
                        {bodyPanel}
                    </div>
                }
            >
                <DayjsLocalProvider>
                    <div
                        className={groupCls}
                        style={style}
                        ref={this.inputNode}
                    >
                        {multiple ? (
                            this._renderInput({ inputClassName, firstInputValue })
                        ) : (
                            <Input
                                bordered={!range && bordered}
                                inputStatus={!range ? status : undefined}
                                placeholder={placeholder || (locale.lng('DatePicker.placeholder') as string)}
                                inputRef={this.firstInputRef}
                                onFocus={() => {
                                    this.inputFocus(0);
                                }}
                                onBlur={this.inputBlur}
                                onChange={e => {
                                    if (editable) {
                                        if (!open) {
                                            this.setState({
                                                open: true,
                                            });
                                        }
                                        this.onInputChange(e.target.value);
                                    }
                                }}
                                onKeyDown={this.onInputKeyDown}
                                disabled={disabled}
                                autoFocus={autoFocus}
                                size={size}
                                className={inputClassName}
                                suffix={!range && this.renderClear()}
                                value={firstInputValue}
                                onClick={e => {
                                    this.handleInputClick();
                                    e.stopPropagation();
                                }}
                                readOnly={inputReadOnly}
                                styleType={variant}
                            />
                        )}
                        {!multiple && range && (
                            <>
                                {separator || <span className="input-separator">-</span>}
                                <Input
                                    placeholder={placeholder || (locale.lng('DatePicker.placeholder') as string)}
                                    inputRef={this.secondInputRef}
                                    onFocus={() => {
                                        this.inputFocus(1);
                                    }}
                                    onBlur={this.inputBlur}
                                    disabled={disabled}
                                    size={size}
                                    className={inputClassName}
                                    value={secondInputValue}
                                    onChange={e => {
                                        if (editable) {
                                            if (!open) {
                                                this.setState({
                                                    open: true,
                                                });
                                            }
                                            this.onInputChange(e.target.value);
                                        }
                                    }}
                                    onKeyDown={this.onInputKeyDown}
                                    onClick={e => {
                                        this.handleInputClick();
                                        e.stopPropagation();
                                    }}
                                    readOnly={inputReadOnly}
                                    styleType={variant}
                                />
                            </>
                        )}
                        {!multiple && range && this.renderClear()}
                    </div>
                </DayjsLocalProvider>
            </Trigger>
        );
    }
}

polyfill(DatePicker);

export default withDisabled<typeof DatePicker>(DatePicker);
