import * as React from 'react';
import * as PropTypes from 'prop-types';
import { isFunction } from '@utiljs/is';
import Popover from '@roo/roo/core/Popover';

import DropMenuItem from './DropMenuItem';
import DropMenuDivider from './DropMenuDivider';
import DropMenuList from './DropMenuList';
import { DropMenuProps, DropMenuItemProps } from './interface';
import { innerPlacement } from '../_utils/types';

class DropMenu extends React.PureComponent<DropMenuProps, any> {
    static propTypes = {
        menu: PropTypes.arrayOf(PropTypes.oneOfType([
            PropTypes.shape({
                label: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
                link: PropTypes.string,
            }),
            PropTypes.string,
        ])),
        placement: PropTypes.oneOf(innerPlacement),
        trigger: PropTypes.oneOf(['hover', 'click']),
        flip: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
        onItemClick: PropTypes.func,
        onClickOutside: PropTypes.func,
        className: PropTypes.string,
        popupClassName: PropTypes.string,
        popupWrapClassName: PropTypes.string,
        popupContainer: PropTypes.func,
        isSubMenu: PropTypes.bool,
        closeMotion: PropTypes.bool,
        arrow: PropTypes.bool,
        autoFocus: PropTypes.bool,
        destroyOnHidden: PropTypes.bool,
    }

    static defaultProps = {
        trigger: 'hover',
        placement: 'bottomLeft',
        menu: null,
        className: null,
        popupClassName: null,
        popupWrapClassName: null,
        onItemClick: undefined,
        onClickOutside: undefined,
        flip: true,
        popupContainer: undefined,
        isSubMenu: false,
        arrow: false,
        closeMotion: false,
        autoFocus: false,
        destroyOnHidden: true,
    }

    static Item = DropMenuItem;

    static List = DropMenuList;

    static Divider = DropMenuDivider;

    private firstItemRef = React.createRef<DropMenuItem>();

    constructor(props: DropMenuProps) {
        super(props);
        this.state = {
            dropMenuArrowPlacement: props.placement,
            focusedIndex: this.getFirstEnabledIndex(props.menu, !!props.autoFocus)
        };
    }

    getFirstEnabledIndex(menu: (DropMenuItemProps | string)[], autoFocus: boolean): number {
        if (!autoFocus || !Array.isArray(menu)) return -1;
        for (let i = 0; i < menu.length; i++) {
            const item = menu[i];
            if (item !== '' && !(typeof item === 'object' && item.disabled)) {
                return i;
            }
        }
        return -1;
    }

    componentDidUpdate(prevProps: DropMenuProps) {
        // 当菜单从隐藏变为显示且启用了autoFocus时，聚焦第一个未禁用的菜单项
        if (!prevProps.visible && this.props.visible && this.props.autoFocus) {
            const focusedIndex = this.getFirstEnabledIndex(this.props.menu, !!this.props.autoFocus);
            this.setState({ focusedIndex }, () => {
                setTimeout(() => {
                    if (this.firstItemRef.current) {
                        this.firstItemRef.current.focus();
                    }
                }, 0);
            });
        }
        // 当菜单隐藏时，重置聚焦状态
        if (prevProps.visible && !this.props.visible) {
            this.setState({ focusedIndex: -1 });
        }
    }

    handleMenuItemClick = (item: DropMenuItemProps, event: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
        const {
            onItemClick,
        } = this.props;

        if (isFunction(onItemClick)) {
            onItemClick(item, event);
        }
    }

    renderMenu(menuData: (DropMenuItemProps | string)[] | void): JSX.Element | null {
        const {
            style,
            className,
            arrow,
            placement
        } = this.props;
        const { dropMenuArrowPlacement, focusedIndex } = this.state;
        if (!menuData) {
            return null;
        }
        return (
            <DropMenuList
                style={style}
                className={className}
                arrow={arrow}
                placement={placement}
                dropMenuArrowPlacement={dropMenuArrowPlacement}
            >
                {
                    menuData?.map?.((item, index) => {
                        const isFocused = index === focusedIndex;
                        const refProp = isFocused ? this.firstItemRef : null;
                        if (item === '') return <DropMenu.Divider key={index} />;
                        if (typeof item === 'string') {
                            return (
                                <DropMenuItem
                                    label={item}
                                    key={item}
                                    onMenuClick={this.handleMenuItemClick}
                                    focused={isFocused}
                                    ref={refProp}
                                />
                            );
                        }
                        return (
                            <DropMenuItem
                                {...item}
                                key={index}
                                onMenuClick={this.handleMenuItemClick}
                                focused={isFocused}
                                ref={refProp}
                            />
                        );
                    })
                }
            </DropMenuList>
        );
    }

    render() {
        const {
            children,
            trigger,
            visible,
            defaultVisible,
            placement,
            onVisibleChange,
            menu,
            menuComponent,
            popupContainer,
            popupClassName,
            popupWrapClassName,
            flip,
            closeMotion,
            onClickOutside,
            disabled,
            arrow,
            destroyOnHidden
        } = this.props;

        const { dropMenuArrowPlacement } = this.state;

        return (
            <Popover
                visible={disabled ? false : visible}
                container={popupContainer}
                defaultVisible={defaultVisible}
                trigger={trigger}
                placement={placement}
                display="inline-block"
                flip={flip}
                onClickOutside={onClickOutside}
                onVisibleChange={onVisibleChange}
                className={popupClassName}
                popupWrapClassName={popupWrapClassName}
                content={menuComponent || this.renderMenu(menu)}
                closeMotion={closeMotion}
                lazy={!destroyOnHidden}
                onPlacementUpdate={place => {
                    if (arrow) {
                        if (place !== placement && dropMenuArrowPlacement !== place) {
                            setTimeout(() => {
                                this.setState({
                                    dropMenuArrowPlacement: place
                                });
                            }, 0);
                        }
                        if (place === placement && dropMenuArrowPlacement !== placement) {
                            setTimeout(() => {
                                this.setState({
                                    dropMenuArrowPlacement: placement
                                });
                            }, 0);
                        }
                    }
                }}
            >
                {
                    children
                }
            </Popover>
        );
    }
}

export default DropMenu;
