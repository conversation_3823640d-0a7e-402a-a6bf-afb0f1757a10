import * as React from 'react';
import * as PropTypes from 'prop-types';
import isEqual from 'lodash/isEqual';
import uniqBy from 'lodash/uniqBy';
import { cloneLoop } from '@utiljs/clone';
import classNames from 'classnames';
import { hasClass } from '@utiljs/dom';
import Input from '@roo/roo/Input';
import { isFunction } from '@utiljs/is';
import { GlobalConfigContext } from '../ConfigProvider';
import { $danger } from '../_utils/ThemeColor';
import {
    TreeProps,
    TreeState,
    TreeData,
    ClickOtherParams,
    Data,
} from './interface';

import TreeNode from './TreeNode';

const ExpandedChangeIdMap: Record<string, any> = {};

class Tree extends React.Component<TreeProps, TreeState> {
    static contextType?: React.Context<any> | undefined = GlobalConfigContext;

    static defaultProps = {
        data: [],
        nodeKey: 'id',
        nodeLabel: 'label',
        nodeChildren: 'children',
        showCheckbox: false,
        selectedKey: '',
        selectedKeys: [],
        lazy: false,
        defaultExpandAll: false,
        defaultExpandedKeys: undefined,
        lazyLoad: undefined,
        disabled: false,
        multiple: false,
        checkStrictly: true,
        expandAction: 'click',
        autoExpandParent: false,
        nodeRender: undefined,
        onNodeClick: undefined,
        onNodeExpand: undefined,
        onNodeCollapse: undefined,
        onCheckChange: undefined,
    }

    static propTypes = {
        nodeKey: PropTypes.string,
        nodeLabel: PropTypes.string,
        nodeChildren: PropTypes.string,
        data: PropTypes.array,
        showCheckbox: PropTypes.bool,
        selectedKey: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        selectedKeys: PropTypes.array,
        lazy: PropTypes.bool,
        defaultExpandAll: PropTypes.bool,
        defaultExpandedKeys: PropTypes.array,
        lazyLoad: PropTypes.func,
        disabled: PropTypes.bool,
        multiple: PropTypes.bool,
        checkStrictly: PropTypes.bool,
        autoExpandParent: PropTypes.bool,
        expandAction: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),
        nodeRender: PropTypes.func,
        onNodeClick: PropTypes.func,
        onNodeExpand: PropTypes.func,
        onNodeCollapse: PropTypes.func,
        onCheckChange: PropTypes.func,
    }

    // 缓存 props 选中的节点，用于改变父节点的 checked 状态
    tempDefaultCheckedNode: TreeData[] = []

    // 缓存 nodeKey 的值
    nodeKey = this.props.nodeKey || 'id';

    // iconNodeRef: React.RefObject<HTMLDivElement> = React.createRef();

    // 使用ref跟踪是否需要重建树，避免无限循环
    isRebuildingTree = false;

    constructor(props: TreeProps) {
        super(props);

        const {
            data, selectedKeys = [],
        } = this.props;
        this.state = {
            copyState: cloneLoop(data),
            selectedKeys,
            stateTree: [],
            flatState: [],
            searchValue: '',
            direction: 'top',
            startDragNodeKey: '',
            dragingNodeKey: '',
            startDragNode: {} as TreeData,
            selectedKeysNode: [],
        };
    }

    static getDerivedStateFromProps(nextProps: TreeProps, prevState: TreeState) {
        // 检查重要属性是否变化
        const isDataChange = !isEqual(nextProps.data, prevState.copyState);
        const isSelectedKeysChange = !isEqual(nextProps.selectedKeys, prevState.selectedKeys);
        const isSelectedKeyChange = nextProps.selectedKey !== prevState._prevSelectedKey;
        const isAutoExpandParentChange = nextProps.autoExpandParent !== prevState._prevAutoExpandParent;

        if (isDataChange || isSelectedKeysChange || isSelectedKeyChange || isAutoExpandParentChange) {
            return {
                copyState: cloneLoop(nextProps.data),
                flatState: [], // 清空flatState，让它在下一次更新中重建
                selectedKeys: nextProps.selectedKeys || [],
                _prevSelectedKey: nextProps.selectedKey,
                _prevAutoExpandParent: nextProps.autoExpandParent,
                needRebuild: true,
            };
        }
        return null;
    }
    
    componentDidUpdate(prevProps: TreeProps) {
        // 如果需要重建树且没有正在重建
        if (this.state.needRebuild && !this.isRebuildingTree) {
            // 设置标志，防止多次重建
            this.isRebuildingTree = true;

            // 如果selectedKeys改变了，或者数据改变了，则重置选中节点缓存
            if (
                !isEqual(prevProps.selectedKeys, this.props.selectedKeys) ||
                !isEqual(prevProps.data, this.props.data)
            ) {
                this.tempDefaultCheckedNode = [];
            }

            // 初始化树状态
            this.initStateTree(this.state.copyState);

            // 重置标志
            this.setState({ needRebuild: false }, () => {
                this.isRebuildingTree = false;
            });
        }
    }

    componentDidMount() {
        this.initStateTree();
    }

    getNodeChildren = (item: TreeData) => (item?.[this.props.nodeChildren || 'children'] ?? []) as TreeData[]

    getNodeKey = (item: TreeData) => item?.[this.nodeKey]

    getNodeLabel = (item: TreeData) => item[this.props.nodeLabel || 'label'] ?? '';

    initStateTree = (treeData?: Data[]) => {
        const { data } = this.props;
        const cData = cloneLoop(treeData || data);
        this.factorialNode(cData as TreeData[]);

        this.updateParentChecked();
        this.updateState(cData as TreeData[]);
    }

    updateParentChecked = () => {
        this.tempDefaultCheckedNode.forEach(item => {
            this.setCheckedRelationNode(item, true);
        });
    }

    isExpanded = (item: TreeData) => {
        const {
            defaultExpandAll,
            defaultExpandedKeys,
            expandedKeys,
            autoExpandParent,
        } = this.props;
        if (expandedKeys) {
            return expandedKeys.indexOf(this.getNodeKey(item)) > -1;
        }
        const hasChild = this.getNodeChildren(item)?.length;
        const needExpandNode = defaultExpandedKeys && defaultExpandedKeys.length && (defaultExpandedKeys as any[]).indexOf(this.getNodeKey(item)) > -1;
        let needSearchNode = false;
        if (this.state.searchValue && hasChild) {
            needSearchNode = this.getNodeChildren(item).some((child: TreeData) => this.getNodeLabel(child).indexOf(this.state.searchValue) > -1);
        }

        if (hasChild) {
            // needExpandNode be superior to defaultExpandAll
            if (
                ((needExpandNode || defaultExpandAll) && (!(item.ExpandedIsChange || ExpandedChangeIdMap[this.getNodeKey(item)]?.ExpandedIsChange)))
                || needSearchNode
                || ExpandedChangeIdMap[this.getNodeKey(item)]?.isExpanded
                || autoExpandParent
            ) {
                return true;
            }
        }
        return false;
    }

    isDisabled = (item: TreeData) => {
        const { disabled, parentDisabled } = this.props;
        if (item.disabled) {
            return !!item.disabled;
        }
        if (disabled) {
            return disabled;
        }
        if (parentDisabled) {
            return !!(this.getNodeChildren(item)?.length);
        }
        return false;
    }

    // factorial node
    factorialNode = (node?: TreeData[], parent?: TreeData) => {
        const {
            showCheckbox,
            lazy,
            selectedKey,
            selectedKeys = [],
            disabled,
            multiple,
            checkStrictly,
            controlled,
        } = this.props;

        if (node && node.length) {
            // 创建一个Set来存储选中的key，将所有值转换为字符串以避免类型问题
            const selectedKeySet = new Set(selectedKeys.map(key => String(key)));

            // 在受控模式下计算父节点的半选状态
            const calculateParentStatus = (children: TreeData[]): { checkedCount: number; hasHalfChecked: boolean } => {
                let checkedCount = 0;
                let hasHalfChecked = false;

                children.forEach(child => {
                    if (selectedKeySet.has(String(this.getNodeKey(child)))) {
                        checkedCount += 1;
                    }
                    // 如果子节点有子节点，递归计算
                    if (this.getNodeChildren(child)?.length) {
                        const childStatus = calculateParentStatus(this.getNodeChildren(child));
                        if (
                            childStatus.hasHalfChecked ||
                            (childStatus.checkedCount > 0 &&
                                childStatus.checkedCount < this.getNodeChildren(child).length)
                        ) {
                            hasHalfChecked = true;
                        }
                        if (childStatus.checkedCount === this.getNodeChildren(child).length) {
                            checkedCount += 1;
                        } else if (childStatus.checkedCount > 0) {
                            hasHalfChecked = true;
                        }
                    }
                });

                return { checkedCount, hasHalfChecked };
            };

            node.forEach((item: TreeData, index: number) => {
                if (!item) return; // 过滤异常item
                const hasChild = this.getNodeChildren(item)?.length;
                const isExpanded = item.isExpanded || this.isExpanded(item);

                let isChecked = false;
                let childCheckedStatus = 2; // 默认为未选中状态

                if (controlled) {
                    isChecked = selectedKeySet.has(String(this.getNodeKey(item)));
                    if (hasChild) {
                        const childStatus = calculateParentStatus(this.getNodeChildren(item));
                        if (childStatus.checkedCount === this.getNodeChildren(item).length) {
                            isChecked = true;
                            childCheckedStatus = 1;
                        } else if (childStatus.checkedCount > 0 || childStatus.hasHalfChecked) {
                            childCheckedStatus = 3; // 半选状态
                        }
                    }
                } else {
                    isChecked =
                        selectedKeySet.has(String(this.getNodeKey(item))) ||
                        !!(parent && parent.isChecked && checkStrictly);
                }

                // set extra props
                item.level = parent ? (parent?.level as number) + 1 : 0;
                item.hasChild = !!hasChild;
                item.hasCheckbox = showCheckbox || false;
                item.isRoot = !parent;
                item.isDisabled = this.isDisabled(item);
                item.isLastNode = !hasChild;
                item.isLeaf = !!(lazy && !item.hasChild && item.isLeaf);
                item.isExpanded = isExpanded;
                item.parent = parent;
                item.spckey = parent ? `${parent.spckey}_${index}` : `${index}`;
                if (!item.isDisabled) {
                    item.isChecked = isChecked;
                    item.childCheckedStatus = childCheckedStatus;
                }
                // item.isChecked = selectedKeys.indexOf(item[nodeKey]) >= 0 || (!!(parent && parent.isChecked));
                item.isSelected = !multiple ? selectedKey === this.getNodeKey(item) : selectedKeySet.has(String(this.getNodeKey(item)));
                if (selectedKeySet.has(String(this.getNodeKey(item)))) {
                    this.tempDefaultCheckedNode.push(item);
                }

                // fill flatstate
                this.construcFlatState(item);

                // factorial init extra props
                if (hasChild) {
                    this.factorialNode(this.getNodeChildren(item), item);
                }
            });
        }
    }

    // flat data
    construcFlatState = (node: TreeData) => {
        const { flatState } = this.state;
        // 这里需要根据nodeKey去重
        // if (!this.getNodeKey(node) || !flatState.find((item => this.getNodeKey(item) === this.getNodeKey(node)))) {
        //     flatState.push(node);
        // }
        const flatStateSet = new Set(flatState.map(item => this.getNodeKey(item)));

        if (!flatStateSet.has(this.getNodeKey(node))) {
            flatState.push(node);
        }
    }

    // common update relation nodes fn to isChecked and childCheckedStatus
    updateRelationNodes = (selectedStatus: boolean, selectedNode: TreeData, needUpdate?: boolean) => {
        const { controlled } = this.props;
        // 在受控模式下，不更新节点关系
        if (!controlled) {
            // 始终执行节点状态更新，确保所有节点状态正确
            this.setCheckedRelationNode(selectedNode, selectedStatus);
        }

        // 此方法不再需要调用updateState，因为setCheckedRelationNode已经调用了
    };

    // start update status
    setCheckedRelationNode = (node: TreeData, checked: boolean) => {
        const { checkStrictly, controlled } = this.props;

        if (!controlled) {
            // set current node status
            this.setCurrNodeStatus(node, checked);

            // checkStrictly为false，则父子不进行关联
            if (checkStrictly) {
                // up set node status
                const { parent } = node;
                if (parent) {
                    this.setParentNodeStatus(parent, checked);
                }
                // down set node status
                if (this.getNodeChildren(node)) {
                    this.setChildNodeStatus(this.getNodeChildren(node), checked);
                }
            }
        }

        // 无论是否受控，都更新整个树状态，确保flatState与实际节点状态同步
        this.updateState(this.state.stateTree);
    }

    // update current status
    setCurrNodeStatus = (node: TreeData, checked: boolean) => {
        if (node.isDisabled) {
            return; // 如果当前节点是 disabled，不做任何改变
        }

        node.isChecked = checked;
        
        // 获取所有子节点
        const children = this.getNodeChildren(node) || [];
        // 计算选中状态，只关注 isChecked
        const checkedChildren = children.filter(child => child.isChecked);
        const hasHalfCheckedChildren = children.some(child => child.childCheckedStatus === 3);
        
        if (children.length === 0) {
            // 如果没有子节点，设置为普通选中状态
            node.childCheckedStatus = checked ? 1 : 2;
        } else if (checkedChildren.length === children.length) {
            // 所有子节点都选中
            node.childCheckedStatus = 1;
        } else if (checkedChildren.length === 0 && !hasHalfCheckedChildren) {
            // 没有子节点选中且没有半选状态
            node.childCheckedStatus = 2;
        } else {
            // 部分选中或有半选状态
            node.childCheckedStatus = 3;
        }
    }

    // update child status
    setChildNodeStatus = (child: TreeData[], checked: boolean) => {
        if (child && child.length) {
            child.forEach(ele => {
                // 如果节点是 disabled，直接跳过，不改变其状态也不向下传导
                if (ele.isDisabled) {
                    return;
                }
                ele.childCheckedStatus = 2;
                ele.isChecked = checked;
                if (this.getNodeChildren(ele)) {
                    this.setChildNodeStatus(this.getNodeChildren(ele), checked);
                }
            });
        }
    }

    // update parent status
    setParentNodeStatus = (parent: TreeData, checked: boolean) => {
        if (!parent) return;

        // 获取所有子节点
        const children = this.getNodeChildren(parent);
        if (!children || children.length === 0) return;

        // 计算选中状态，只关注 isChecked
        const checkedChildren = children.filter(child => child.isChecked);
        const hasHalfCheckedChildren = children.some(child => child.childCheckedStatus === 3);
        
        if (checkedChildren.length === children.length) {
            // 所有子节点都选中
            parent.childCheckedStatus = 1;
            parent.isChecked = true;
        } else if (checkedChildren.length === 0 && !hasHalfCheckedChildren) {
            // 没有子节点选中且没有半选状态
            parent.childCheckedStatus = 2;
            parent.isChecked = false;
        } else {
            // 部分选中或有半选状态
            parent.childCheckedStatus = 3;
            parent.isChecked = false;
        }

        // 继续向上传导
        if (parent.parent) {
            this.setParentNodeStatus(parent.parent, checked);
        }
    }

    // control half selected
    ctrHalfStatus = (flag: boolean[], node: TreeData, checked: boolean) => {
        if (flag.length === this.getNodeChildren(node)?.length) {
            node.childCheckedStatus = 1;
            node.isChecked = true;
        } else if (checked || (flag.length > 0 && flag.length < this.getNodeChildren(node)?.length)) {
            node.childCheckedStatus = 3;
            node.isChecked = false;
        } else {
            node.childCheckedStatus = 2;
            node.isChecked = false;
        }
    }

    // get selected node
    getCheckedNodes = () => {
        const { controlled, nodeKey = 'id' } = this.props;
        const { flatState, selectedKeysNode = [] } = this.state;
        if (controlled) {
            return uniqBy(selectedKeysNode, nodeKey);
        } else {
            const aNodes = flatState.filter((node: TreeData) => node.isChecked);
            return aNodes;
        }
    }

    // set selected node
    setCheckedNodes = (selectNodes: string[] | number[]) => {
        const { flatState } = this.state;
        const aNodes = flatState.filter((node: TreeData) => (selectNodes as any[]).indexOf(this.getNodeKey(node)) > -1);

        aNodes.forEach(node => {
            this.updateRelationNodes(true, node, true);
        });
    }

    setNotCheckedNodes = (selectNodes: Array<string | number>) => {
        const { flatState } = this.state;
        const aNodes = flatState.filter((node: TreeData) => (selectNodes as any[]).indexOf(this.getNodeKey(node)) > -1);

        aNodes.forEach(node => {
            this.updateRelationNodes(false, node, true);
        });
    }

    // clear selected
    clearCheckedNodes = () => {
        const aNodes = this.getCheckedNodes();

        aNodes.forEach((node: TreeData) => {
            this.updateRelationNodes(false, node, true);
        });
    }

    // selected all
    checkAll = () => {
        const { stateTree } = this.state;

        stateTree.forEach((node: TreeData) => {
            this.updateRelationNodes(true, node, true);
        });
    }

    // clear all
    clearAll = () => {
        this.clearCheckedNodes();
    }

    // 递归选择父节点
    getParentCheckNode = (parent: TreeData, selectedKeysNodeList?: TreeData[]) => {
        const _selectedKeysNodeList: TreeData[] = selectedKeysNodeList || [];
        
        // 获取所有子节点，不再过滤 disabled
        const children = this.getNodeChildren(parent) || [];
        
        // 检查所有子节点是否都在选中列表中
        const isAllChecked = children.length > 0 && children.every(item2 => _selectedKeysNodeList?.some(item1 => this.getNodeKey(item1) === this.getNodeKey(item2)));

        // 如果所有子节点都在选中列表中，且父节点不是 disabled，则添加父节点
        if (isAllChecked && !parent.isDisabled) {
            _selectedKeysNodeList.push(parent);
        }
        
        if (parent.parent) {
            this.getParentCheckNode(parent.parent, _selectedKeysNodeList);
        }
        
        return _selectedKeysNodeList;
    }

    // 递归选择子节点
    getChildCheckNode = (child: TreeData | TreeData[], selectedKeysNodeList?: TreeData[]) => {
        const _selectedKeysNodeList: TreeData[] = selectedKeysNodeList || [];
        const childrenToProcess = Array.isArray(child) ? child : [child];
        
        childrenToProcess.forEach((item: TreeData) => {
            // 只处理非 disabled 节点，因为 disabled 节点不能改变状态
            if (!item.isDisabled) {
                _selectedKeysNodeList.push(item);
                if (this.getNodeChildren(item)?.length) {
                    this.getChildCheckNode(this.getNodeChildren(item), _selectedKeysNodeList);
                }
            }
        });
        
        return _selectedKeysNodeList;
    }

    // 递归取消父节点
    cancelParentCheckNode = (parent: TreeData, selectedKeysNodeList?: TreeData[]) => {
        const _selectedKeysNodeList: TreeData[] = selectedKeysNodeList || [];
        const index = _selectedKeysNodeList.findIndex(n => this.getNodeKey(parent) === this.getNodeKey(n));
        if (index !== -1) _selectedKeysNodeList.splice(index, 1);
        // 判断当前节点的父节点，也需要去掉
        if (parent.parent) {
            this.cancelParentCheckNode(parent.parent, _selectedKeysNodeList);
        }
        return _selectedKeysNodeList;
    }

    // 递归取消子节点
    cancelChildCheckNode = (child: TreeData | TreeData[], selectedKeysNodeList?: TreeData[]) => {
        const _selectedKeysNodeList: TreeData[] = selectedKeysNodeList || [];
        // 判断当前节点的子节点，也需要去掉
        const childrenToProcess = Array.isArray(child) ? child : [child];
        childrenToProcess.forEach(item => {
            const index = _selectedKeysNodeList.findIndex(n => this.getNodeKey(item) === this.getNodeKey(n));
            if (index !== -1) _selectedKeysNodeList.splice(index, 1);
            if (this.getNodeChildren(item)?.length) {
                this.cancelChildCheckNode(this.getNodeChildren(item), _selectedKeysNodeList);
            }
        });
        return _selectedKeysNodeList;
    }

    // handle checkbox change
    handleCheckChange = (evt: any, node: TreeData, isNodeChecked?: boolean) => {
        const _evt = evt.currentTarget;
        const { blockNode, data, controlled } = this.props;
        const isChecked = blockNode ? isNodeChecked || evt.target.checked : evt.target.checked;
        const { nodeKey = 'id', onCheckChange, checkStrictly } = this.props;
        const { selectedKeysNode = [], selectedKeys, flatState } = this.state;
        let needUpdate = true;

        // 在受控模式下，不直接修改节点状态
        if (controlled) {
            if (isChecked) {
                if (isFunction(onCheckChange) && !selectedKeys?.includes(this.getNodeKey(node))) {
                    needUpdate = false;
                }
            }
        } else {
            node.isChecked = isChecked;
        }
        
        // 基于selectedKeys构建selectedKeysNode
        let selectedKeysNodeList: TreeData[] = [...(selectedKeysNode || [])];
        
        if (selectedKeys?.length) {
            // 默认选择的节点
            const defaultSelectNode = flatState.filter(item => selectedKeys.includes(this.getNodeKey(item))) || [];
            defaultSelectNode.forEach(item => {
                if (!selectedKeysNodeList.some(n => this.getNodeKey(n) === this.getNodeKey(item))) {
                    selectedKeysNodeList.push(item);
                }
            });
        }

        selectedKeysNodeList = uniqBy(selectedKeysNodeList, nodeKey);
        const hasChecked = selectedKeysNodeList.some(item => this.getNodeKey(item) === this.getNodeKey(node));
        // 如果没有选中此节点
        if (checkStrictly) { // 父子关联的情况下
            if (!hasChecked) {
                // 如果是叶子节点
                if (!node.hasChild) {
                    selectedKeysNodeList.push(node);
                }
                // 如果当前节点有孩子，则也需要选中
                if (this.getNodeChildren(node)?.length) {
                    selectedKeysNodeList = this.getChildCheckNode(node, selectedKeysNodeList);
                }
                // 如果当前节点的子都选中，则父亲也要选中
                if (node.parent) {
                    selectedKeysNodeList = this.getParentCheckNode(node.parent, selectedKeysNodeList);
                }
            } else {
                // 如果是叶子节点
                if (!node.hasChild) {
                    // 如果已经选择了当前节点，则去掉此节点，叶子节点
                    selectedKeysNodeList = selectedKeysNodeList.filter(item => this.getNodeKey(item) !== this.getNodeKey(node));
                }
                // 如果当前节点有孩子，则也去掉
                if (this.getNodeChildren(node)?.length) {
                    selectedKeysNodeList = this.cancelChildCheckNode(node, selectedKeysNodeList);
                }
                if (node.parent) {
                    // 如果当前节点有父亲，则也去掉
                    selectedKeysNodeList = this.cancelParentCheckNode(node.parent, selectedKeysNodeList);
                }
            }
        } else {
            if (!hasChecked) {
                selectedKeysNodeList.push(node);
            } else {
                selectedKeysNodeList = selectedKeysNodeList.filter(item => this.getNodeKey(item) !== this.getNodeKey(node));
            }
        }
        // 去重并确保每个节点的isChecked为true
        selectedKeysNodeList = uniqBy(selectedKeysNodeList, nodeKey);
        selectedKeysNodeList = selectedKeysNodeList.map(item => ({
            ...item,
            isChecked: true,
        }));

        // 设置当前选中节点状态
        this.setState({
            selectedKeysNode: selectedKeysNodeList,
        }, () => {
            // 在回调中处理节点关系更新和父子节点选中状态同步
            this.updateRelationNodes(isChecked, node, needUpdate);
            if (this.props.lazy && node.isLeaf && this.props.showCheckbox) {
                const otherParams = {
                    evtName: 'node-label',
                };
                this.execLazyLoad(_evt, node, otherParams);
            }
            
            const checkedKeys = this.getCheckedNodes().map(item => this.getNodeKey(item));
            if (onCheckChange) {
                onCheckChange(cloneLoop({
                    id: this.getNodeKey(node),
                    label: this.getNodeLabel(node),
                    isChecked,
                }), this.getCheckedNodes(), checkedKeys);
            }
        });
    }

    getSelectNodes = () => {
        const { flatState } = this.state;
        const aNodes = flatState.filter((node: TreeData) => node.isSelected);
        return aNodes;
    }

    getSelectCurNode = (id: any) => {
        const { flatState } = this.state;
        const aNodes = flatState.filter((node: TreeData) => this.getNodeKey(node) === id);
        return aNodes;
    }

    // handle plan click
    handleClick = (evt: any, item: TreeData, otherParams?: ClickOtherParams) => {
        const evtNode = evt.currentTarget.parentNode.parentNode;
        const _evt = evt.currentTarget;
        const {
            onNodeClick,
            lazy,
            showCheckbox,
            expandedKeys,
            multiple,
        } = this.props;
        
        const { evtName, isExpend } = otherParams || {};

        // isLeaf means lazy load Data
        if (lazy && item.isLeaf) {
            this.execLazyLoad(_evt, item, otherParams);
        }

        // 如果点击了带有复选框的叶子节点或者是单选模式下的节点标签，都不执行展开收起逻辑
        if (evtName === 'node-label') {
            // onNodeClick hook
            if (onNodeClick) {
                // disabled node dont have click event
                if (!this.isDisabled(item)) {
                    item.isSelected = !item.isSelected;
                    if (multiple && !showCheckbox) {
                        onNodeClick(cloneLoop({
                            id: this.getNodeKey(item),
                            label: this.getNodeLabel(item),
                            isSelected: item.isSelected,
                        }), this.getSelectNodes());
                    } else {
                        onNodeClick(cloneLoop({
                            id: this.getNodeKey(item),
                            label: this.getNodeLabel(item),
                            isSelected: item.isSelected,
                        }), this.getSelectCurNode(this.getNodeKey(item)));
                    }
                }
            }
        } else {
            // 点击前面的图标，仅执行展开收起
            const hasClassExpand = isExpend && hasClass(evtNode, 'is-expanded');
            if (!expandedKeys) {
                if (!hasClassExpand || (multiple && evtName === 'node-label')) {
                    item.isExpanded = true;
                    item.ExpandedIsChange = true;
                    ExpandedChangeIdMap[this.getNodeKey(item)] = {
                        isExpanded: true,
                        ExpandedIsChange: true
                    };
                } else {
                    item.isExpanded = false;
                    item.ExpandedIsChange = true;
                    ExpandedChangeIdMap[this.getNodeKey(item)] = {
                        isExpanded: false,
                        ExpandedIsChange: true
                    };
                }
            }
    
            this.handleItemExpendedCollapse(item, hasClassExpand);
        }

        // 更新数据
        this.updateState(this.state.stateTree);
    }

    // 处理节点的展开收起
    handleItemExpendedCollapse = (item: TreeData, hasClassExpand: boolean|undefined) => {
        const { onNodeExpand, onNodeCollapse } = this.props;
        // parent node
        if (this.getNodeChildren(item)?.length) {
            // toggleClass(evtNode, 'is-expanded');
            if (!hasClassExpand) {
                // onNodeExpand hook
                if (onNodeExpand) {
                    onNodeExpand(
                        cloneLoop({
                            id: this.getNodeKey(item),
                            label: this.getNodeLabel(item),
                        }),
                    );
                }
            } else {
                // onNodeCollapse hook
                if (onNodeCollapse) {
                    onNodeCollapse(
                        cloneLoop({
                            id: this.getNodeKey(item),
                            label: this.getNodeLabel(item),
                        }),
                    );
                }
            }
        }
    };

    execLazyLoad = (evt: any, item: TreeData, otherParams?: ClickOtherParams) => {
        const { lazyLoad } = this.props;
        if (lazyLoad && typeof lazyLoad === 'function') {
            item.__isLoading__ = true;
            // exec lazyLoadFn
            lazyLoad(item).then((newNode: TreeData) => {
                item.__isLoading__ = false;
                this.appendNode(newNode);
                this.forceUpdate(() => {
                    // 回调事件
                    const { isExpend } = otherParams || {};
                    const _evtNode = evt.parentNode.parentNode;
                    const hasClassExpand = isExpend && hasClass(_evtNode, 'is-expanded');
                    // 加载完毕后再执行，这里需要取反
                    this.handleItemExpendedCollapse(item, !hasClassExpand);
                });
            })
        } else {
            console.error(`[${this.context.prefixCls}-react] lazyLoad must be a function!`);
        }
    }

    appendNode = (item: TreeData) => {
        this.resertCurrNode(item);
        const root = this.getRootNode(item);
        if (root) {
            this.factorialNode(this.getNodeChildren(root), root);
            this.updateState(this.state.stateTree);
        }
    }

    resertCurrNode = (item: TreeData) => {
        item.isLeaf = false;
        item.isLastNode = false;
        item.hasChild = true;
        item.isExpanded = true; // 动态节点默认展开
    }

    updateState = (data: TreeData[]) => {
        // 创建一个临时的 flatState 数组来存储所有节点
        const newFlatState: TreeData[] = [];
        
        // 递归函数，用于遍历树并收集所有节点到 newFlatState
        const collectNodes = (nodes: TreeData[]) => {
            if (nodes && nodes.length) {
                nodes.forEach(node => {
                    // 由于我们要保持 flatState 中的引用与 stateTree 中的相同
                    // 这里直接将节点添加到 newFlatState 中
                    newFlatState.push(node);
                    
                    // 递归处理子节点
                    if (this.getNodeChildren(node)?.length) {
                        collectNodes(this.getNodeChildren(node));
                    }
                });
            }
        };
        
        // 收集所有节点
        collectNodes(data);
        
        // 更新 state
        this.setState({
            stateTree: data,
            flatState: newFlatState,
        });
    }

    getRootNode = (node?: TreeData) => {
        if (node && !node.isRoot) {
            this.getRootNode(node.parent);
        }
        return node;
    }

    onDisabledClick = (event: any) => {
        event.persist();
        event.stopPropagation();
    }

    renderCheckbox = (item: TreeData) => {
        const { showCheckbox } = this.props;
        if (!showCheckbox) {
            return null;
        }

        const checkedStyle = classNames('custom-checkbox', {
            'half-checked': item.childCheckedStatus === 3,
        });

        return (
            <label className={`${this.context.prefixCls}-checkbox`}>
                {
                    (this.isDisabled(item)) ? (
                        <input
                            className="tree-input"
                            type="checkbox"
                            disabled
                            checked={!!item.isChecked}
                        />
                    ) : (
                        <input
                            className="tree-input"
                            type="checkbox"
                            checked={!!item.isChecked}
                            onClick={evt => { evt.persist(); evt.stopPropagation(); }}
                            onChange={evt => { this.handleCheckChange(evt, item); }}
                        />
                    )
                }
                <span
                    className={checkedStyle}
                />
            </label>
        );
    }

    renderLabel = (item: TreeData) => {
        const { nodeRender } = this.props;
        const { searchValue } = this.state;

        // nodeRender func render label
        if (nodeRender && typeof nodeRender === 'function') {
            return nodeRender(item, searchValue);
        }
        const label = this.getNodeLabel(item);
        const findIndex: number = label.indexOf(searchValue);
        if (searchValue && findIndex > -1) {
            const start = label.slice(0, findIndex);
            const end = label.slice(findIndex + searchValue.length);
            return (
                <>
                    {
                        item.icon ? (
                            <span
                                style={{ display: 'inline-block' }}
                                className={`${this.context.prefixCls}-tree-node-icon`}
                            >{item.icon}
                            </span>
                        ) : null
                    }
                    {start}
                    <span
                        style={{ color: $danger }}
                        className={`${this.context.prefixCls}-tree-search-label`}
                    >
                        { searchValue}
                    </span>
                    {end}
                </>
            );
        }

        // default label
        return (
            <span className={`${this.context.prefixCls}-tree-node-label-content`}>
                {
                    item.icon ? (
                        <span
                            className={`${this.context.prefixCls}-tree-node-icon`}
                        >{item.icon}
                        </span>
                    ) : null
                }
                <span>{label}</span>
            </span>
        );
    }

    onDragEnd = (startNode: TreeData, endNode: TreeData, direction: 'top' | 'bottom') => {
        // 确保节点存在
        if (!startNode || !endNode) {
            return;
        }
        
        const startNodeKey = this.getNodeKey(startNode);
        const endNodeKey = this.getNodeKey(endNode);
        if (startNodeKey === endNodeKey) {
            return;
        }
        if (this.props.onDragEnd) {
            this.props.onDragEnd({
                startNode: cloneLoop({
                    id: startNodeKey,
                    label: this.getNodeLabel(startNode),
                }),
                endNode: cloneLoop({
                    id: endNodeKey,
                    label: this.getNodeLabel(endNode),
                }),
                startParentNode: startNode.parent ? cloneLoop({
                    id: this.getNodeKey(startNode.parent),
                    label: this.getNodeLabel(startNode.parent),
                }) : undefined,
                endParentNode: endNode.parent ? cloneLoop({
                    id: this.getNodeKey(endNode.parent),
                    label: this.getNodeLabel(endNode.parent),
                }) : undefined,
                direction
            });
        }
    }

    onDragging = (targetNode: TreeData, startNode: TreeData) => {
        // 确保节点存在
        if (!targetNode || !startNode) {
            return;
        }
        
        if (this.props.onDragging) {
            this.props.onDragging(targetNode, startNode);
        }
        // startNode.isExpanded = false
        // startNode.ExpandedIsChange = true
        // this.setState({ targetNode });
        // this.setState({this.state.stateTree})
    }

    setCurrentDirection = (direction: 'top' | 'bottom') => {
        this.setState({ direction });
    }

    // factorial render tree node
    renderDragTree = (data: TreeData[]) => {
        const {
            nodeKey,
            lazy,
            className,
            style,
            expandedKeys
        } = this.props;

        if (!data || !data.length) return null;

        const ischild = (pn: TreeData, cn: TreeData) => {
            let currentPn = { ...cn.parent } as TreeData;
            let result = false;
            while (currentPn && this.getNodeKey(currentPn) && !result) {
                if (this.getNodeKey(currentPn) === this.getNodeKey(pn)) {
                    result = true;
                } else {
                    currentPn = { ...currentPn.parent } as TreeData;
                }
            }
            return result;
        };

        const ononDragEnterOver = (event: React.DragEvent<HTMLElement>, key: string, direction: 'top' | 'bottom', dropNode: TreeData) => {
            if (this.state.dragingNodeKey === key && direction === this.state.direction) {
                return;
            }
            
            // 确保 startDragNode 存在
            if (!this.state.startDragNode || Object.keys(this.state.startDragNode).length === 0) {
                return;
            }
            
            const checkChild = ischild(this.state.startDragNode, dropNode);
            let allowDrop = true;
            if (this.props.dropAllowed && typeof this.props.dropAllowed === 'function') {
                allowDrop = this.props.dropAllowed(this.state.startDragNode, dropNode, direction);
            }
            if (checkChild || !allowDrop) {
                this.setState({
                    dragingNodeKey: '',
                });
            } else {
                this.onDragging(this.state.startDragNode, dropNode);
                this.setState({
                    dragingNodeKey: key,
                    direction,
                });
            }
        };

        return data.map((node: TreeData, index: number) => (
            <TreeNode
                onDragStart={(event: React.DragEvent<HTMLElement>, key: string, dragNode: TreeData) => {
                    this.setState({
                        startDragNodeKey: key,
                        startDragNode: dragNode
                    });
                }}
                onDragEnd={() => {
                    this.setState({
                        dragingNodeKey: '',
                        direction: 'top',
                        startDragNodeKey: '',
                    });
                }}
                onDragEnter={ononDragEnterOver}
                onDragOver={ononDragEnterOver}
                onDrop={(event: React.DragEvent<HTMLElement>, key: string, direction: 'top' | 'bottom', dropNode: TreeData) => {
                    // 确保 startDragNode 存在且不为空对象
                    if (!this.state.startDragNode || Object.keys(this.state.startDragNode).length === 0) {
                        this.setState({
                            dragingNodeKey: '',
                            startDragNodeKey: ''
                        });
                        return;
                    }
                    
                    let allowDrop = true;
                    if (this.props.dropAllowed && typeof this.props.dropAllowed === 'function') {
                        allowDrop = this.props.dropAllowed(this.state.startDragNode, dropNode, direction);
                    }
                    const checkChild = ischild(this.state.startDragNode, dropNode);
                    
                    if (checkChild || !allowDrop) {
                        this.setState({
                            dragingNodeKey: '',
                            startDragNodeKey: ''
                        });
                    }
                    this.setState({
                        dragingNodeKey: '',
                        startDragNodeKey: ''
                    });
                    this.onDragEnd(this.state.startDragNode, dropNode, direction);
                }}
                dragingNodeKey={this.state.dragingNodeKey}
                startDragNodeKey={this.state.startDragNodeKey}
                key={this.getNodeKey(node)}
                expandedKeys={expandedKeys}
                index={index}
                node={node}
                lazy={lazy}
                nodeKey={nodeKey}
                className={className}
                style={style}
                handleClick={this.handleClick}
                renderCheckbox={this.renderCheckbox}
                renderLabel={this.renderLabel}
                direction={this.state.direction}
                switcherIcon={this.props.switcherIcon}
                expandAction={this.props.expandAction}
            >
                {
                    node.hasChild ? this.renderDragTree(this.getNodeChildren(node)) : null
                }
            </TreeNode>
        ));
    }

    handleExpendAction = (evt: any, node: TreeData, action: string) => {
        const { expandAction } = this.props;
        if (expandAction === action) {
            const otherParams = {
                evtName: 'node-label',
                isExpend: true,
            };
            this.handleClick(evt, node, otherParams);
        } else {
            const otherParams = {
                evtName: 'node-label',
                isExpend: false,
            };
            this.handleClick(evt, node, otherParams);
        }
    }

    renderTree = (data: TreeData[]) => {
        const {
            lazy,
            className,
            style,
            expandedKeys,
            switcherIcon,
            expandAction,
            blockNode,
            multiple,
            selectedKey,
            showCheckbox,
        } = this.props;
        if (!data || !data.length) return null;
        return data.map((node: TreeData) => {
            if (!node) return null; // 过滤掉异常node
            const nodeId = this.getNodeKey(node);
            const isSelected = !multiple ? selectedKey === this.getNodeKey(node) : node.isSelected;
            const isExpandedNode = expandedKeys ? expandedKeys.indexOf(nodeId) > -1 : node.isExpanded;
            return (
                <div
                    className={classNames(`${this.context.prefixCls}-tree-node`,
                        {
                            'is-expanded': isExpandedNode,
                            'is-leaf': !(node.isLeaf && lazy) && node.isLastNode,
                        }, className)}
                    style={style}
                    data-react={nodeId}
                    key={nodeId}
                >

                    <div
                        className={classNames(`${this.context.prefixCls}-tree-node-content`, {
                            disabled: node.isDisabled,
                            [`${this.context.prefixCls}-tree-is-selected`]: this.props.showCheckbox ? false : isSelected,
                        })}
                    >
                        {
                            // eslint-disable-next-line no-nested-ternary
                            switcherIcon ? (
                                // 叶子节点不展示
                                !(node.isLeaf && lazy) && node.isLastNode ? null : (
                                    <div
                                        className={classNames({
                                            'custom-tree-node-icon': isExpandedNode,
                                            [`${this.context.prefixCls}-tree-node-loading-icon`]: node.__isLoading__,
                                        })}
                                        onClick={evt => {
                                            if (expandAction === 'click') {
                                                const otherParams = {
                                                    isExpend: true,
                                                };
                                                this.handleClick(evt, node, otherParams);
                                            }
                                        }}
                                        onDoubleClick={evt => {
                                            if (expandAction === 'doubleClick') {
                                                const otherParams = {
                                                    isExpend: true,
                                                };
                                                this.handleClick(evt, node, otherParams);
                                            }
                                        }}
                                        // ref={this.iconNodeRef}
                                    >
                                        {switcherIcon}
                                    </div>
                                )
                            ) : (
                                <div
                                    className={classNames({
                                        [`${this.context.prefixCls}-tree-node-loading-icon`]: node.__isLoading__,
                                        [`${this.context.prefixCls}-tree-node-expand-icon`]: !node.__isLoading__,
                                    })}
                                    // ref={this.iconNodeRef}
                                    onClick={evt => {
                                        if (expandAction === 'click') {
                                            const otherParams = {
                                                isExpend: true,
                                            };
                                            this.handleClick(evt, node, otherParams);
                                        }
                                    }}
                                    onDoubleClick={evt => {
                                        if (expandAction === 'doubleClick') {
                                            const otherParams = {
                                                isExpend: true,
                                            };
                                            this.handleClick(evt, node, otherParams);
                                        }
                                    }}
                                />
                            )
                        }
                        {this.renderCheckbox(node)}
                        <div
                            className={classNames(
                                `${this.context.prefixCls}-tree-node-label`,
                                { [`${this.context.prefixCls}-tree-node-label-block`]: blockNode }
                            )}
                            onClick={evt => {
                                if (expandAction === 'click') {
                                    this.handleExpendAction(evt, node, 'click');
                                }
                                if (blockNode && !node.isDisabled && showCheckbox) {
                                    // 如果占据整行，并且当前tree是支持多选，则可以点击行选中
                                    this.handleCheckChange(evt, node, !node.isChecked);
                                }
                            }}
                            onDoubleClick={evt => {
                                if (expandAction === 'doubleClick') {
                                    this.handleExpendAction(evt, node, 'doubleClick');
                                }
                            }}
                        >
                            {this.renderLabel(node)}
                        </div>
                    </div>
                    {
                        node.hasChild && isExpandedNode
                            ? (
                                <div className={`${this.context.prefixCls}-tree-node-children`}>
                                    {this.renderTree(this.getNodeChildren(node))}
                                </div>
                            )
                            : null
                    }
                </div>
            );
        });
    }

    onSearchValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        this.setState({ searchValue: value });
        const { stateTree } = this.state;
        this.setTreeNodeExpanded(stateTree, value);
        if (this.props?.searchInput?.onChange) {
            this.props.searchInput.onChange(e);
        }
        this.setState({ stateTree });
    }

    setTreeNodeExpanded = (stateTree: TreeData[], value: string) => {
        // 节点1 节点2 节点3
        let totalFlag = false;
        stateTree.forEach((item: TreeData) => {
            let singleFlag = false;
            if (this.getNodeChildren(item)?.length) {
                singleFlag = this.setTreeNodeExpanded(this.getNodeChildren(item), value);
            }
            if (singleFlag) {
                // 展开当前节点，如节点1
                item.isExpanded = true;
                item.ExpandedIsChange = true;
            } else {
                item.isExpanded = false;
                item.ExpandedIsChange = true;
            }
            const label = this.getNodeLabel(item);
            // 都转小写去看有没有匹配的值
            const labelToLowerCase = label?.toString()?.toLowerCase() || label;
            const valueToLowerCase = value?.toString()?.toLowerCase() || value;
            if (labelToLowerCase?.indexOf(valueToLowerCase) > -1 || singleFlag) {
                totalFlag = true;
            }
        });
        return totalFlag;
    }

    render() {
        const { stateTree } = this.state;
        const { search, searchInput, draggable } = this.props;

        let treeDom;
        if (draggable) {
            const currentTreeDom = this.renderDragTree(stateTree);
            treeDom = (
                <div className={`${this.context.prefixCls}-draggable-tree`}>
                    {currentTreeDom}
                </div>
            );
        } else {
            treeDom = this.renderTree(stateTree);
        }
        if (search) {
            const searchProps = searchInput || {};
            const {
                onChange,
                value,
                ...rest
            } = searchProps;
            return (
                <div className={`${this.context.prefixCls}-search-tree`}>
                    <Input
                        {...rest}
                        onChange={this.onSearchValueChange}
                        value={this.state.searchValue}
                    />
                    {
                        treeDom
                    }
                </div>
            );
        }
        return treeDom;
    }
}

export default Tree;