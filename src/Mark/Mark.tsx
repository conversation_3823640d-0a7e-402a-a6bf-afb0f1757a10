import * as React from 'react';
import * as PropTypes from 'prop-types';
import classNames from 'classnames';
import { polyfill } from 'react-lifecycles-compat';
import { hasClass } from '@utiljs/dom';
import { isFunction, isNumber } from '@utiljs/is';
import Tooltip from '@roo/roo/Tooltip';
import KeyCode from 'rc-util/lib/KeyCode';
import locale from '../locale';
import { MarkProps, MarkState } from './interface';
import { GlobalConfigContext } from '../ConfigProvider';
import { $gray400, $brand } from '../_utils/ThemeColor';
import withDisabled from '../_utils/hoc/withDisabled';

class Mark extends React.Component<MarkProps, MarkState> {
    static contextType?: React.Context<any> | undefined = GlobalConfigContext;

    static propTypes = {
        defaultValue: PropTypes.number,
        value: PropTypes.number,
        max: PropTypes.number,
        disabled: PropTypes.bool,
        allowHalf: PropTypes.bool,
        lowThreshold: PropTypes.number,
        highThreshold: PropTypes.number,
        colors: PropTypes.array,
        voidColor: PropTypes.string,
        iconType: PropTypes.oneOf(['star', 'face']),
        showText: PropTypes.bool,
        texts: PropTypes.array,
        textColor: PropTypes.string,
        onChange: PropTypes.func,
        onHoverChange: PropTypes.func,
        tooltips: PropTypes.array,
        character: PropTypes.node,
        allowClear: PropTypes.bool,
        keyboard: PropTypes.bool,
        autoFocus: PropTypes.bool,
        onFocus: PropTypes.func,
        onBlur: PropTypes.func,
    };

    static defaultProps = {
        defaultValue: undefined,
        value: undefined,
        max: 5,
        disabled: false,
        allowHalf: false,
        lowThreshold: 2,
        highThreshold: 4,
        colors: [$brand, $brand, $brand],
        voidColor: $gray400,
        iconType: 'star',
        showText: false,
        texts: [],
        textColor: '',
        onChange: undefined,
        onHoverChange: undefined,
        tooltips: [],
        character: undefined,
        allowClear: false,
        keyboard: false,
        autoFocus: false,
        onFocus: undefined,
        onBlur: undefined,
    };

    private starsRef = React.createRef<HTMLSpanElement>();
    private isFocusing = false;

    getInitValue = () => {
        const { value, defaultValue } = this.props;
        let newValue = defaultValue;
        if (isNumber(value)) {
            newValue = value;
        }
        return newValue || 0;
    };

    state = {
        value: this.getInitValue(),
        currentValue: this.getInitValue(),
        hoverValue: -1,
    };

    private faceClassMap: Array<string> = ['frown', 'meh', 'smile'];


    componentDidMount() {
        if (this.props.autoFocus && !this.props.disabled) {
            setTimeout(() => {
                if (this.starsRef.current) {
                    this.starsRef.current.focus();
                }
            }, 0);
        }
    }

    componentDidUpdate(prevProps: MarkProps) {
        // autoFocus 是受控属性，当属性值变化时会触发重新聚焦
        if (prevProps.autoFocus !== this.props.autoFocus && this.props.autoFocus && !this.props.disabled) {
            setTimeout(() => {
                if (this.starsRef.current) {
                    this.starsRef.current.focus();
                }
            }, 0);
        }
    }

    static getDerivedStateFromProps(nextProps: MarkProps) {
        if (nextProps.value !== undefined) {
            return {
                value: nextProps.value,
            };
        }
        return null;
    }

    handleFocus = (e: React.FocusEvent<HTMLSpanElement>): void => {
        const { onFocus, disabled } = this.props;
        if (disabled) return;
        if (!this.isFocusing) {
            this.isFocusing = true;
            if (isFunction(onFocus)) {
                onFocus(e);
            }
        }
    };

    handleBlur = (e: React.FocusEvent<HTMLSpanElement>): void => {
        const { onBlur, disabled } = this.props;
        if (disabled) return;
        this.isFocusing = false;
        if (isFunction(onBlur)) {
            onBlur(e);
        }
    };

    handleMouseMove = (value: number, event: React.MouseEvent<HTMLSpanElement>): void => {
        const { disabled, allowHalf, onHoverChange } = this.props;
        if (disabled) return;
        if (allowHalf) {
            const { value: hoverVal, pointerAtSmallHalf } = this.getCurrentValue(value, event);
            const currVal = pointerAtSmallHalf ? hoverVal - 0.5 : hoverVal;
            this.setState({
                hoverValue: hoverVal,
                currentValue: currVal,
            });
            if (isFunction(onHoverChange)) {
                onHoverChange(currVal);
            }
        } else {
            this.setState({
                hoverValue: value,
                currentValue: value,
            });
            if (isFunction(onHoverChange)) {
                onHoverChange(value);
            }
        }
    };

    handleMouseLeave = (): void => {
        const { disabled, onHoverChange } = this.props;
        const { value } = this.state;
        if (disabled) return;
        this.setState({
            hoverValue: -1,
            currentValue: value,
        });
        if (isFunction(onHoverChange)) {
            // 移出传入 undefined
            onHoverChange(undefined);
        }
    };

    getCurrentValue = (value: number, event: React.MouseEvent<HTMLSpanElement>) => {
        let { target } = event;
        if (hasClass(target as HTMLSpanElement, `${this.context.prefixCls}-mark-item`)) {
            target = (target as HTMLSpanElement).querySelector(`.${this.context.prefixCls}-mark-icon`) as HTMLElement;
        }
        if (hasClass(target as HTMLSpanElement, `${this.context.prefixCls}-mark-half-icon`)) {
            target = (target as HTMLSpanElement).parentNode as HTMLElement;
        }

        // 正常情况下：鼠标指针相对于目标元素左边界的水平距离
        // RTL情况下：鼠标指针相对于目标元素右边界的水平距离
        const distanceToBorder =
            this.context.direction === 'RTL'
                ? (target as any).getBoundingClientRect().right - event.clientX
                : event.clientX - (target as any).getBoundingClientRect().left;
        const pointerAtSmallHalf = Boolean(distanceToBorder * 2 < (target as HTMLElement).clientWidth);
        return { value, pointerAtSmallHalf };
    };

    handleClickChange = (value: number, e: React.MouseEvent<HTMLSpanElement, MouseEvent>): void => {
        const { value: propValue, disabled, allowHalf, onChange, allowClear } = this.props;
        const { pointerAtSmallHalf, value: hoverVal } = this.getCurrentValue(value, e);
        const currentValue = allowHalf && pointerAtSmallHalf ? hoverVal - 0.5 : hoverVal;

        const isReset = allowClear && currentValue === this.state.value;

        if (disabled) return;
        if (allowHalf && pointerAtSmallHalf) {
            if (propValue === undefined) {
                this.setState({
                    value: isReset ? 0 : currentValue,
                    currentValue: isReset ? 0 : currentValue,
                });
            }
            this.setState({ currentValue: isReset ? 0 : currentValue });
            if (onChange) {
                onChange(isReset ? 0 : currentValue!);
            }
        } else {
            if (propValue === undefined) {
                this.setState({
                    currentValue: isReset ? 0 : value,
                    value: isReset ? 0 : value,
                });
            }
            this.setState({ currentValue: isReset ? 0 : value });
            if (onChange) {
                onChange(isReset ? 0 : value);
            }
        }
    };

    getRangeColorOrClass = (currentValue: number, map: Array<string>): string => {
        const { lowThreshold, highThreshold } = this.props;
        if (currentValue <= lowThreshold!) {
            return map[0];
        }
        if (Math.ceil(currentValue) < highThreshold!) {
            return map[1];
        }
        return map[2];
    };

    getIconColor = (value: number): object => {
        const { colors, voidColor } = this.props;
        const { currentValue } = this.state;

        if (value <= currentValue!) {
            return {
                color: this.getRangeColorOrClass(currentValue!, colors!),
            };
        }
        return {
            color: voidColor,
        };
    };

    getHalfIconColor = (): object => {
        const { colors } = this.props;
        const { currentValue } = this.state;
        const color = this.getRangeColorOrClass(currentValue!, colors!);
        return { color };
    };

    getIconClass = (value: number): string => {
        const { iconType, character } = this.props;
        const { currentValue, hoverValue } = this.state;
        let icon: string = value <= currentValue! ? 'star-fill' : 'star';
        if (iconType === 'face') {
            icon = this.getRangeColorOrClass(value, this.faceClassMap);
        }
        if (character) return classNames(`${this.context.prefixCls}-mark-icon icon`, { hover: value === hoverValue });
        return classNames(`${this.context.prefixCls}-mark-icon icon icon-${icon}`, { hover: value === hoverValue });
    };

    getHalfIconClass = (): string => {
        const { iconType } = this.props;
        const { currentValue } = this.state;
        let icon: string = '';
        icon = 'star-fill';
        const { character } = this.props;
        if (iconType === 'face') {
            icon = this.getRangeColorOrClass(currentValue!, this.faceClassMap);
        }
        if (character) return classNames(`${this.context.prefixCls}-mark-icon icon`);
        return `${this.context.prefixCls}-mark-icon icon icon-${icon}`;
    };

    showHalfIcon(value: number) {
        const { currentValue } = this.state;
        const { allowHalf } = this.props;
        return allowHalf && Math.ceil(currentValue!) === value && Math.ceil(currentValue!) !== currentValue;
    }

    renderHalfIcon(value: number): React.ReactNode {
        const halIconStyle: object = {
            position: 'absolute',
            top: 0,
            ...(this.context.direction === 'RTL' ? { right: 0 } : { left: 0 }),
            overflow: 'hidden',
            width: '50%',
        };
        if (!this.showHalfIcon(value)) return null;
        return (
            <i
                className={`${this.context.prefixCls}-mark-half-icon ${this.getHalfIconClass()}`}
                style={Object.assign(this.getHalfIconColor(), halIconStyle)}
            />
        );
    }

    renderCustomHalfIcon(value: number) {
        const { character } = this.props;
        const halIconStyle: object = {
            position: 'absolute',
            // top: 0,
            ...(this.context.direction === 'RTL' ? { right: 0 } : { left: 0 }),
            overflow: 'hidden',
            width: '50%',
        };
        if (!this.showHalfIcon(value)) return null;
        return (
            <span
                className={`${this.context.prefixCls}-mark-half-icon ${this.getHalfIconClass()}`}
                style={Object.assign(this.getHalfIconColor(), halIconStyle)}
            >
                {character}
            </span>
        );
    }

    handleKeyDown(event: React.KeyboardEvent) {
        const { allowHalf, disabled, max = 5, value: propValue, onChange, keyboard, onKeyDown } = this.props;
        if (disabled) return;
        const isRTL = this.context.direction === 'RTL';
        const { currentValue = 0 } = this.state;
        const step = allowHalf ? 0.5 : 1;
        const updateValue = (v: number) => {
            if (propValue === undefined) {
                this.setState({
                    currentValue: v,
                    value: v,
                });
            }
            this.setState({ currentValue: v });
            if (onChange) {
                onChange(v);
            }
        };
        if (keyboard) {
            if (
                ((event.keyCode === KeyCode.RIGHT && !isRTL) || (event.keyCode === KeyCode.LEFT && isRTL)) &&
                currentValue + step <= max
            ) {
                const nextValue = currentValue + step;
                updateValue(nextValue);
            } else if (
                ((event.keyCode === KeyCode.LEFT && !isRTL) || (event.keyCode === KeyCode.RIGHT && isRTL)) &&
                currentValue - step >= 0
            ) {
                const nextValue = currentValue - step;
                updateValue(nextValue);
            }
        }
        if (onKeyDown) {
            onKeyDown(event);
        }
    }

    renderContent(): React.ReactNode {
        const {
            className,
            style,
            allowHalf,
            voidColor,
            iconType,
            showText,
            lowThreshold,
            highThreshold,
            textColor,
            tooltips,
            character,
            onChange,
            keyboard, // 从 reset中抽离
            onHoverChange, // 从 reset中抽离
            allowClear, // 从 reset中抽离
            autoFocus,
            onFocus,
            onBlur,
            ...restProps
        } = this.props;
        const markList = [];

        if (tooltips?.length) {
            for (let value = 1; value <= this.props.max!; value++) {
                markList.push(
                    <span
                        {...restProps}
                        key={`mark-${value}`}
                        className={classNames(`${this.context.prefixCls}-mark-item`, className)}
                        style={style}
                        onMouseMove={event => this.handleMouseMove(value, event)}
                        onClick={e => this.handleClickChange(value, e)}
                        tabIndex={-1}
                    >
                        <Tooltip content={tooltips![value! - 1]}>
                            {character ? (
                                <span
                                    style={{ ...this.getIconColor(value), width: 'auto' }}
                                    className={this.getIconClass(value)}
                                >
                                    {character}
                                    {this.renderCustomHalfIcon(value)}
                                </span>
                            ) : (
                                <i
                                    className={this.getIconClass(value)}
                                    style={this.getIconColor(value)}
                                >
                                    {this.renderHalfIcon(value)}
                                </i>
                            )}
                        </Tooltip>
                    </span>,
                );
            }
        } else {
            for (let value = 1; value <= this.props.max!; value++) {
                markList.push(
                    <span
                        {...restProps}
                        key={`mark-${value}`}
                        className={classNames(`${this.context.prefixCls}-mark-item`, className)}
                        style={style}
                        onMouseMove={event => this.handleMouseMove(value, event)}
                        onClick={e => this.handleClickChange(value, e)}
                        tabIndex={-1}
                    >
                        {character ? (
                            <span
                                className={this.getIconClass(value)}
                                style={{ ...this.getIconColor(value), width: 'auto' }}
                            >
                                {character}
                                {this.renderCustomHalfIcon(value)}
                            </span>
                        ) : (
                            <i
                                className={this.getIconClass(value)}
                                style={this.getIconColor(value)}
                            >
                                {this.renderHalfIcon(value)}
                            </i>
                        )}
                    </span>,
                );
            }
        }
        return markList;
    }

    renderMarkText(): React.ReactNode | null {
        const { showText, texts, textColor } = this.props;
        const textsLocale = !texts?.length ? locale.lng('Mark.texts') : texts;
        const { currentValue } = this.state;
        if (!showText) return null;
        return (
            <span
                className={`${this.context.prefixCls}-mark-text`}
                style={{ color: textColor }}
            >
                {textsLocale![currentValue! - 1]}
            </span>
        );
    }

    render(): React.ReactNode {
        return (
            <div
                className={`${this.context.prefixCls}-mark`}
                onKeyDown={e => this.handleKeyDown(e)}
            >
                <span
                    ref={this.starsRef}
                    onBlur={this.handleBlur}
                    onFocus={this.handleFocus}
                    tabIndex={-1}
                >
                    {this.renderContent()}
                </span>
                {this.renderMarkText()}
            </div>
        );
    }
}

polyfill(Mark);
export default withDisabled<typeof Mark>(Mark);
