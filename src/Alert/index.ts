/*
 * Alert
 */
import RooAlert from './Alert';
import ErrorBoundary from './ErrorBoundary';
import withApiCompatibility from '../_utils/hoc/withApiCompatibility';

export * from './interface';

// 应用API兼容高阶组件
const WrappedAlert = withApiCompatibility('Alert', RooAlert);

type CompoundedComponent = typeof WrappedAlert & {
    ErrorBoundary: typeof ErrorBoundary;
};

const Alert = WrappedAlert as CompoundedComponent;
Alert.ErrorBoundary = ErrorBoundary;

export default Alert;
