import * as React from 'react';
import * as PropTypes from 'prop-types';
import warning from 'warning';
import classnames from 'classnames';
import { GlobalConfigContext } from '@roo/roo/ConfigProvider';
import { isFunction, isString } from '@utiljs/is';
import CSSMotion from 'rc-motion';
import { AlertProps, AlertState } from './interface';

class Alert extends React.Component<AlertProps, AlertState> {
    static contextType = GlobalConfigContext;
    context!: React.ContextType<typeof GlobalConfigContext>;

    static propTypes = {
        type: PropTypes.oneOf(['success', 'warning', 'info', 'danger']),
        icon: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
        title: PropTypes.any,
        showIcon: PropTypes.bool,
        dismissable: PropTypes.bool,
        closable: PropTypes.bool,
        extra: PropTypes.any,
        isShow: PropTypes.bool,
        onClose: PropTypes.func,
        className: PropTypes.string,
        style: PropTypes.object,
        description: PropTypes.any,
        closeIcon: PropTypes.node,
        isCloseMotion: PropTypes.bool,
        afterClose: PropTypes.func,
        noMargin: PropTypes.bool,
    };

    static defaultProps = {
        icon: 'roo-icon-check-circle-new',
        showIcon: true,
        dismissable: false,
        closable: false,
        extra: '',
        type: 'info',
        isShow: true,
        title: '',
        onClose: () => {},
        className: '',
        style: {},
        description: null,
        closeIcon: null,
        isCloseMotion: false,
        afterClose: undefined,
        noMargin: false,
    };

    constructor(props: AlertProps) {
        super(props);
        if (props.dismissable) {
            warning(false, 'Roo Alert: `dismissable` 已废弃，请使用 `closable`');
        }

        this.state = {
            closed: false,
        };
    }

    onClose = (e?: React.MouseEvent<HTMLElement>): void => {
        if (this.props.onClose) {
            this.props.onClose(e);
        }
        this.close();
    };

    close = (): void => {
        this.setState({
            closed: true,
        });
    };

    handleAfterClose = () => {
        const { afterClose } = this.props;
        if (isFunction(afterClose)) {
            afterClose();
        }
    };

    renderClose = (): React.ReactNode | null => {
        if (this.props.dismissable || this.props.closable) {
            return (
                <a
                    className="close"
                    onClick={this.onClose}
                >
                    {this.props.closeIcon ? this.props.closeIcon : <i className="roo-icon roo-icon-close-new" />}
                </a>
            );
        }
        return null;
    };

    renderLeftIcon = () => {
        const { icon, showIcon } = this.props;
        if (!showIcon) return null;
        if (isString(icon)) {
            // 字符串类型认为是icon的name
            return <i className={`roo-icon ${icon}`} />;
        }
        // 其他情况认为是reactNode
        return icon;
    };

    getAlertCls = () => {
        const { description, type, className, banner, size = this.context.size || 'normal' } = this.props;
        return classnames(`${this.context.prefixCls}-alert`, `${this.context.prefixCls}-alert-${type}`, className, {
            [`${this.context.prefixCls}-alert-${size}`]: size && size !== 'normal',
            [`${this.context.prefixCls}-alert-banner`]: !!banner,
            [`${this.context.prefixCls}-alert-with-description`]: !!description,
            [`${this.context.prefixCls}-alert-no-margin`]: this.props.noMargin,
        });
    };

    render(): React.ReactNode {
        const { isShow, title, extra, style, description, action, isCloseMotion } = this.props;
        const { closed } = this.state;
        return (
            <div>
                <CSSMotion
                    visible={isShow && !closed}
                    motionName={
                        isCloseMotion
                            ? `${this.context.prefixCls}-alert-close-motion`
                            : `${this.context.prefixCls}-alert-motion`
                    }
                    onLeaveStart={(node: any) => ({
                        maxHeight: node.offsetHeight,
                    })}
                    onLeaveEnd={this.handleAfterClose}
                >
                    {({ className: motionClassName, style: motionStyle }, ref) => (
                        <div
                            ref={ref}
                            style={motionStyle || {}}
                            className={motionClassName || ''}
                        >
                            <div
                                className={this.getAlertCls()}
                                style={style}
                                role="alert"
                            >
                                {this.renderLeftIcon()}
                                <div className={`${this.context.prefixCls}-alert-content`}>
                                    <span className={`${this.context.prefixCls}-title`}>{title}</span>
                                    {description && (
                                        <div className={classnames('description', { 'no-title-description': !title })}>
                                            {description}
                                        </div>
                                    )}
                                </div>
                                {action ? (
                                    <div className={`${this.context.prefixCls}-alert-action`}>{action}</div>
                                ) : null}
                                {extra ? <a className="extra">{extra}</a> : this.renderClose()}
                            </div>
                        </div>
                    )}
                </CSSMotion>
            </div>
        );
    }
}

export default Alert;
