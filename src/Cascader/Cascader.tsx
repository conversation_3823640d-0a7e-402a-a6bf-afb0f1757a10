/* eslint-disable no-nested-ternary */
import * as React from 'react';
import * as PropTypes from 'prop-types';
import { Manager, Reference, PopperPortal } from '@roo/roo/core/Popper';
import { createRef } from '@roo/create-react-ref';
import Icon from '@roo/roo/Icon';
import Input from '@roo/roo/Input';
import { isFunction, isArray, isNull } from '@utiljs/is';
import { cloneLoop } from '@utiljs/clone';
import locale from '@roo/roo/locale';
import cls from 'classnames';
import isEqual from 'lodash/isEqual';
import { GlobalConfigContext } from '@roo/roo/ConfigProvider';
import Empty from '@roo/roo/core/Empty';
import ReactResizeDetector from '../_utils/ReactResizeDetector';
import useSearchOptions from './utils/useSearchOptions';
import VirtualList from '../_utils/VirtualList';
import { innerPlacement, placementEnum, placementTypes } from '../_utils/types';
import Link from '../_utils/Link';
import { scrollIntoParentView } from './utils/commonUtil';
import { $btnBrand, $light } from '../_utils/ThemeColor';
import { isThenable } from '../_utils/promise';
import {
    Option,
    SearchOption,
    CascaderProps,
    States,
    FieldNamesType,
} from './interface';
import withDisabled from '../_utils/hoc/withDisabled';

const searchOptionsPageSize = 20;
enum ShowCheckedStrategy {
    child = 'child',
    parent = 'parent'
}

const defaultFieldNames = {
    value: 'value',
    label: 'label',
    disabled: 'disabled',
    children: 'children',
    isLeaf: 'isLeaf',
};

const getArrayData = (data: any) => (isArray(data) ? data : []);

const getFiledName = (filed: 'children' | 'value' | 'label' | 'disabled' | 'isLeaf', names?: FieldNamesType) => names?.[filed] || filed;

const collectSearchOptions = (item: Option, searchOptions: SearchOption[], path: Option[], names?: FieldNamesType) => {
    const newPath = [...path, item];
    if (!item[getFiledName('children', names)] || item[getFiledName('children', names)].length === 0) {
        searchOptions.push({
            value: newPath.map(opt => opt[getFiledName('value', names)]),
            selectedOptions: newPath,
            label: newPath.map(opt => opt[getFiledName('label', names)]).join('/'),
        });
    } else {
        item[getFiledName('children', names)].forEach((childItem: Option) => {
            collectSearchOptions(childItem, searchOptions, newPath, names);
        });
    }
};

// 初始化渲染的下拉列表
const initSearchOption = (options?: Option[], names?: FieldNamesType) => {
    const searchOptions: SearchOption[] = [];
    if (options?.length) {
        options.forEach((item: Option) => {
            collectSearchOptions(item, searchOptions, [], names);
        });
    }
    return searchOptions;
};

// 用于查找节点值并构建完整路径
const findOptionsByPath = (
    options: Option[] | undefined,
    value: string | number,
    path: Option[] = [],
    names?: FieldNamesType,
): Option[] | null => {
    if (!options || !options.length) return null;

    for (let i = 0; i < options.length; i++) {
        const option = options[i];
        // 将当前选项添加到路径中，构建新的路径
        const currentPath = [...path, option];

        // 如果当前选项的值与目标值匹配，直接返回完整路径
        if (option[getFiledName('value', names)] === value) {
            return currentPath;
        }

        // 如果当前选项有子选项，递归查找子选项
        if (option[getFiledName('children', names)] && option[getFiledName('children', names)].length > 0) {
            const result = findOptionsByPath(option[getFiledName('children', names)], value, currentPath, names);
            if (result) return result;
        }
    }
    return null;
};

// 初始化渲染的下拉列表
const initOptions = (options?: Option[], value?: (number | string)[], names?: FieldNamesType) => {
    // 如果值是数组且只有一个元素，并且这个元素不是数组中的第一级选项值，则尝试查找完整路径
    if (isArray(value) && value.length === 1) {
        const singleValue = value[0];
        // 检查这个值是否是第一级选项的值
        const isFirstLevelValue = options?.some(option => option[getFiledName('value', names)] === singleValue);
        // 只有当不是第一级选项值时，才尝试查找完整路径
        if (!isFirstLevelValue) {
            const fullPath = findOptionsByPath(options, singleValue, [], names);
            // 如果找到了完整路径，使用它来初始化选项
            if (fullPath) {
                const result: Option[][] = [cloneLoop(options) || []];
                // 根据找到的路径构建嵌套选项
                for (let i = 0; i < fullPath.length - 1; i++) {
                    const children = fullPath[i][getFiledName('children', names)];
                    if (children && children.length) {
                        result.push(children);
                    }
                }
                return result;
            }
        }
    }
    const initOptionsArray: any = options ? [options] : options;
    const arrValue = getArrayData(value);
    arrValue.forEach((valueItem: string | number) => {
        const result = getArrayData(options)?.filter((optionItem: Option) => optionItem[getFiledName('value', names)] === valueItem) || [];
        if (result.length !== 0 && result[0][getFiledName('children', names)]) {
            initOptionsArray.push(result[0][getFiledName('children', names)]);
            options = result[0][getFiledName('children', names)];
        }
    });
    return initOptionsArray;
};

// 初始化选择值以及选择项
const init = (options?: Option[], value?: (number | string)[], names?: FieldNamesType) => {
    const selectedOptions: Option[] = [];
    let optionArr: Option[] | undefined = cloneLoop(options);
    if (isArray(value)) {
        // 如果值是数组且只有一个元素，并且这个元素不是数组中的第一级选项值，则尝试查找完整路径
        if (value.length === 1) {
            const singleValue = value[0];
            // 检查这个值是否是第一级选项的值
            const isFirstLevelValue = options?.some(option => option[getFiledName('value', names)] === singleValue);
            // 只有当不是第一级选项值时，才尝试查找完整路径
            if (!isFirstLevelValue) {
                // 查找匹配的节点路径
                const fullPath = findOptionsByPath(options, singleValue, [], names);
                // 如果找到了完整路径，使用它来设置选中项
                if (fullPath) {
                    return {
                        value: fullPath.map(option => option[getFiledName('value', names)]),
                        tempValue: fullPath.map(option => option[getFiledName('value', names)]),
                        selectedOptions: fullPath,
                        tempSelectedOptions: cloneLoop(fullPath),
                    };
                }
            }
        }
        value.forEach((valueItem: number | string) => {
            if (isArray(optionArr)) {
                const result = optionArr.filter(
                    (optionsItem: Option) => optionsItem[getFiledName('value', names)] === valueItem,
                );
                if (result.length !== 0) {
                    selectedOptions.push(result[0]);
                    optionArr = result[0][getFiledName('children', names)];
                }
            }
        });
    }
    return {
        value,
        tempValue: cloneLoop(value),
        selectedOptions,
        tempSelectedOptions: cloneLoop(selectedOptions),
    };
};

class Cascader extends React.Component<CascaderProps, States> {
    static contextType = GlobalConfigContext;
    context!: React.ContextType<typeof GlobalConfigContext>;

    static propTypes = {
        options: PropTypes.array,
        placeholder: PropTypes.string,
        expandTrigger: PropTypes.string,
        changeOnSelect: PropTypes.bool,
        onChange: PropTypes.func,
        clearable: PropTypes.bool,
        disabled: PropTypes.bool,
        loadData: PropTypes.func,
        filterable: PropTypes.bool,
        // size: PropTypes.string,
        customizeFilter: PropTypes.func,
        fieldNames: PropTypes.shape({
            value: PropTypes.string,
            label: PropTypes.string,
            disabled: PropTypes.string,
            children: PropTypes.string,
            isLeaf: PropTypes.string,
        }),
        placement: PropTypes.oneOf(innerPlacement),
        flip: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
        disablePortal: PropTypes.bool,
        children: PropTypes.node,
        clearIcon: PropTypes.node,
        dropdownClassName: PropTypes.string,
        notFoundContent: PropTypes.node,
        suffixIcon: PropTypes.node,
        open: PropTypes.bool,
        onDropdownVisibleChange: PropTypes.func,
        dropdownRender: PropTypes.func,
        autoFocus: PropTypes.bool,
        inline: PropTypes.bool,
        virtual: PropTypes.bool,
        displayRender: PropTypes.func,
        onBlur: PropTypes.func,
        onSearchLabelClick: PropTypes.func,
        notSearchFoundContent: PropTypes.node,
        filterOption: PropTypes.func,
        expandIcon: PropTypes.node,
        showCheckedStrategy: PropTypes.string,
        bordered: PropTypes.bool,
        defaultValue: PropTypes.arrayOf(PropTypes.number),
        popupContainer: PropTypes.oneOfType([PropTypes.instanceOf(Element), PropTypes.func]),
        virtualListProps: PropTypes.shape({
            height: PropTypes.number,
            itemHeight: PropTypes.number,
            threshold: PropTypes.string
        })
    };

    static defaultProps = {
        // value: [],
        defaultValue: undefined,
        options: [],
        placeholder: '',
        expandTrigger: 'click',
        changeOnSelect: false,
        onChange: () => { },
        loadData: undefined,
        customizeFilter: null,
        clearable: true,
        disabled: false,
        filterable: false,
        fieldNames: {
            value: 'value',
            label: 'label',
            disabled: 'disabled',
            children: 'children',
            isLeaf: 'isLeaf',
        },
        // size: 'normal',
        placement: undefined,
        flip: true,
        disablePortal: false,
        children: null,
        clearIcon: null,
        dropdownClassName: null,
        notFoundContent: null,
        suffixIcon: null,
        // open: false,
        onDropdownVisibleChange: null,
        dropdownRender: null,
        autoFocus: false,
        inline: false,
        virtual: false,
        displayRender: undefined,
        onBlur: undefined,
        onSearchLabelClick: undefined,
        notSearchFoundContent: undefined,
        filterOption: null,
        expandIcon: undefined,
        showCheckedStrategy: ShowCheckedStrategy.parent,
        bordered: true,
        popupContainer: undefined,
        virtualListProps: undefined
    };

    /**
     * 静态方法替代componentWillReceiveProps，计算由props变化导致的state更新
     * 这是一个纯函数，不应该有副作用
     * @param nextProps 新的props
     * @param prevState 当前state
     * @returns 需要更新的state或null
     */
    static getDerivedStateFromProps(nextProps: CascaderProps, prevState: States): Partial<States> | null {
        // 只在必要时更新状态
        const names = { ...defaultFieldNames, ...nextProps.fieldNames };
        const updateState: Partial<States> = {};
        const arrPropsOptions = getArrayData(nextProps.options);

        // 只在props中明确指定value时才更新value，避免覆盖内部状态更新
        const hasValueInProps = 'value' in nextProps;
        const value = hasValueInProps ? nextProps.value : prevState.value;

        // 只有在这些条件下才需要初始化options和selectedOptions:
        // 1. options发生变化
        // 2. value发生变化
        // 3. loadData + tempValue发生变化
        const isOptionsChanged = !isEqual(arrPropsOptions, prevState.options?.[0]);
        const isValueChanged = hasValueInProps && !isEqual(value, prevState.value);
        const shouldUpdateOptions = isOptionsChanged || isValueChanged;

        let selectedOptions = prevState.selectedOptions;
        let tempSelectedOptions = prevState.tempSelectedOptions;
        let stateTempValue = prevState.tempValue;
        let tempValue = prevState.tempValue;

        // 只有在需要时才重新计算这些值
        if (shouldUpdateOptions) {
            const initResult = init(arrPropsOptions, value, names);
            selectedOptions = initResult.selectedOptions;
            tempValue = initResult.tempValue;
            tempSelectedOptions = initResult.tempSelectedOptions;
        }

        // 处理inline模式下的特殊情况
        if (nextProps.inline && value?.length) {
            if (!stateTempValue?.length) {
                //inline下如果有value,没tempValue,更新tempValue值, 防止级联被收起
                stateTempValue = cloneLoop(value);
            } else if (!isEqual(value, stateTempValue)) {
                //inline下如果有value与tempValue不一致,更新tempValue值
                stateTempValue = cloneLoop(value);
            }
        }

        // 只在filterable开启且options改变时更新searchOptions
        if (isOptionsChanged && nextProps.filterable) {
            const { customizeFilter } = nextProps;
            const isExistCustomizeFilter = customizeFilter && typeof customizeFilter === 'function';
            if (isExistCustomizeFilter) {
                const newSearchOptions = initSearchOption(arrPropsOptions, names);
                if (!isEqual(newSearchOptions, prevState.searchOptions)) {
                    updateState.searchOptions = newSearchOptions;
                }
            }
        }
        
        // 只在必要时更新options
        if (shouldUpdateOptions) {
            const options = initOptions(arrPropsOptions, nextProps.loadData ? stateTempValue : value, names);
            if (!isEqual(options, prevState.options)) {
                updateState.options = options;
            }
        }

        // 只有在props中明确传入value且value改变时才更新value
        if (isValueChanged) {
            updateState.value = value;
        }
        
        // 针对loadData模式的tempValue处理
        const isValueEquals = nextProps.value !== value;
        const newTempValue = nextProps.loadData ? (isValueEquals ? tempValue : prevState.tempValue) : tempValue;
        if (!isEqual(newTempValue, prevState.tempValue)) {
            updateState.tempValue = newTempValue;
        }
        
        // 针对loadData模式的tempSelectedOptions处理
        const newTempSelectedOptions = nextProps.loadData ? (isValueEquals ? tempSelectedOptions : prevState.tempSelectedOptions) : tempSelectedOptions;
        if (!isEqual(newTempSelectedOptions, prevState.tempSelectedOptions)) {
            updateState.tempSelectedOptions = newTempSelectedOptions;
        }

        // 只有在selectedOptions改变时才更新
        if (!isEqual(selectedOptions, prevState.selectedOptions)) {
            updateState.selectedOptions = selectedOptions;
        }

        // 只有在存在open属性时才由props控制
        if ('open' in nextProps) {
            // 当props中有明确的open值时，它应该控制dropdownPanelDisplay
            const newDropdownPanelDisplay = nextProps.open && !nextProps.disabled ? 'block' : 'none';
            if (newDropdownPanelDisplay !== prevState.dropdownPanelDisplay) {
                updateState.dropdownPanelDisplay = newDropdownPanelDisplay;
            }
            // 保存当前open属性以便下次比较
            updateState.open = nextProps.open;
        }

        return Object.keys(updateState).length > 0 ? updateState : null;
    }

    private optionsItemIndex: number;

    private liIndex: number;

    private names: FieldNamesType;

    private readonly optionRenderer: (option: Option) => any;

    // selectPane 框体
    private selectRef: HTMLElement | null = null;

    private containerRef: React.RefObject<HTMLDivElement> = createRef();

    constructor(props: CascaderProps) {
        super(props);
        this.names = {
            ...{
                value: 'value',
                label: 'label',
                disabled: 'disabled',
                children: 'children',
                isLeaf: 'isLeaf',
            },
            ...this.props.fieldNames,
        };
        const { filterable } = this.props;
        const {
            value,
            tempValue,
            selectedOptions,
            tempSelectedOptions,
        } = this.init(this.props.options, this.props.value || this.props.defaultValue || []);
        const searchOptions = filterable ? initSearchOption(this.props.options, this.names) : [];
        const options = this.initOptions(this.getArrayData(this.props.options), this.props.value || this.props.defaultValue || []);
        this.state = {
            dropdownPanelDisplay: 'none',
            isMouseOver: false,
            options,
            value,
            tempValue,
            selectedOptions,
            tempSelectedOptions,
            inputValue: '',
            searchOptions,
            searchPanelDisplay: 'none',
            popperPortalVisible: false,
            focused: false,
            searchOptionsFilter: [],
            searchOptionsCurrentPage: 1,
            searchOptionsTotalPage: 0,
            isForceUpdate: false
        };
        this.optionsItemIndex = 0;
        this.liIndex = 0;
        this.optionRenderer = this.props.optionRenderer || (
            (option: Option) => option[this.getFiledName('label')]
        );
    }

    getArrayData = (data: any) => (isArray(data) ? data : [])

    getFiledName = (filed: 'children' | 'value' | 'label' | 'disabled' | 'isLeaf') => this.names[filed] || filed

    componentDidMount() {
        if (this.selectRef) {
            // const { width } = this.selectRef.getBoundingClientRect(); // 如果需要设置宽度，可释放开
            this.setState({
                // width,
                popperPortalVisible: true,
            });
        }
        const { open, onDropdownVisibleChange, disabled } = this.props;
        if (open && !disabled) {
            this.setState({ dropdownPanelDisplay: 'block' });
            if (isFunction(onDropdownVisibleChange)) onDropdownVisibleChange(true);
        }
    }

    componentDidUpdate() {
        if (this.state.dropdownPanelDisplay) {
            const { options } = this.state;
            if (!options || !Array.isArray(options)) return;
            options.forEach(optionsItem => {
                if (!optionsItem || !Array.isArray(optionsItem)) return;
                optionsItem.forEach(option => {
                    if (option) {
                        const value = option[this.getFiledName('value')];
                        const ele = document.getElementById(`${value}-select`);
                        if (ele) { scrollIntoParentView(ele); }
                    }
                });
            });
        }
    }

    // 初始化选择值以及选择项
    init = (options?: Option[], value?: Array<string | number>) => {
        const selectedOptions: Option[] = [];
        let optionArr: Option[] | undefined = cloneLoop(options);
        if (isArray(value)) {
            // 如果值是数组且只有一个元素，并且这个元素不是数组中的第一级选项值，则尝试查找完整路径
            if (value.length === 1) {
                const singleValue = value[0];
                // 检查这个值是否是第一级选项的值
                const isFirstLevelValue = options?.some(option => option[this.getFiledName('value')] === singleValue);
                // 只有当不是第一级选项值时，才尝试查找完整路径
                if (!isFirstLevelValue) {
                    // 查找匹配的节点路径
                    const fullPath = findOptionsByPath(options, singleValue, [], this.names);
                    // 如果找到了完整路径，使用它来设置选中项
                    if (fullPath) {
                        return {
                            value: fullPath.map(option => option[this.getFiledName('value')]),
                            tempValue: fullPath.map(option => option[this.getFiledName('value')]),
                            selectedOptions: fullPath,
                            tempSelectedOptions: cloneLoop(fullPath),
                        };
                    }
                }
            }
            value.forEach((valueItem: string | number) => {
                if (isArray(optionArr)) {
                    const result = optionArr.filter((optionsItem: Option) => optionsItem[this.getFiledName('value')] === valueItem);
                    if (result.length !== 0) {
                        selectedOptions.push(result[0]);
                        optionArr = result[0][this.getFiledName('children')];
                    }
                }
            });
        }
        return {
            value,
            tempValue: cloneLoop(value),
            selectedOptions,
            tempSelectedOptions: cloneLoop(selectedOptions),
        };
    };

    // 初始化渲染的下拉列表
    initOptions = (options?: Option[], value?: Array<string | number>) => {
        // 如果值是数组且只有一个元素，并且这个元素不是数组中的第一级选项值，则尝试查找完整路径
        if (isArray(value) && value.length === 1) {
            const singleValue = value[0];
            // 检查这个值是否是第一级选项的值
            const isFirstLevelValue = options?.some(option => option[this.getFiledName('value')] === singleValue);
            // 只有当不是第一级选项值时，才尝试查找完整路径
            if (!isFirstLevelValue) {
                const fullPath = findOptionsByPath(options, singleValue, [], this.names);
                // 如果找到了完整路径，使用它来初始化选项
                if (fullPath) {
                    const result: Option[][] = [cloneLoop(options) || []];
                    // 根据找到的路径构建嵌套选项
                    for (let i = 0; i < fullPath.length - 1; i++) {
                        const children = fullPath[i][this.getFiledName('children')];
                        if (children && children.length) {
                            result.push(children);
                        }
                    }
                    return result;
                }
            }
        }
        const initOptionsArray: any = options ? [options] : options;
        const arrValue = this.getArrayData(value);
        arrValue.forEach((valueItem: string | number) => {
            const result = (this.getArrayData(options))?.filter((optionItem: Option) => optionItem[this.getFiledName('value')] === valueItem) || [];
            if (result.length !== 0 && result[0][this.getFiledName('children')]) {
                initOptionsArray.push(result[0][this.getFiledName('children')]);
                options = result[0][this.getFiledName('children')];
            }
        });
        return initOptionsArray;
    };

    // 渲染更新下一级列表
    /**
     * @param item 当前option选项
     * @param optionsItemIndex 当前optionsItem下标
     * @param needLiIndex 是否需要liIndex进行比对(loadData传true,防止因异步选项展示错误)
     */
    updateOptions = (item: Option, optionsItemIndex: number, needLiIndex?: boolean) => {
        if (needLiIndex) {
            this.state.options.forEach((optionsItem, index) => {
                if (index === optionsItemIndex) {
                    optionsItem.forEach((optionVal, valIndex) => {
                        if (valIndex === this.liIndex && optionVal.value === item.value) {
                            this.state.options[optionsItemIndex + 1] = item[this.getFiledName('children')] || [];
                        }
                    });
                }
            });
        } else {
            // 当前选中位置之后的元素都清空
            this.state.options.splice(optionsItemIndex + 1);
            // 当前选中位置之后插入新的子列
            this.state.options.push(item[this.getFiledName('children')] || []);
        }

        // 直接调用setState，触发React重新渲染
        this.setState(prevState => ({
            options: [...prevState.options]
        }));
    };

    // 获得临时选中的值和选项
    getTempSelect = (item: Option, optionsItemIndex: number) => {
        const { tempValue = [], tempSelectedOptions } = this.state;
        // 当前选中位置之后的元素都清空
        tempValue.splice(optionsItemIndex);
        // 当前选中位置之后插入新的子列
        tempValue.push(item[this.getFiledName('value')]);
        // 当前选中位置之后的元素都清空
        tempSelectedOptions.splice(optionsItemIndex);
        // 当前选中位置之后插入新的子列
        tempSelectedOptions.push(item);
                
        return {
            tempValue,
            tempSelectedOptions,
        };
    };

    /**
     * 开启级联panel
     * @param isSearchChange 是否为搜索变更触发
     */
    showDropdownPanel = (isSearchChange?: boolean) => {
        const { value, tempValue } = this.state;
        const { onDropdownVisibleChange } = this.props;
        const options = this.initOptions(this.props.options, value);
        const newStates: any = {
            options,
            dropdownPanelDisplay: 'block',
        };
        // 只有在组件是受控模式时才更新open状态
        if ('open' in this.props) {
            newStates.open = true;
        }
        //如果有value,没tempValue,更新tempValue值
        if (value && value.length && (!tempValue || !tempValue.length)) {
            newStates.tempValue = cloneLoop(value);
        }
        //  异步获取menu数据，展示时重新赋值
        this.setState(newStates);
        // 即使是受控模式，也需要通知父组件更新 open 状态
        if (isFunction(onDropdownVisibleChange) && !isSearchChange) onDropdownVisibleChange(true);
    };

    // 隐藏级联panel
    hideDropdownPanel = (isSearchChange = false) => {
        const { dropdownPanelDisplay } = this.state;
        const { open, onDropdownVisibleChange } = this.props;
        if (dropdownPanelDisplay === 'none') return;
        // 将面板回归到上一次确定选择的结果
        const options = this.initOptions(this.props.options, this.state.value);
        // 创建新状态对象
        const newState: any = {
            tempSelectedOptions: cloneLoop(this.state.selectedOptions),
            tempValue: cloneLoop(this.state.value),
            options,
            dropdownPanelDisplay: open && !isSearchChange ? 'block' : 'none',
        };
        // 只有在组件是受控模式时才更新open状态
        if ('open' in this.props) {
            newState.open = open && !isSearchChange;
        }
        // 开启搜索并open
        this.setState(newState);
        if (isFunction(onDropdownVisibleChange) && !isSearchChange) onDropdownVisibleChange(false);
    };

    // 开启筛选级联panel
    showSearchPanel = () => {
        this.setState({
            searchPanelDisplay: 'block',
        });
    };

    // 隐藏筛选级联panel
    hideSearchPanel = (isSearchChange = false) => {
        const { searchPanelDisplay } = this.state;
        const { open } = this.props;
        if (searchPanelDisplay === 'none') return;
        this.setState({
            searchPanelDisplay: open && !isSearchChange ? 'block' : 'none',
        });
    };

    // 过滤筛选列表 (支持 customizeFilter 异步获取数据)
    filterSearchOptions = (searchOptions: SearchOption[]) => {
        const { inputValue } = this.state;
        try {
            const {
                customizeFilter, fieldNames, changeOnSelect, options = [], filterOption
            } = this.props;
            if (customizeFilter && typeof customizeFilter === 'function') {
                const customizeFilterData: any = customizeFilter(inputValue, searchOptions);
                if (isThenable(customizeFilterData)) {
                    customizeFilterData.then((data: any) => { this.setState({ searchOptionsFilter: data || [], searchOptionsTotalPage: Math.ceil(data?.length / searchOptionsPageSize), searchOptionsCurrentPage: 1 }); });
                } else {
                    this.setState({ searchOptionsFilter: customizeFilterData as SearchOption[] || [], searchOptionsTotalPage: Math.ceil(customizeFilterData?.length / searchOptionsPageSize), searchOptionsCurrentPage: 1 });
                }
            } else {
                const searchOptionsFilter = useSearchOptions({
                    searchValue: inputValue,
                    fieldNames: fieldNames as any,
                    changeOnSelect: changeOnSelect as any,
                    options,
                    config: {
                        filter: isFunction(filterOption) ? filterOption : undefined
                    }
                });
                this.setState({ searchOptionsFilter, searchOptionsTotalPage: Math.ceil(searchOptionsFilter?.length / searchOptionsPageSize), searchOptionsCurrentPage: 1 });
            }
        } catch (e) {
            console.error('Warning: customizeFilter 函数逻辑实现报错，请检查', e);
        }
    };

    // 获取展示在input中文案
    getDisplayText = () => {
        const { displayRender, showCheckedStrategy } = this.props;
        const { selectedOptions } = this.state;
        if (isFunction(displayRender)) {
            return displayRender(selectedOptions);
        }
        if (showCheckedStrategy === ShowCheckedStrategy.child && selectedOptions[selectedOptions.length - 1]) {
            return selectedOptions[selectedOptions.length - 1][this.getFiledName('label')];
        }
        return selectedOptions.map(item => item[this.getFiledName('label')]).join('/');
    };

    isLeaf = (item: Option) => {
        if (item[this.getFiledName('isLeaf')]) {
            return true;
        } else {
            if (item[this.getFiledName('isLeaf')] === undefined) {
                return (!item[this.getFiledName('children')] || item[this.getFiledName('children')].length === 0);
            } else {
                return false;
            }
        }
    };

    getItemIcon = (item: Option) => {
        const { expandIcon } = this.props;
        const className = `${this.context.prefixCls}-cascader-expand-icon`;
        if (item.loading) {
            return (
                <Icon
                    name="loading"
                    className={className}
                />
            );
        }
        return this.isLeaf(item) ? null
            : (
                <div className={className}>
                    {
                        expandIcon || (
                            <Icon
                                name="chevron-right-new"
                            />
                        )
                    }
                </div>
            );
    };

    defaultNotFoundContent = () => <Empty title={locale.lng('Cascader.noData') as string} />;

    handleClickOutside = (event: MouseEvent) => {
        if (this.selectRef && this.selectRef.contains(event.target as HTMLElement)) {
            return;
        }
        this.hideDropdownPanel();
        this.hideSearchPanel();
        this.handleOnInputBlur();
    };

    handleOnSearchPanelClick = (optionsItem: SearchOption, index: number) => {
        const { value, selectedOptions } = optionsItem;
        const {
            open, onChange, onDropdownVisibleChange, onSearchLabelClick
        } = this.props;
        const options = this.initOptions(this.props.options, value);
        this.setState({
            options,
            tempValue: cloneLoop(value),
            value,
            tempSelectedOptions: cloneLoop(selectedOptions),
            selectedOptions,
            searchPanelDisplay: open ? 'block' : 'none',
            inputValue: '',
        });
        this.handleOnInputBlur();
        if (isFunction(onChange)) {
            onChange(value, selectedOptions);
        }
        if (isFunction(onSearchLabelClick)) {
            onSearchLabelClick(value, index);
        }
        if (isFunction(onDropdownVisibleChange) && !open) onDropdownVisibleChange(false);
    };

    updateLoadDataState = (item: any, optionsItemIndex: number, isNeedLiIndex?: boolean) => {
        this.optionsItemIndex = optionsItemIndex;
        this.updateOptions(item, optionsItemIndex, isNeedLiIndex);
    };

    handleOnItemClick = (item: Option, liIndex: number, optionsItemIndex: number) => {
        if (!item.disabled) {
            const {
                changeOnSelect,
                expandTrigger,
                loadData,
                onChange,
                open,
                onDropdownVisibleChange,
                inline
            } = this.props;
            this.handleOnInputBlur();

            const isLeaf = this.isLeaf(item);
            
            // 非叶子节点并且，无children或者children长度为0的
            if (!isLeaf && (!item[this.getFiledName('children')] || item[this.getFiledName('children')].length === 0)) {
                if (loadData) {
                    // 加载数据前保存节点状态
                    const originalChildren = item[this.getFiledName('children')];
                    
                    // 执行loadData加载数据
                    const loadResult: any = loadData(item);
                    
                    // 检查loadData是否同步修改了item
                    const hasBeenModified = originalChildren !== item[this.getFiledName('children')];
                    
                    if (isThenable(loadResult)) {
                        // 异步处理
                        loadResult.then(() => {
                            this.liIndex = liIndex;
                            // 更新下级
                            this.updateOptions(item, optionsItemIndex, true);
                            // 更新临时选择
                            const { tempValue, tempSelectedOptions } = this.getTempSelect(item, optionsItemIndex);
                            this.setState({
                                tempValue,
                                tempSelectedOptions,
                            });
                        });
                    } else {
                        // 同步处理
                        this.liIndex = liIndex;
                        // 更新下级
                        this.updateOptions(item, optionsItemIndex, true);
                        // 更新临时选择
                        const { tempValue, tempSelectedOptions } = this.getTempSelect(item, optionsItemIndex);
                        this.setState({
                            tempValue,
                            tempSelectedOptions,
                        });
                    }
                    
                    // 如果loadData是同步的且已修改item，提前返回，避免执行后面的逻辑
                    if (!isThenable(loadResult) && hasBeenModified) {
                        return;
                    }
                }
            }
            
            // 更新下级
            this.updateOptions(item, optionsItemIndex);
            // 更新临时选择
            const { tempValue, tempSelectedOptions } = this.getTempSelect(item, optionsItemIndex);
            const isValueInProps = 'value' in this.props;
            /***
                 * 判断是否是最后一级
                 * 如果是，更新临时选择、最终选择，并收起面板
                 * 如果不是，更新临时选择。
                 */
            if (isLeaf) {
                let newStates: any = {
                    dropdownPanelDisplay: open ? 'block' : 'none',
                    tempValue: []
                };
                if (!isValueInProps) {
                    newStates = {
                        ...newStates,
                        value: cloneLoop(tempValue),
                        tempValue,
                        tempSelectedOptions,
                        selectedOptions: cloneLoop(tempSelectedOptions),
                    };
                }
                this.setState(newStates, () => {
                    if (isFunction(onChange)) {
                        onChange(cloneLoop(tempValue) || [], cloneLoop(tempSelectedOptions));
                    }
                });
                if (!inline && isFunction(onDropdownVisibleChange) && !open) onDropdownVisibleChange(false);
            } else {
                this.liIndex = liIndex;
                if (changeOnSelect) {
                    const { tempValue: tempValueState, tempSelectedOptions: tempSelectedOptionsState } = this.state;
                    this.setState({
                        value: cloneLoop(tempValueState),
                        selectedOptions: cloneLoop(tempSelectedOptionsState),
                        dropdownPanelDisplay: expandTrigger === 'click' || open ? 'block' : 'none',
                    });
                    if (isFunction(onChange)) {
                        onChange(cloneLoop(tempValueState) || [], cloneLoop(tempSelectedOptionsState));
                    }
                    if (isFunction(onDropdownVisibleChange) && !(expandTrigger === 'click' || open)) onDropdownVisibleChange(false);
                } else {
                    this.setState({
                        tempValue,
                        tempSelectedOptions,
                    });
                }
            }
        }
    };

    handleOnItemMouseOver = (item: Option, liIndex: number, optionsItemIndex: number) => {
        if (!item.disabled) {
            const { expandTrigger } = this.props;
            const { loadData } = this.props;
            // click状态下，什么都不更新
            if (expandTrigger === 'hover') {
                // 非叶子节点并且，无children或者children长度为0的
                if (!this.isLeaf(item) && (!item[this.getFiledName('children')] || item[this.getFiledName('children')].length === 0)) {
                    if (loadData) {
                        const loadResult = loadData(item);
                        if (isThenable(loadResult)) {
                            (loadResult as Promise<any>).then(() => {
                                this.updateLoadDataState(item, optionsItemIndex, true);
                                const { tempValue, tempSelectedOptions } = this.getTempSelect(item, optionsItemIndex);
                                this.setState({
                                    tempValue,
                                    tempSelectedOptions,
                                });
                            });
                        }
                    }
                }
                this.liIndex = liIndex;
                this.updateLoadDataState(item, optionsItemIndex);
                const { tempValue, tempSelectedOptions } = this.getTempSelect(item, optionsItemIndex);
                this.setState({
                    tempValue,
                    tempSelectedOptions,
                });
            }
        }
    };

    handleOnInputClick = () => {
        if (!this.props.disabled) {
            // 两个面板都不显示的情况下，打开下拉列表
            if (this.state.dropdownPanelDisplay === 'none' && this.state.searchPanelDisplay === 'none') {
                this.showDropdownPanel();
            } else {
                this.hideDropdownPanel();
            }
        }
    };

    handleOnInputMouseOver = () => {
        if (!this.props.disabled) {
            this.setState({
                isMouseOver: true,
            });
        }
    };

    handleOnInputMouseOut = () => {
        if (!this.props.disabled) {
            this.setState({
                isMouseOver: false,
            });
        }
    };

    handleOnIconClick = (e: React.MouseEvent<HTMLDivElement>) => {
        const { options } = this.state;
        // 可清除，并且有选中
        if (this.props.clearable && this.state.selectedOptions.length !== 0) {
            e.stopPropagation();
            this.setState({
                selectedOptions: [],
                tempSelectedOptions: [],
                tempValue: [],
                value: [],
                options: [options[0]],
                inputValue: '',
            }, () => {
                this.handleClear();
            });
        }
    };

    handleClear = () => {
        const {
            onChange
        } = this.props;

        if (isFunction(onChange)) {
            onChange([], []);
        }
    }

    handleOnInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const inputValue = e.target.value;
        this.setState({ inputValue }, () => {
            if (this.props.filterable) {
                if (inputValue !== '') {
                    // 搜索change事件触发，open情况下应关闭 DropdownPanel
                    this.hideDropdownPanel(true);
                    this.showSearchPanel();
                } else { // 输入的为空的时候,展开下拉级联列表
                    this.showDropdownPanel(true);
                    this.hideSearchPanel(true);
                }
                this.filterSearchOptions(this.state.searchOptions);
            }
        });
    };

    handleOnInputFocus = () => {
        this.setState({
            focused: true,
        });
    }

    handleOnInputBlur = () => {
        this.setState({
            focused: false,
            inputValue: '',
        });
    }

    handleBlur = (e: React.FocusEvent) => {
        const { onBlur } = this.props;
        if (isFunction(onBlur)) onBlur(e);
    }

    handleSearchScroll = (event: any) => {
        const target = event.target;
        const { searchOptionsTotalPage, searchOptionsCurrentPage } = this.state;
        if (searchOptionsCurrentPage < searchOptionsTotalPage && target && target.scrollHeight - target.scrollTop < (target.clientHeight as number + 150)) {
            this.setState(prevState => ({ searchOptionsCurrentPage: prevState.searchOptionsCurrentPage + 1 }));
        }
    };

    // 搜索关键词高亮
    _renderHighlightText(label: string, keyword: string) {
        return label.split(keyword).map((part, index) => {
            if (index !== 0) {
                return (
                    <>
                        <span style={{ color: $btnBrand }}>{keyword}</span>
                        {part}
                    </>
                );
            }
            return part;
        });
    }

    renderNoSearchContent = () => {
        const { notSearchFoundContent } = this.props;
        if (notSearchFoundContent) {
            return (
                <li>
                    {notSearchFoundContent}
                </li>
            );
        }
        return (
            <li>
                <Link
                    className="disabled"
                >
                    {/* 无匹配数据 */}
                    {locale.lng('Cascader.noMatch')}
                </Link>
            </li>
        );
    }

    // 渲染筛选级联
    renderSearchOptionPanel = () => {
        const { inputValue, searchOptionsFilter, searchOptionsCurrentPage } = this.state;
        const searchVisibleOptions = searchOptionsFilter.slice(0, searchOptionsCurrentPage * searchOptionsPageSize);
        if (this.props.filterable) {
            return (
                <div
                    className={`${this.context.prefixCls}-dropdown-menu-cascader ${this.context.prefixCls}-dropdown-menu-cascader-search`}
                    style={{ position: 'initial' }}
                >
                    <ul
                        className={`${this.context.prefixCls}-dropdown-menu`}
                        style={{ display: this.state.searchPanelDisplay }}
                        onScroll={this.handleSearchScroll}
                    >
                        {
                            searchVisibleOptions.length === 0 ? this.renderNoSearchContent() : (
                                searchVisibleOptions.map((optionsItem: SearchOption, index: number) => (
                                    <li
                                        key={`${optionsItem.label}-${Math.random().toString(36).substr(3)}`}
                                        onClick={this.handleOnSearchPanelClick.bind(this, optionsItem, index)}
                                    >
                                        <Link>{this._renderHighlightText(optionsItem.label, inputValue)}</Link>
                                    </li>
                                ))
                            )
                        }
                    </ul>
                </div>
            );
        } else {
            return null;
        }
    };

    renderItemLi = (item: Option, index: number, optionsItemIndex: number) => {
        const { dropdownMenuColumnStyle = {} } = this.props;
        let className = '';
        const itemValue = item[this.getFiledName('value')];
        const active = this.state.tempValue && this.state.tempValue[optionsItemIndex] === itemValue;
        const isLeaf = this.isLeaf(item);

        if (item[this.getFiledName('disabled')]) {
            className = 'disabled';
        } else if (active) {
            className = 'active-highlight';
        }
        if (!isLeaf) {
            className = `${className} withoutAfter`;
        }
        return (
            <li
                key={itemValue}
                id={`${itemValue}-${active ? 'select' : 'unselected'}`}
                onMouseOver={this.handleOnItemMouseOver.bind(this, item, index, optionsItemIndex)}
                onFocus={() => { }}
                onClick={this.handleOnItemClick.bind(this, item, index, optionsItemIndex)}
            >
                <Link
                    className={cls(
                        'overflow-hidden',
                        `${this.context.prefixCls}-cascader-option-item`,
                        className
                    )}
                    style={{ ...dropdownMenuColumnStyle }}
                >
                    <div
                        title={item[this.getFiledName('label')]}
                        className="cascader-text"
                    >
                        {this.optionRenderer!(item)}
                    </div>
                    {this.getItemIcon(item)}
                </Link>
            </li>
        );
    };

    /* eslint-disable */
    renderSuffixIcon = () => {
        const {
            disabled, children, clearable, clearIcon, suffixIcon,
        } = this.props;
        if (disabled || children) return null;
        let iconClearRender = null;
        let iconSuffixRender = null;
        let iconClassName = 'roo-icon addon-icon ';
        if (this.state.isMouseOver) {
            if (!clearable || this.state.selectedOptions.length === 0) {
                if (suffixIcon) {
                    iconSuffixRender = (<span className="roo-icon addon-icon">{suffixIcon}</span>);
                }
                iconClassName += 'roo-icon-chevron-down-new';
                return suffixIcon ? iconSuffixRender : (
                    <i
                        className={iconClassName}
                        onClick={this.handleOnIconClick}
                    />
                );
            } else {
                if (clearIcon) {
                    iconClearRender = (
                        <span
                            className="roo-icon addon-icon"
                            onClick={this.handleOnIconClick}
                        >{clearIcon}
                        </span>
                    );
                }
                iconClassName += 'roo-icon-times-circle-new';
                return clearIcon ? iconClearRender : (
                    <i
                        className={iconClassName}
                        onClick={this.handleOnIconClick}
                    />
                );
            }
        } else {
            if (suffixIcon) {
                iconSuffixRender = (<span className="roo-icon addon-icon">{suffixIcon}</span>);
            }
            iconClassName += 'roo-icon-chevron-down-new';
            return suffixIcon ? iconSuffixRender : (
                <i
                    className={iconClassName}
                    onClick={this.handleOnIconClick}
                />
            );
        }
    }

    renderOptionItem = (optionsItem: Option[], optionsItemIndex: number) => {
        const { virtual, virtualListProps } = this.props;
        const { height = 180, itemHeight = 36, threshold = 100 } = virtualListProps || {};
        if (virtual) {
            return (
                <VirtualList
                    renderItem={(item, index) => this.renderItemLi(item, index, optionsItemIndex)}
                    height={height}
                    itemHeight={itemHeight}
                    itemKey={this.getFiledName('value')}
                    data={optionsItem}
                    virtual={isNull(threshold) ? false : optionsItem.length >= threshold}
                />
            )
        }
        return optionsItem?.map((item: Option, index) => this.renderItemLi(item, index, optionsItemIndex));
    }

    // 渲染级联列表
    renderDropdownPanel = (options: Option[][]) => {
        const { notFoundContent, dropdownRender, inline, virtual } = this.props;
        let styleUl: object = { display: inline ? 'block' : this.state.dropdownPanelDisplay };
        let styleDiv: object = inline ? { position: 'initial', boxShadow: 'none' } : { position: 'initial' };
        // 自定义下拉框内容触发，修复部分样式（dom结构问题, 需样式覆盖解决）
        if (isFunction(dropdownRender)) {
            styleDiv = { ...styleDiv, boxShadow: 'none' };
            styleUl = { ...styleUl, borderRadius: '2px 2px 0 0' };
        }
        return (
            <div
                className={`${this.context.prefixCls}-dropdown-menu-cascader`}
                style={styleDiv}
                ref={this.containerRef}
            >
                {
                    options?.map((optionsItem: Option[], optionsItemIndex: number) => (isArray(optionsItem) && optionsItem.length > 0
                        ? (
                            <ul
                                key={optionsItemIndex}
                                className={cls(
                                    `${this.context.prefixCls}-dropdown-menu`,
                                    {
                                        [`${this.context.prefixCls}-dropdown-menu-inline`]: inline,
                                        [`${this.context.prefixCls}-dropdown-menu-virtual`]: virtual
                                    }
                                )}
                                style={styleUl}
                            >
                                {
                                    this.renderOptionItem(optionsItem, optionsItemIndex)}
                            </ul>
                        ) : null))
                }
                {
                    options?.every((item: Option[]) => item.length === 0) && (
                        <ul
                            className={`${this.context.prefixCls}-dropdown-menu ${inline ? `${this.context.prefixCls}-dropdown-menu-inline` : ''}`}
                            style={{ display: inline ? 'block' : this.state.dropdownPanelDisplay }}
                        >
                            <li>
                                {notFoundContent || this.defaultNotFoundContent()}
                            </li>
                        </ul>

                    )
                }
            </div>
        );
    };

    renderPanel = (dropdownRender: Function | null) => {
        const { inline } = this.props;
        let stylePanel: object = { display: inline ? 'block' : this.state.dropdownPanelDisplay };
        if (isFunction(dropdownRender)) stylePanel = { ...stylePanel, background: $light, boxShadow: '0px 10px 16px 0px rgb(88 90 110 / 15%)' };
        return (
            <div style={stylePanel}>
                {
                    isFunction(dropdownRender) && dropdownRender(this.renderDropdownPanel(this.state.options))
                }
                {
                    !isFunction(dropdownRender) && this.renderDropdownPanel(this.state.options)
                }
            </div>
        );
    }

    getWrapperCls() {
        const clsArr = [`${this.context.prefixCls}-cascader`, `${this.context.prefixCls}-input-group`, 'has-icon'];
        const { className } = this.props;
        if (className) clsArr.push(className);
        return clsArr;
    }

    renderInputStatus() {
        const {
            inputStatus,
            message,
            style,
        } = this.props;
        const groupCls = cls(this.getWrapperCls(), {
            'has-error': inputStatus === 'error',
            'has-success-hook': inputStatus === 'success',
            'has-success-color': inputStatus === 'success',
        });
        return (
            <div onMouseOver={this.handleOnInputMouseOver}
                onMouseOut={this.handleOnInputMouseOut}
                onClick={this.handleOnInputClick}
            >
                <div
                    className={groupCls}
                    style={style}
                >
                    {this.renderInput()}
                    {this.renderSuffixIcon()}
                </div>
                {(inputStatus === 'error' && message) ? <p className="help-block">{message}</p> : null}
            </div>
        );
    }

    renderNormal() {
        const { children, style } = this.props;
        return <div
            onMouseOver={this.handleOnInputMouseOver}
            onMouseOut={this.handleOnInputMouseOut}
            onClick={this.handleOnInputClick}
            style={style}
            className={cls(this.getWrapperCls())}
        >
            {children || this.renderInput()}
            {this.renderSuffixIcon()}
        </div>
    }

    renderInput() {
        const displayText = this.getDisplayText();
        const {
            filterable, placeholder, autoFocus, bordered, inputType,
            size = this.context.size || 'normal'
        } = this.props;
        const {
            inputValue, focused
        } = this.state;
        const placeholderLocale = locale.lng('Select.placeholder') as string;
        return (
            <Input
                disabled={this.props.disabled}
                autoFocus={filterable && autoFocus}
                placeholder={(filterable && focused) ? (displayText || placeholder || placeholderLocale) : placeholder || placeholderLocale}
                value={(filterable && focused) ? inputValue : displayText}
                onChange={this.handleOnInputChange}
                onFocus={this.handleOnInputFocus}
                onBlur={this.handleBlur}
                bordered={bordered}
                styleType={inputType}
                size={size}
            />
        );
    }

    getPlacement = () => {
        const { placement } = this.props;
        if (placement) {
            return placementEnum[placement as placementTypes];
        } else {
            return this.context.direction === 'RTL' ? placementEnum.bottomRight : placementEnum.bottomLeft;
        }
    }

    render(): React.ReactNode {
        const {
            disablePortal, popupContainer, flip, dropdownClassName, dropdownRender, inline, inputStatus
        } = this.props;
        const { popperPortalVisible, isForceUpdate } = this.state;
        // inline直接渲染到页面中，不需要弹窗和输入框
        if (inline) {
            return (
                <div>
                    {
                        this.renderPanel(dropdownRender as Function | null)
                    }
                    {
                        this.renderSearchOptionPanel()
                    }
                </div>
            );
        }
        return (
            <div className="dropdown">
                <Manager>
                    <ReactResizeDetector
                        handleHeight
                        handleWidth
                        onResize={() => {
                            // 当输入框宽高变化时，下拉窗重新渲染
                            this.setState({
                                isForceUpdate: !this.state.isForceUpdate
                            });
                        }}
                    >
                        <Reference
                            innerRef={node => { this.selectRef = node; }}
                        >
                            {inputStatus ? this.renderInputStatus() : this.renderNormal()}
                        </Reference>

                    </ReactResizeDetector>
                    <PopperPortal
                        zIndex={1000}
                        disablePortal={disablePortal}
                        visible={popperPortalVisible}
                        modifiers={[
                            {
                                name: 'flip',
                                enabled: flip,
                                options: {
                                    flipVariations: flip === 'flip',
                                }
                            },
                        ]}
                        className={dropdownClassName}
                        placement={this.getPlacement()}
                        onClickOutside={this.handleClickOutside}
                        container={popupContainer}
                        key={isForceUpdate ? 'force-update' : 'normal'}
                    >
                        <div>
                            {
                                this.renderPanel(dropdownRender as Function | null)
                            }
                            {
                                this.renderSearchOptionPanel()
                            }
                        </div>
                    </PopperPortal>
                </Manager>
            </div>
        );
    }
}

export default withDisabled<typeof Cascader>(Cascader);
