{"name": "@roo/roo", "version": "1.18.5", "description": "<PERSON><PERSON>（袋鼠UI）是外卖前端团队全力打造的一款适用于外卖业务的前端组件库，其宗旨在于提升组件应用质量和业务开发效率。", "license": "ISC", "scripts": {"start": "npm run version && npm run delDoc && node scripts/build-theme.js && npx gulp copy:css && npx docm dev --ts --propsParser=true", "dev": "npm run version && npm run delDoc && node scripts/build-theme.js && npx gulp copy:css && npx docm dev --ts --propsParser=false", "delDoc": "npx del-cli ./.docz", "pre-commit": "npx jest ; npm run eslint ; npm run deploy", "eslint": "npx eslint 'src/**/*.{tsx,js,jsx}' ; npx eslint 'tests/**/*.js'", "lint": "npx eslint --config .eslintrc.js --ext .js,.tsx,.jsx,.ts src", "lint:fix": "eslint 'src/**/*{.ts,.tsx}' -c ./.eslintrc.js --fix", "test": "jest", "test:ui": "vitest --ui --coverage -u", "test:image": "node ./tests/__image__/utils/start.js & jest --config jest.image.js --no-cache -i", "testOne": "npx jest ./tests/Button/main.test.js  --coverage=false  --detectO<PERSON>Handles", "newtest": "npm run version && vitest run  --no-cache --coverage  -u ", "lint-staged": "lint-staged", "ci": "jest --changed<PERSON><PERSON><PERSON> master --ci", "deploy": "bash ./scripts/roo-deploy.sh", "build:deploy": "bash ./scripts/roo-deploy.sh", "build": "rm -rf build && npm i && npm run version && node scripts/build-theme.js && gulp compile --max_old_space_size=32000", "copyFile": "gulp copy:file", "dist": "npx webpack --config webpack.config.js", "pub:next": "mnpm publish --tag next", "pub:beta": "mnpm publish --tag beta", "pub": "npm run tag && mnpm publish --latest", "publish2cdn": "node ./scripts/publish.js", "view:tag": "mnpm dist-tag ls $npm_package_name", "tag": "git tag v$npm_package_version && git push --tags", "genCon": "node ./scripts/contributor.js", "version": "node ./scripts/generate-version", "uploadCDN": "node ./scripts/uoload-cdn.js && npx stark-uploader --appkey 4794067a --static '**/*.* !**/*.map' ./build/cdn", "developCmp": "cd ./dev && npm run dev", "buildWebsite": "PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true npm run build && npx gulp copy:css && npx docm build --ts --propsParser=true  && node ./scripts/removePrefetch.js && node ./scripts/website-build.js"}, "lint-staged": {"**/*.{js,jsx, ts, tsx}": ["jest --findRelatedTests"]}, "main": "./index.js", "module": "./es/index.js", "husky": {"hooks": {"pre-commit": "echo '🚥  执行 ESLint 检查:' && lint-staged && echo '🚥  执行 TypeScript 类型检查:' && tsc", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "repository": {"type": "git", "url": "ssh://*******************/wm/roo.git"}, "author": "<EMAIL>", "devDependencies": {"@babel/cli": "^7.18.10", "@babel/core": "^7.18.13", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.18.10", "@babel/plugin-proposal-do-expressions": "^7.18.6", "@babel/plugin-proposal-export-default-from": "^7.18.10", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-function-sent": "^7.18.6", "@babel/plugin-proposal-json-strings": "^7.18.6", "@babel/plugin-proposal-logical-assignment-operators": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-numeric-separator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-proposal-pipeline-operator": "^7.18.6", "@babel/plugin-proposal-throw-expressions": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.7.4", "@babel/plugin-syntax-import-meta": "^7.7.4", "@babel/plugin-transform-member-expression-literals": "^7.18.6", "@babel/plugin-transform-object-assign": "^7.18.6", "@babel/plugin-transform-property-literals": "^7.18.6", "@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.18.10", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@commitlint/cli": "^8.3.6", "@commitlint/config-conventional": "^17.0.3", "@mfe/precommit-eslint": "^2.1.5", "@mfe/stark-uploader": "^1.12.0", "@roo/docm": "1.0.2", "@roo/roo-docs": "0.39.9", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^12.1.2", "@testing-library/user-event": "^14.5.2", "@types/classnames": "^2.2.9", "@types/enzyme": "^3.10.11", "@types/hoist-non-react-statics": "^3.3.1", "@types/jest": "^27.1.4", "@types/lodash": "^4.14.184", "@types/prismjs": "^1.26.0", "@types/raf-schd": "^4.0.3", "@types/react-click-outside": "^3.0.3", "@types/react-dom": "^16.9.16", "@types/react-is": "^17.0.3", "@types/react-lifecycles-compat": "^3.0.1", "@types/react-router": "^5.1.18", "@types/react-router-dom": "^5.3.3", "@types/react-slick": "^0.23.4", "@types/react-transition-group": "^4.4.5", "@types/warning": "^3.0.0", "@typescript-eslint/eslint-plugin": "^2.10.0", "@typescript-eslint/parser": "^2.10.0", "@vitejs/plugin-react": "^4.3.0", "@vitest/coverage-v8": "^1.6.0", "@vitest/ui": "^1.6.0", "@wmfe/eslint-config-mt": "^0.3.3", "ansi-colors": "^4.1.3", "awesome-typescript-loader": "^5.2.1", "axios-mock-adapter": "^1.17.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^10.0.3", "babel-jest": "^27.1.4", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-dev-expression": "^0.2.2", "babel-plugin-inline-import-data-uri": "^1.0.1", "clean-webpack-plugin": "^3.0.0", "copy-webpack-plugin": "^5.0.5", "css-loader": "^3.2.0", "del-cli": "^3.0.0", "enzyme": "^3.9.0", "enzyme-adapter-react-16": "^1.15.1", "eslint": "6.8.0", "eslint-config-airbnb": "^18.0.1", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-react": "^7.30.1", "eslint-plugin-typescript": "^0.12.0", "file-loader": "^5.0.2", "gulp": "^4.0.2", "gulp-babel": "^8.0.0", "gulp-insert": "^0.5.0", "gulp-json-editor": "^2.5.4", "gulp-replace": "^1.0.0", "gulp-strip-code": "^0.1.4", "gulp-typescript": "^5.0.1", "husky": "^3.1.0", "identity-obj-proxy": "3.0.0", "immutability-helper": "^3.1.1", "jest": "^27.1.4", "jest-environment-jsdom": "^27.1.4", "jest-environment-puppeteer": "^6.1.1", "jest-html-reporters": "^1.2.0", "jest-image-snapshot": "^6.2.0", "jest-puppeteer": "^6.1.1", "jest-transform-css": "^2.0.0", "jsdom": "^16.7.0", "less-loader": "^5.0.0", "lint-staged": "^9.5.0", "mockjs": "^1.1.0", "mos-mss": "^1.1.1", "mss-sdk": "^2.0.0", "node-notifier": "^10.0.1", "prettier": "^2.0.0", "puppeteer": "^19.0.0", "raw-loader": "^4.0.0", "react": "^16.13.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^16.13.1", "react-fast-marquee": "^1.2.1", "react-highlight": "^0.12.0", "react-sticky-box": "^2.0.5", "request": "^2.88.0", "style-loader": "^1.0.1", "through2": "^3.0.1", "ts-jest": "^27.1.4", "ts-loader": "^6.2.1", "tsconfig-paths": "^3.14.1", "tslint": "^5.20.1", "tslint-config-prettier": "^1.18.0", "typescript": "^3.7.3", "typescript-eslint-parser": "^22.0.0", "uglifyjs-webpack-plugin": "^2.2.0", "url-loader": "^4.1.1", "vitest": "^1.6.0", "webpack": "^4.28.4", "webpack-cli": "^3.3.10", "yargs": "^15.0.2"}, "dependencies": {"@ai/mss-upload-js": "^1.1.7", "@babel/runtime-corejs2": "^7.18.9", "@popperjs/core": "^2.11.5", "@rc-component/trigger": "^2.2.5", "@roo/create-react-ref": "0.0.2", "@roo/react-color": "^1.0.2", "@roo/roo-theme-var": "^1.4.5", "@utiljs/clone": "^0.2.8", "@utiljs/cookie": "^0.1.6", "@utiljs/dom": "^0.2.6", "@utiljs/functional": "^0.6.5", "@utiljs/guid": "^0.5.7", "@utiljs/is": "^0.11.10", "@utiljs/param": "^0.6.11", "@utiljs/type": "^0.5.5", "@utiljs/use-request": "^0.2.5-beta.35", "@wangeditor/editor": "^5.1.23", "@yyfe/Copy": "^1.0.21", "async-validator": "^1.10.0", "axios": "^0.18.0", "classnames": "^2.2.6", "cropperjs": "^1.5.13", "dayjs": "1.11.13", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "^3.4.1", "hoist-non-react-statics": "^3.3.1", "lodash": "^4.17.15", "lodash-es": "^4.17.21", "memoize-one": "^5.1.1", "moment": "^2.29.4", "prop-types": "^15.8.1", "raf-schd": "^4.0.3", "rc-field-form": "~1.38.2", "rc-menu": "~9.16.0", "rc-motion": "^2.9.5", "rc-progress": "~3.2.1", "rc-table7481": "npm:rc-table@7.48.1", "rc-tree": "^5.13.1", "rc-util": "^5.32.2", "rc-virtual-list": "^3.14.2", "react-click-outside": "^3.0.1", "react-drag-listview": "^2.0.0", "react-fast-compare": "^2.0.4", "react-is": "^18.2.0", "react-lifecycles-compat": "^3.0.4", "react-popper": "^2.3.0", "react-slick": "^0.30.2", "react-transition-group": "^2.5.3", "react-window": "^1.8.8", "resize-observer-polyfill": "^1.5.1", "scroll-into-view-if-needed": "^2.2.31", "warning": "^4.0.3"}, "peerDependencies": {"react": ">=16.13.1", "react-dom": ">=16.13.1"}, "sideEffects": ["dist/*", "*.css"], "resolutions": {"@types/react": "^16.9.15"}}